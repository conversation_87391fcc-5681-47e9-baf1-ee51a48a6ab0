<?php

namespace App;

use App\Exceptions\CrmException;
use App\Models\OffDay;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class PublicHoliday extends Model
{
    use SoftDeletes;

    protected $guard_name = 'api';

    protected $table = 'public_holidays';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'line_id',
        'from_date',
        'to_date'
    ];

    protected $casts = [
        'from_date' => 'datetime',
        'to_date' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function line()
    {
        return $this->belongsTo(Line::class);
    }

    public static function calendar()
    {
        $color = "orange";
        // throw new CrmException(DB::table('public_holidays')->select('name', 'from_date', 'to_date')
        //     ->whereNull('deleted_at')->get());
        return DB::table('public_holidays')->select('name', 'from_date', 'to_date')
            ->whereNull('deleted_at')->get()->map(function ($item) use ($color) {
                return [
                    'color' => $color,
                    'start' => Carbon::parse($item->from_date)->startOfDay()->toDateTimeString(),
                    'end' => Carbon::parse($item->to_date)->endOfDay()->toDateTimeString(),
                    'name' => $item->name,
                    'timed' => true
                ];
            });
    }

    static public function isPublicHoliday($from, $to, $date, $lineId = null)
    {
        $month = [Carbon::parse($from)->format('m'), Carbon::parse($to)->format('m')];
        $year = Carbon::parse($from)->format('Y');
        $publicHolidays = PublicHoliday::where(fn($q) => $q->whereBetween(DB::raw("(DATE_FORMAT(crm_public_holidays.from_date,'%m'))"), $month)
            ->orWhereBetween(DB::raw("(DATE_FORMAT(crm_public_holidays.from_date,'%m'))"), [Carbon::now()->startOfYear()->format('m'), Carbon::now()->addMonth(1)->format('m')]))
            ->whereYear('public_holidays.from_date', $year)->whereYear('public_holidays.to_date', $year)
            ->where(fn($q) => $q->whereNull('line_id')->orWhere('line_id', $lineId))->get();
        $period = [];
        $count = 0;
        foreach ($publicHolidays as $publicHoliday) {
            $period = array_merge($period, CarbonPeriod::create($publicHoliday->from_date, $publicHoliday->to_date)->toArray());
        }
        foreach ($period as $value) {
            if (Carbon::parse($value)->toDateString() == Carbon::parse($date)->toDateString()) {
                $count = 1;
                break;
            }
        }
        if ($count == 1) return true;
        else return false;
    }


    public static function getTotalHolidayDays(Carbon $start, Carbon $end, $shiftId, $lineId = null): int
    {
        // Fetch public holidays that overlap with the specified date range
        $holidays = PublicHoliday::where(function ($query) use ($start, $end) {
            $query
                ->whereBetween("from_date", [$start, $end])
                ->orWhereBetween("to_date", [$start, $end])
                ->orWhere(function ($query) use ($start, $end) {
                    $query->where("from_date", "<=", $start)->where("to_date", ">=", $end);
                });
        })->where(fn($q) => $q->whereNull('line_id')->orWhere('line_id', $lineId))->get();

        // Calculate the total number of holiday days within the specified range
        $totalDays = 0;

        foreach ($holidays as $holiday) {
            // Determine the start and end dates for the overlap period
            $holidayStart = $holiday->from_date->greaterThan($start)
                ? $holiday->from_date
                : $start;
            $holidayEnd = $holiday->to_date->lessThan($end) ? $holiday->to_date : $end;

            // Add the number of days in the overlap period to the total
            $totalDays += static::countDaysExcludingOffDays($holidayStart, $holidayEnd, $shiftId);
        }

        return $totalDays;
    }

    public static function countDaysExcludingOffDays(Carbon $startDate, Carbon $endDate, $shiftId): int
    {
        $count = 0;

        $period = CarbonPeriod::create($startDate, $endDate);

        $offDays = OffDay::where("shift_id", $shiftId)
            ->pluck("day")
            ->map(function ($day) {
                $const = "Illuminate\Support\Carbon::" . strtoupper($day);

                return constant($const);
            })
            ->toArray();

        foreach ($period as $date) {
            if (!in_array($date->dayOfWeek, $offDays)) {
                $count++;
            }
        }

        return $count;
    }
}
