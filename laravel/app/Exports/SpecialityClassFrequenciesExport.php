<?php

namespace App\Exports;

use App\Exceptions\CrmException;
use App\ClassFrequency;
use App\Models\SpecialityClassFrequency;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromCollection;

class SpecialityClassFrequenciesExport implements FromQuery,  WithMapping, WithHeadings
{

    public function headings(): array
    {
        return [
            'id',
            'line',
            'speciality',
            'class',
            'frequency',
            'date',
        ];
    }

    public function map($speciality_class_frequency): array
    {

        return [
            'id' => $speciality_class_frequency->id,
            'line' => $speciality_class_frequency->line->name,
            'speciality' => $speciality_class_frequency->speciality->name,
            'class' => $speciality_class_frequency->class->name,
            'frequency' => $speciality_class_frequency->frequency,
            'date' => $speciality_class_frequency->date,
        ];
    }


    public function query()
    {

        return SpecialityClassFrequency::query();
    }
}
