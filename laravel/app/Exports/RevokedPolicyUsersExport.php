<?php

namespace App\Exports;

use App\Line;
use App\Services\Enums\AlertTypeReport;
use App\Shift;
use App\LineDivision;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;

class RevokedPolicyUsersExport implements FromQuery, WithHeadings
{

    public function __construct(private readonly array $ids, private readonly AlertTypeReport $alertType)
    {
    }

    public function headings(): array
    {
        return [
            'Id',
            'Full Name',
            'Emp Code',
            'Role',
            'Shift',
            'Line',
            'Division',
        ];
    }

    public function query()
    {
        return DB::table("users")
            ->select([
                "users.id as Id",
                "users.fullname as Full Name",
                "users.emp_code as Emp Code",
                "users.menuroles as Role",
                "Shift" => fn($query) => $query->selectSub(
                    Shift::query()
                        ->select('name')
                        ->where('id', $this->alertType->getShift()),
                    "Shift"
                ),
                "Line" => fn($query) => $query->selectSub(
                    Line::query()
                        ->select("lines.name")
                        ->join("line_users", "lines.id", "=", "line_users.line_id")
                        ->whereColumn("line_users.user_id", "users.id")
                        ->where("lines.from_date", "<=", (string)now())
                        ->where(
                            fn($q) => $q
                                ->where("lines.to_date", ">=", (string)now())
                                ->orWhere("lines.to_date", "=", null)
                        )
                        ->where("line_users.from_date", "<=", (string)now())
                        ->where(
                            fn($q) => $q
                                ->where("line_users.to_date", ">=", (string)now())
                                ->orWhere("line_users.to_date", "=", null)
                        )
                        ->limit(1),
                    "Line"
                ),
                "Division" => fn($query) => $query->selectSub(
                    LineDivision::query()
                        ->select("line_divisions.name")
                        ->join(
                            "line_users_divisions",
                            "line_divisions.id",
                            "=",
                            "line_users_divisions.line_division_id"
                        )
                        ->whereColumn("line_users_divisions.user_id", "users.id")
                        ->where("line_divisions.from_date", "<=", (string)now())
                        ->where(
                            fn($q) => $q
                                ->where("line_divisions.to_date", "=", null)
                                ->orWhere("line_divisions.to_date", ">=", (string)now())
                        )
                        ->where("line_users_divisions.from_date", "<=", (string)now())
                        ->where(
                            fn($q) => $q
                                ->where("line_users_divisions.to_date", ">=", (string)now())
                                ->orWhere("line_users_divisions.to_date", "=", null)
                        )
                        ->limit(1),
                    "Division"
                )
            ])
            ->whereIn('users.id', $this->ids)
            ->orderBy('users.id');
    }
}
