<?php

namespace App\Exports;

use App\Account;
use App\Exceptions\CrmException;
use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\Exportable;

class AccountsExport implements FromView
{
    use Exportable;
    /**
     * @return \Illuminate\Support\Collection
     */
    public function view(): View
    {
        // throw new CrmException(Account::select(
        //     'accounts.id as id',
        //     'accounts.name as name',
        //     // 'account_types.name as type',
        //     'accounts.code as code',
        //     'accounts.notes as notes',
        //     'accounts.address as address',
        //     'accounts.tel as tel',
        //     'accounts.mobile as mobile',
        //     'accounts.email as email',
        //     'accounts.active_date as active_date',
        //     'accounts.inactive_date as inactive_date',
        // )
        //     ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
        //     // with([
        //     // 'type',
        //     // 'accountlines',
        //     // 'accountdoctors'
        //     // ])
        //     ->get());
        return view('accounts.export', [
            // 'accounts' => Account::with(['type','sub_type','accountlines','accountdoctors'])->get(),
            'accounts' => Account::select(
                'accounts.id as id',
                'accounts.name as name',
                'account_types.name as type',
                'accounts.code as code',
                'accounts.notes as notes',
                'accounts.address as address',
                'accounts.tel as tel',
                'accounts.mobile as mobile',
                'accounts.email as email',
                'accounts.active_date as active_date',
                'accounts.inactive_date as inactive_date',
            )
                ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
                // with([
                // 'type',
                // 'accountlines',
                // 'accountdoctors'
                // ])
                ->get()
        ]);
    }
}
