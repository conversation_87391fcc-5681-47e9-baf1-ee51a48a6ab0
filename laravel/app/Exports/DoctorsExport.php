<?php

namespace App\Exports;

use App\Doctor;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;

class DoctorsExport implements FromQuery, WithHeadings
{
    public function query()
    {
        return Doctor::query()->select(
            'doctors.id',
            'doctors.ucode',
            'doctors.name as doctor',
            'doctors.tel',
            'doctors.mobile',
            'doctors.email',
            'doctors.dob',
            'doctors.dom',
            'doctors.active_date',
            'doctors.inactive_date',
            'specialities.name as speciality',
            DB::raw('IFNULL( crm_accounts.name,"") as name'),
            DB::raw('IFNULL( crm_accounts.code,"") as acc_code'),
            DB::raw('IFNULL(crm_account_types.name,"") as account_type'),
            DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
            DB::raw('IFNULL(group_concat(distinct crm_line_divisions.name),"") as division'),
            DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
            'new_account_doctors.from_date as account_from_date',
            'new_account_doctors.to_date as account_to_date',
        )
            ->leftJoin('new_account_doctors', 'doctors.id', 'new_account_doctors.doctor_id')
            ->leftJoin('accounts', 'new_account_doctors.account_id', 'accounts.id')
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
            ->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('classes as d', 'doctors.class_id', 'd.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('line_users_divisions', 'account_lines.line_division_id', 'line_users_divisions.line_division_id')
            ->leftJoin('specialities', 'doctors.speciality_id', '=', 'specialities.id')
            ->leftJoin('specialities AS c', function ($join) {
                $join->on('doctors.sub_speciality_id', '=', 'c.id');
            })
            ->where('doctors.deleted_at', '=', null)
            ->groupBy(
                'doctors.id',
                'accounts.id',
                'account_types.id',
                'new_account_doctors.id'
            );
    }

    public function headings(): array
    {
        return [
            'id',
            'ucode',
            'doctor',
            'tel',
            'mobile',
            'email',
            'dob',
            'dom',
            'active_date',
            'inactive_date',
            'speciality',
            'name',
            'acc_code',
            'account_type',
            'line',
            'division',
            'brick',
            'account_from_date',
            'account_to_date',
        ];
    }
}
