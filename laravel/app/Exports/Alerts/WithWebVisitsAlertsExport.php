<?php

namespace App\Exports\Alerts;

use App\Exceptions\CrmException;
use App\Models\Alert;
use App\Http\Controllers\AlertController;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Style;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\WithStyles;




class WithWebVisitsAlertsExport implements  WithHeadings, FromArray, WithColumnWidths, ShouldAutoSize, WithStyles
{
    protected $alerts;

    public function __construct(array $alerts)
    {
        // throw new CrmException($alerts);
        $propertiesToExtract = ['id', 'fullname', 'user_name', 'emp_code', 'role', 'lines', 'divisions',];
        $printedAlert = array_map(function ($item) use ($propertiesToExtract) {
            return array_intersect_key($item, array_flip($propertiesToExtract));
        }, $alerts);


        $this->alerts = $printedAlert;
    }


    public function array(): array
    {
        // Log::info('$this->alerts');
        // Log::info($this->alerts);

        return $this->alerts;
    }

    public function headings(): array
    {
        return [
            'Id',
            'Fullname',
            'Name',
            'Emp_code',
            'Role',
            'Line',
            'Divisions',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        
        // $styleArray = [
        //     'borders' => [
        //         'bottom' => ['borderStyle' => 'hair', 'color' => ['argb' => 'FFFF0000']],
        //         'top' => ['borderStyle' => 'hair', 'color' => ['argb' => 'FFFF0000']],
        //         'right' => ['borderStyle' => 'hair', 'color' => ['argb' => 'FF00FF00']],
        //         'left' => ['borderStyle' => 'hair', 'color' => ['argb' => 'FF00FF00']],
        //     ],
        // ];

        // $sheet->getStyle('B2:C3')->applyFromArray($styleArray);

        return [
            'A' => [
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                ],
            ],
            1    => ['font' => ['bold' => true]],
            // 'A'    => ['alignment' => ['indent' => true]],

            
        ];

        // $sheet->getStyle('A1')->getFont()->setBold(true);
    }


    public function columnWidths(): array
    {
        return [
            'A' => 15,
            'B' => 25,            
            'C' => 25,            
            'D' => 20,            
            // 'E' => 10,            
            'F' => 15,            
            'G' => 20,            
            'H' => 15,            
        ];
    }

    

   
}
