<?php

namespace App\Exports;

use App\Level;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class LevelsExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return Level::get([
            'id',
            'name',
            'notes',
            'sort',
        ]);
    }

    public function headings(): array
    {
        return [
            'id',
            'name',
            'notes',
            'sort',
        ];
    }
}
