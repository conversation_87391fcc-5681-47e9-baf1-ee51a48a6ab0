<?php

namespace App\Exports;

use App\Pricetype;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;


class PricetypesExport implements FromCollection,WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return Pricetype::get([
            'id',
            'name',
            'notes',
            'sort',
        ]);
    }

    public function headings(): array
    {
        return [
            'id',
            'name',
            'notes',
            'sort',
        ];
    }
}
