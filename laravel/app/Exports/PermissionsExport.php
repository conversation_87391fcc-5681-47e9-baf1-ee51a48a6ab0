<?php

namespace App\Exports;

use Spatie\Permission\Models\Permission;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class PermissionsExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return Permission::get([
            'id',
            'name',
            'guard_name',
            'form_id',
            'action_id',
        ]);
    }

    public function headings(): array
    {
        return [
            'id',
            'name',
            'guard_name',
            'form_id',
            'action_id',
        ];
    }
}
