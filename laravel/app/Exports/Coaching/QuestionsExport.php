<?php

namespace App\Exports\Coaching;

use App\Exceptions\CrmException;
use App\Models\Coaching\Question;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class QuestionsExport implements FromQuery,  WithMapping, WithHeadings
{


    public function headings(): array
    {
        return [
            'id',
            'name',
            'notes',
            'sort',
            'answers',
            'categories',
        ];
    }

    public function map($question): array
    {

        return [
            $question->id,
            $question->name,
            $question->notes,
            $question->sort,
            $question->answers->pluck('name')->implode(', '),
            $question->categories->pluck('name')->implode(', '),
        ];
    }


    public function query()
    {

        return Question::query();
    }

}
