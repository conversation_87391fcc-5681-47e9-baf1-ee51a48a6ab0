<?php

namespace App\Exports\Coaching;

use App\Models\Coaching\Answer;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class AnswersExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return Answer::get([
            'id',
            'name',
            'notes',
            'weight',
            'question_id',
        ]);
    }

    public function headings(): array
    {
        return [
            'id',
            'name',
            'notes',
            'weight',
            'question_id',
        ];
    }
}
