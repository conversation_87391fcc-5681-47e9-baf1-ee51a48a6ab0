<?php

namespace App\Exports;

use App\Distributor;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class DistributorsExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return Distributor::get([
            'id',
            'name',
            'notes',
            'sort',
        ]);
    }

    public function headings(): array
    {
        return [
            'id',
            'name',
            'notes',
            'sort',
        ];
    }

}
