<?php

namespace App\Exports;

use App\Brick;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class BricksExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return Brick::get([
            'id',
            'name',
            'notes',
            'sort',
        ]);
    }

    public function headings(): array
    {
        return [
            'id',
            'name',
            'notes',
            'sort',
        ];
    }
}
