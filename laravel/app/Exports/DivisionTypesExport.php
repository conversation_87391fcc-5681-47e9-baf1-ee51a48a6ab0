<?php

namespace App\Exports;

use App\DivisionType;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;


class DivisionTypesExport implements FromQuery, WithHeadings
{

    public function query()
    {
        return DivisionType::query()->select('division_types.id', 'division_types.name', 'division_types.sort', 'division_types.notes','c.name as parent')
        ->leftJoin('division_types AS c', function($join){
            $join->on('division_types.parent_id', '=', 'c.id');
        })
        ->where('division_types.deleted_at', '=', null);
    }

    public function headings(): array
    {
        return [
            'id',
            'name',
            'sort',
            'notes',
            'parent',
        ];
    }
}
