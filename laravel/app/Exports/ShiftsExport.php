<?php

namespace App\Exports;

use App\Shift;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ShiftsExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return Shift::get([
            'id',
            'name',
            'notes',
            'sort',
        ]);
    }

    public function headings(): array
    {
        return [
            'id',
            'name',
            'notes',
            'sort',
        ];
    }
}
