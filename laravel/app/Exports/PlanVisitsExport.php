<?php

namespace App\Exports;

use App\Exceptions\CrmException;
use App\Family;
use App\PlanVisit;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class PlanVisitsExport implements FromCollection, WithHeadings
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        // Get one reason per visit
        $reasonSubquery = DB::table('reasonables')
            ->select('reasonable_id', DB::raw('MAX(reason_id) as reason_id'))
            ->where('reasonable_type', PlanVisit::class)
            ->groupBy('reasonable_id');

        // Start building the query with specific columns and aliases directly in the query
        $query = PlanVisit::select([
            'planned_visits.id',
            'lines.name as line',
            'line_divisions.name as division',
            'users.fullname as user',
            'accounts.name as account',
            DB::raw('IFNULL(crm_doctors.name, "") as doctor'),
            DB::raw('IFNULL(crm_specialities.name, "") as speciality'),
            'account_types.name as acc_type',
            'visit_types.name as type',
            DB::raw('IFNULL(crm_shifts.name, "") as shift'),
            'planned_visits.visit_date as date',
            DB::raw('CASE
                WHEN crm_plan_visit_details.approval = 0 THEN "Disapproved"
                WHEN crm_plan_visit_details.approval IS NOT NULL THEN "Approved"
                ELSE "Pending"
            END as status'),
            DB::raw('IFNULL(crm_reasons.reason, "") as reason')
        ]);

        // Apply user permissions filter efficiently
        if (!Auth::user()->hasRole('admin')) {
            $userIds = auth()->user()->allBelowUsers()->prepend(auth()->user())->pluck('id')->toArray();
            $query->whereIn('planned_visits.user_id', $userIds);
        }

        // Use join instead of with() for better performance on large datasets
        $query->join('lines', 'planned_visits.line_id', '=', 'lines.id')
            ->join('line_divisions', 'planned_visits.div_id', '=', 'line_divisions.id')
            ->join('users', 'planned_visits.user_id', '=', 'users.id')
            ->join('accounts', 'planned_visits.account_id', '=', 'accounts.id')
            ->join('account_types', 'accounts.type_id', '=', 'account_types.id')
            ->join('visit_types', 'planned_visits.visit_type', '=', 'visit_types.id')
            ->leftJoin('doctors', 'planned_visits.account_dr_id', '=', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', '=', 'specialities.id')
            ->leftJoin('shifts', 'planned_visits.shift_id', '=', 'shifts.id')
            ->leftJoin('plan_visit_details', function ($join) {
                $join->on('planned_visits.id', '=', 'plan_visit_details.visitable_id')
                    ->where('plan_visit_details.visitable_type', PlanVisit::class);
            })
            ->leftJoin(DB::raw("({$reasonSubquery->toSql()}) as crm_reason_subquery"),
                'planned_visits.id', '=', 'reason_subquery.reasonable_id')
            ->leftJoin('reasons', 'reason_subquery.reason_id', '=', 'reasons.id')
            ->mergeBindings($reasonSubquery);

        // Limit and execute - now returns data directly with the correct column names
        return $query->limit(1000)->get();
    }

    public function headings(): array
    {
        return [
            'id',
            'line',
            'division',
            'user',
            'account',
            'doctor',
            'speciality',
            'acc_type',
            'type',
            'shift',
            'date',
            'status',
            'reason',
        ];
    }
}
