<?php

namespace App;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class RequestType extends Model
{
    use SoftDeletes;

    protected $guard_name = 'api';

    protected $table = 'request_types';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['name','value', 'with_deduct', 'sort'];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }
}
