<?php

namespace App\Models;

use App\Line;
use App\LineDivision;
use App\Product;
use App\Traits\ModelImportable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Budget extends Model
{
    use SoftDeletes;
    use ModelImportable;
    protected $guard_name = 'api';

    protected $table = 'budgets';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'file_id',
        'div_id',
        'line_id',
        'budgetable_id',
        'budgetable_type',
        'product_id',
        'from_date',
        'to_date',
        'amount',
        'type_id'
    ];

    protected $casts = [
        'from_date' => 'datetime',
        'to_date' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function line()
    {
        return $this->belongsTo(Line::class, 'line_id');
    }

    public function type()
    {
        return $this->belongsTo(BudgetType::class, 'type_id');
    }
    public function division()
    {
        return $this->belongsTo(LineDivision::class, 'div_id');
    }
    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }
}
