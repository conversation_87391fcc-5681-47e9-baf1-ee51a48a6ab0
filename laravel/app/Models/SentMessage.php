<?php

namespace App\Models;

use App\User;
use Illuminate\Database\Eloquent\Relations\Pivot;

class SentMessage extends Pivot
{
    protected  $fillable = ["message_id", "user_id"];
    protected  $table = "sent_message";
    public $incrementing = true;

    public function message()
    {
        return $this->belongsTo(Message::class, "message_id");
    }

    public function user()
    {
        return $this->belongsTo(User::class, "user_id");
    }
}
