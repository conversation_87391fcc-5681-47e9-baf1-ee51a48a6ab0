<?php

namespace App\Models\EDetailing;

use App\Line;
use App\Product;
use App\Traits\ModelImportable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductPresentation extends Model
{
    use SoftDeletes;
    use ModelImportable;

    protected $guard_name = 'api';

    protected $table = 'product_presentations';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'product_id' , 'line_id' , 'message_id' , 'presentation_id' , 'file_id'
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function line(): BelongsTo
    {
        return $this->belongsTo(Line::class);
    }

    public function presentation(): BelongsTo
    {
        return $this->belongsTo(Presentation::class);
    }

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }
}
