<?php

namespace App\Models\EDetailing;

use App\Models\Attachment;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class Thumbnail extends Model
{
    use SoftDeletes;

    protected $guard_name = 'api';

    protected $table = 'thumbnails';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'attachment_id',
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function attachment()
    {
        return $this->morphOne(Attachment::class, "attachable");
    }

    public function slide(): HasOne
    {
        return $this->hasOne(PresentationSlide::class, 'thumbnail_id');
    }
}
