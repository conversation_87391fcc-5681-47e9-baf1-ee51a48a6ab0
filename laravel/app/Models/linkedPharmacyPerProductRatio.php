<?php

namespace App\Models;

use App\Mapping;
use App\Product;
use App\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class linkedPharmacyPerProductRatio extends Model
{
    use HasFactory;
    protected $guard_name = 'api';
    protected $table = 'linked_pharmacy_per_product_ratios';
    protected $fillable = [
        'pharmacy_id',
        'product_id',
        'user_id',
        'account_id',
        'ratio'
    ];
    public function pharmacy()
    {
        return $this->belongsTo(Mapping::class, 'pharmacy_id');
    }
    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function account()
    {
        return $this->belongsTo(Product::class, 'account_id');
    }
}
