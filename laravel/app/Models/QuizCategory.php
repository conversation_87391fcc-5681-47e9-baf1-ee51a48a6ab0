<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


class QuizCategory extends Model
{
    use SoftDeletes;

    protected $guard_name = 'api';

    protected $table = 'quiz_categories';

    protected $fillable = [
        'name', 'sort' 
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }
    public function quizQuestions()
    {
        return $this->hasMany(QuizQuestion::class);
    }
}
