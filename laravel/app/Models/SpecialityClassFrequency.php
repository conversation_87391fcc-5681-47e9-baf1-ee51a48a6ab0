<?php

namespace App\Models;

use App\Classes;
use App\Line;
use App\Speciality;
use App\Traits\ModelExportable;
use App\Traits\ModelImportable;
use App\Traits\PdfExportable;
use App\Traits\SendMail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SpecialityClassFrequency extends Model
{
    use HasFactory;
    use ModelImportable;
    use ModelExportable;
    use PdfExportable;
    use SendMail;
    protected $guard_name = 'api';

    protected $table = 'speciality_class_frequencies';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'speciality_id',
        'class_id',
        'line_id',
        'file_id',
        'date',
        'frequency',
        'year',
        'month',
    ];
    protected $casts = [
        'date' => 'datetime',
        'deleted_at' => 'datetime',
        'year' => 'integer',
        'month' => 'integer',
    ];

    /**
     * Boot method to automatically set year and month when date is set
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if ($model->date) {
                $model->year = $model->date->year;
                $model->month = $model->date->month;
            }
        });

        static::updating(function ($model) {
            if ($model->isDirty('date') && $model->date) {
                $model->year = $model->date->year;
                $model->month = $model->date->month;
            }
        });
    }

    public function Line(): BelongsTo
    {
        return $this->belongsTo(Line::class, 'line_id');
    }
    public function class(): BelongsTo
    {
        return $this->belongsTo(Classes::class, 'class_id');
    }

    public function speciality(): BelongsTo
    {
        return $this->belongsTo(Speciality::class, 'speciality_id');
    }
}
