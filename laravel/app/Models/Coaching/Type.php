<?php

namespace App\Models\Coaching;

use App\Line;
use App\Traits\ModelExportable;
use App\Traits\ModelImportable;
use App\Traits\PdfExportable;
use App\Traits\SendMail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Type extends Model
{
    use SoftDeletes;
    use ModelImportable;
    use ModelExportable;
    use PdfExportable;
    use SendMail;

    protected $guard_name = 'api';

    protected $table = 'coaching_types';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name', 'line_id' , 'notes', 'sort', 'file_id',
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the coachingCategories for the coachingType.
     */
    public function categories()
    {
        return $this->hasMany(Category::class);
    }
    public function line()
    {
        return $this->belongsTo(Line::class);
    }

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

}
