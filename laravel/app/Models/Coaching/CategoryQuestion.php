<?php

namespace App\Models\Coaching;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

use App\Traits\ModelExportable;
use App\Traits\ModelImportable;
use App\Traits\PdfExportable;
use App\Traits\SendMail;

class CategoryQuestion extends Model
{
    use SoftDeletes;
    use ModelImportable;
    use ModelExportable;
    use PdfExportable;
    use SendMail;

    protected $guard_name = 'api';

    protected $table = 'coaching_category_question';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'category_id', 'question_id', 'file_id',
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function question(){
        return $this->belongsTo(Question::class, 'question_id');
    }

    public function category(){
        return $this->belongsTo(Category::class, 'category_id');
    }
}
