<?php

namespace App\Models;

use App\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ActualVisitError extends Model
{
    use HasFactory;
    protected $table = 'actual_visit_errors';

    protected $fillable = [
        'plan_id',
        'user_id',
        'line_id',
        'div_id',
        'brick_id',
        'acc_type_id',
        'end_visit_date',
        'account_id',
        'account_dr_id',
        'visit_type_id',
        'visit_date',
        'end_visit_time',
        'll',
        'lg',
        'll_start',
        'lg_end',
        'visit_duration',
        'invalid_duration',
        'visit_deviation',
        'visit_address',
        'is_automatic',
        'is_web_visit',
        'failures',
        'visit_status',
        'is_fake_gps',
        'os_version',
        'device_brand',
        'os_type'
    ];
    protected $casts = [
        'failures' => 'json',
    ];
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
