<?php

namespace App\Models;

use App\Traits\ModelExportable;
use App\Traits\ModelImportable;
use App\Traits\PdfExportable;
use App\Traits\SendMail;
use App\User;
use App\VacationType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VacationBalance extends Model
{
    use HasFactory;
    use ModelImportable;
    use ModelExportable;
    use PdfExportable;
    use SendMail;

    protected $guard_name = 'api';
    protected $table = 'vacation_balances';

    protected $fillable = ['user_id', 'balance', 'vacation_type_id', 'from_date', 'to_date', 'file_id'];


    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function vacationType()
    {
        return $this->belongsTo(VacationType::class, 'vacation_type_id');
    }
}
