<?php

namespace App\Models;

use App\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class Message extends Model
{
    use SoftDeletes;

    protected $fillable = ["from", "subject", "body", "read", "stared", "important"];
//    protected $appends = [
//        "important",
//        "read",
//        "favorite",
//        "trashed"
//    ];


    public function sender(): BelongsTo
    {
        return $this->belongsTo(User::class, "from", "id");
    }

    public function carbonCopies(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'carbon_copy')->using(CarbonCopy::class);
    }

    public function blindCarbonCopies(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'blind_carbon_copy')->using(BlindCarbonCopy::class);
    }

    public function attachments(): MorphMany
    {
        return $this->morphMany(Attachment::class, "attachable");
    }

    public function userTrashed(): BelongsToMany
    {
        return $this->belongsToMany(User::class, "trash_message")->using(TrashMessage::class);
    }

    public function userSent(): BelongsToMany
    {
        return $this->belongsToMany(User::class, "sent_message")->using(SentMessage::class);
    }

    public function userRead(): BelongsToMany
    {
        return $this->belongsToMany(User::class, "read_message")->using(ReadMessage::class);
    }

    public function userImportant(): BelongsToMany
    {
        return $this->belongsToMany(User::class, "important_message")->using(ImportantMessage::class);
    }

    public function userFavorite(): BelongsToMany
    {
        return $this->belongsToMany(User::class, "favorite_message")->using(FavoriteMessage::class);
    }

    public function toUsers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'message_user')->using(MessageUser::class);
    }

    public function markAsImportant(?User $user = null): static
    {
        /** @var User $user */
        $user = $user ?? auth()->user();
        if (!$this->isImportant($user))
            $user->importantMessages()->attach($this->id);
        return $this;
    }

    public function markAsRead(?User $user = null): static
    {
        /** @var User $user */
        $user = $user ?? auth()->user();
        if (!$this->isRead($user))
            $user->readMessages()->attach($this->id);
        return $this;
    }

    public function markAsSent(?User $user = null): static
    {
        /** @var User $user */
        $user = $user ?? auth()->user();
        if (!$this->isSent($user)) {
            $user->sentMessages()->attach($this->id);
        }
        return $this;
    }

    public function markAsTrashed(?User $user = null): static
    {
        /** @var User $user */
        $user = $user ?? auth()->user();
        if (!$this->isTrashed($user)) {
            $user->trashMessages()->attach($this->id);
        }
        return $this;
    }

    public function markAsFavorite(?User $user = null)
    {
        /** @var User $user */
        $user = $user ?? auth()->user();
        if (!$this->isFavorite($user))
            $user->favoriteMessages()->attach($this->id);
        return $this;
    }

    public function markAsToUser(?User $user = null)
    {
        /** @var User $user */
        $user = $user ?? auth()->user();
        if (!$this->isToUser($user))
            $user->messageReceived()->attach($this->id);
        return $this;
    }

    public function getImportantAttribute()
    {
        return $this->isImportant();
    }

    public function getFavoriteAttribute()
    {
        return $this->isImportant();
    }

    public function getReadAttribute()
    {
        return $this->isRead();
    }

    public function getTrashedAttribute()
    {
        return $this->isTrashed();
    }

    public function getSentAttribute()
    {
        return $this->isSent();
    }

    public function isImportant(?User $user = null): bool
    {
        /** @var User $user */
        $user = $user ?? auth()->user();
        return $user->importantMessages()->wherePivot('message_id', $this->id)->exists();
    }

    public function isFavorite(?User $user = null): bool
    {
        /** @var User $user */
        $user = $user ?? auth()->user();
        return $user->favoriteMessages()->wherePivot('message_id', $this->id)->exists();
    }

    public function isRead(?User $user = null): bool
    {
        /** @var User $user */
        $user = $user ?? auth()->user();
        return $user->readMessages()->wherePivot('message_id', $this->id)->exists();
    }

    public function isTrashed(?User $user = null): bool
    {
        /** @var User $user */
        $user = $user ?? auth()->user();
        return $user->trashMessages()->wherePivot('message_id', $this->id)->exists();
    }

    public function isSent(?User $user = null)
    {
        /** @var User $user */
        $user = $user ?? auth()->user();
        return $user->sentMessages()->wherePivot('message_id', $this->id)->exists();
    }

    public function isToUser(?User $user = null)
    {
        /** @var User $user */
        $user = $user ?? auth()->user();
        return $user->toUsers()->wherePivot('message_id', $this->id)->exists();
    }

    public function markAs(array|string $values): static
    {
        Log::info($values);
        match (gettype($values)) {
            "string" => (function ($values) {
                $method = Str::camel('markAs_' . $values);
                $this->$method();
            })($values),
            "array" => (function ($values) {
                foreach ($values as $key => $value) {
                    $checkMethod = Str::camel('is_' . $key);
                    $trashMethod = Str::camel('markAs_' . $key);
                    if (!$this->$checkMethod()) $this->$trashMethod();
                }
            })($values),
        };


        return $this;
    }

    // public function scopeWhereTrashed($query){
    //     return $query->where
    // }
}
