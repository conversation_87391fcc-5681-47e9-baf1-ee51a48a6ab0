<?php

namespace App\Models;

use App\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuizResult extends Model
{
    protected $table = 'quiz_result';

    protected $fillable = [
        'quiz_id', 'user_id', 'time_consumed', 'result', 'question_id', 'answer_id'
    ];
    public function question()
    {
        return $this->belongsTo(QuizQuestion::class);
    }
    public function quiz()
    {
        return $this->belongsTo(Quiz::class);
    }
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function answer()
    {
        return $this->belongsTo(QuizAnswer::class);
    }
    
}
