<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BudgetSetting extends Model
{
    use SoftDeletes;

    protected $guard_name = 'api';

    protected $table = 'budget_settings';
    
    protected $fillable = ['name','key','value','type'];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }
}
