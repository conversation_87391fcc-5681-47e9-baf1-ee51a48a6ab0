<?php

namespace App\Models;

use App\Mapping;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class SaleFactorType extends Pivot
{

    protected $table="sale_factor_types";

    protected $fillable=[
        "id","mapping_id","sale_factor_id","from_date","to_date"
    ];

    public function mapping() : BelongsTo
    {
        return $this->belongsTo(Mapping::class);
    }

    public function saleFactor() : BelongsTo
    {
        return $this->belongsTo(SaleFactor::class);
    }

}
