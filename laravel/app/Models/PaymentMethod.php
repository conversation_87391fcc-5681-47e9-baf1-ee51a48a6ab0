<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PaymentMethod extends Model
{
    protected $guard_name = 'api';

    protected $table = 'payment_methods';

    protected $fillable = ['name', 'with_users', 'sort'];

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'categories_payments', 'payment_method_id', 'category_id');
    }
}
