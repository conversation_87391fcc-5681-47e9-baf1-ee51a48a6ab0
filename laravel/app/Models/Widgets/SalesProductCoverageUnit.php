<?php

namespace App\Models\Widgets;

use App\Interfaces\Widgets\ActionExecutionInterface;
use App\Interfaces\Widgets\FetchableInterface;
use App\Product;
use App\Sale;
use App\Scopes\Widgets\SalesProductCoverageUnitScope;
use App\Traits\Widgets\BootableWidget;
use App\Traits\Widgets\Fetchable;
use App\Traits\Widgets\SettingsAccessAndAuthorizationAccess;
use App\Widget;
use Illuminate\Support\Collection;

class SalesProductCoverageUnit extends Widget implements FetchableInterface, ActionExecutionInterface
{
    use SettingsAccessAndAuthorizationAccess,Fetchable,BootableWidget;

    public function fetchWidgetData(Widget $widget): Collection
    {

        $products = Product::salesInMonth()->get()->map(function ($item) {
            return [$item->name => $item->sales->sum('quantity')];
        })->collapse();
        // $data =$products->merge(['Sales' => Sale::inMonth()->get()->sum('quantity')]);
        return Collect(data_fill($widget, 'data', $products));
    }

    public function execute(): void
    {
    }
}
