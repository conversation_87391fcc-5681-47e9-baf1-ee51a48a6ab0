<?php

namespace App\Models\Widgets;

use App\ActualVisit;
use App\Doctor;
use App\Interfaces\Widgets\FetchableInterface;
use App\Scopes\Widgets\DoctorCoverageScope;
use App\Traits\Widgets\BootableWidget;
use App\Traits\Widgets\Fetchable;
use App\Traits\Widgets\SettingsAccessAndAuthorizationAccess;
use App\Widget;
use Illuminate\Support\Collection;

class DoctorCoverage extends Widget implements FetchableInterface
{
    use SettingsAccessAndAuthorizationAccess,Fetchable,BootableWidget;
    public function fetchWidgetData(Widget $widget): Collection
    {

        $doctors = Doctor::getAccountIdsOfDoctorsInLineDivisionWithAuthUser();
        $data = ['Doctors' => count($doctors), 'Coverage' => count(ActualVisit::getActualVisitsOfAuthUserAgainstCollectionOfAccountIds($doctors)->unique('account_dr_id'))];

        return Collect(data_fill($widget, 'data', $data));
    }

    public function execute(): void
    {

    }
}
