<?php

namespace App\Models\Widgets;

use App\Exceptions\CrmException;
use App\Interfaces\Widgets\FetchableInterface;
use App\Models\Quiz as ModelsQuiz;
use App\Models\QuizSetting;
use App\Scopes\Widgets\QuizScope;
use App\Traits\Widgets\BootableWidget;
use App\Traits\Widgets\Fetchable;
use App\Traits\Widgets\SettingsAccessAndAuthorizationAccess;
use App\Widget;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class Quiz extends Widget implements FetchableInterface
{
    use SettingsAccessAndAuthorizationAccess, Fetchable, BootableWidget;
    public function fetchWidgetData(Widget $widget): Collection
    {
        /**@var User */
        $auth = Auth::user();
        $now = Carbon::now();
        $showAnswerSetting = QuizSetting::where('key', 'show_answers')->value('value') == 'Yes';

        $data = ModelsQuiz::where(function ($query) use ($now) {
            // $query->whereRaw("TIMESTAMP(start_date, start_time) <= ?", [$now])
            //     ->whereRaw("TIMESTAMP(end_date, end_time) >= ?", [$now]);
            $query->where('start_date', '<=', $now)
                ->where('end_date', '>=', $now);
        })
            ->whereHas('users', function ($q) use ($auth) {
                $q->where('users.id', $auth->id);
            })->get()->map(function ($quiz) use ($auth, $showAnswerSetting) {
                $exists = DB::table('quiz_result')->where('quiz_id', $quiz->id)->where('user_id', $auth->id)->exists();
                return [
                    'id' => $quiz->id,
                    'name' => $quiz->name,
                    'description' => $quiz->desc,
                    'time' => $quiz->time,
                    'line' => $quiz->lines()->whereIntegerInRaw('lines.id', $auth->lines()?->pluck('lines.id')->toArray())->first()?->name,
                    's_date' => $quiz->start_date,
                    'e_date' => $quiz->end_date ?? '',
                    'quiz_degree' => $quiz->quiz_degree,
                    'success_degree' => $quiz->success_degree,
                    'quizActions' => $this->quizData($quiz, $exists, $showAnswerSetting),
                ];
            });

        return Collect(data_fill($widget, 'data', collect($data) ?? []));
    }

    public function quizData($quiz, $exists, $showAnswerSetting)

    {
        return [
            [
                'id' => $quiz->id,
                'type' => 'text',
                'title' => 'Start Quiz',
                'transaction' => '',
                'color' => 'primary',
                'visible' =>  !$exists,
                'widget_type' => 'quiz'
            ],
            [
                'id' => $quiz->id,
                'type' => 'text',
                'title' => 'Show Answers',
                'transaction' => '',
                'color' => 'primary',
                'visible' =>  $exists && $showAnswerSetting,
                'widget_type' => 'quiz'
            ],
        ];
    }
}
