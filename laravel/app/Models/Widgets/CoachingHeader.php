<?php

namespace App\Models\Widgets;


use App\Interfaces\Widgets\FetchableInterface;
use App\Models\Coaching\CoachingHeader as ModelCoachingHeader;
use App\Scopes\Widgets\CoachingHeaderScope;
use App\Traits\Widgets\BootableWidget;
use App\Traits\Widgets\Fetchable;
use App\Traits\Widgets\SettingsAccessAndAuthorizationAccess;

use App\Widget;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class CoachingHeader extends Widget implements FetchableInterface
{
    use SettingsAccessAndAuthorizationAccess, Fetchable, BootableWidget;
    public function fetchWidgetData(Widget $widget): Collection
    {
        $auth = Auth::id();
        $data = ModelCoachingHeader::where('employee_id', $auth)->whereNull('approval')->get()->map(function ($coaching) {
            return [
                'id' => $coaching->id,
                'evaluator' => $coaching->evaluator->name,
                'date' => $coaching->date,
                'strenghts' => $coaching->strenghts,
                'points_of_improvement' => $coaching->points_of_improvement,
                'notes' => $coaching->notes,
                'approval' =>  $coaching->approval ? $coaching->approval != null : 'pending',
                'coachingAction' =>  $this->coachingAction($coaching),
            ];
        });
        // $data = ModelCoachingHeader::get();

        return Collect(data_fill($widget, 'data', collect($data) ?? []));
    }

    public function coachingAction($coaching)

    {
        return [
            [
                'id' => $coaching->id,
                'type' => 'text',
                'title' => 'Aknowledge',
                'transaction' => '',
                'color' => 'primary',
                'visible' =>  $coaching->id != '',
                'widget_type' => 'coaching_header'
            ],
        ];
    }
}
