<?php

namespace App\Models\Widgets;

use App\Interfaces\Widgets\FetchableInterface;
use App\Scopes\Widgets\AlertWithoutAMVisitsScope;
use App\Traits\Widgets\Fetchable;
use App\Traits\Widgets\SettingsAccessAndAuthorizationAccess;
use App\Widget;
use App\Models\Alert;
use App\ActualVisit;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\LineDivisionUser;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;
use App\Services\Alerts\AlertReportsService;
use Illuminate\Support\Facades\Auth;

class AlertWithoutAMVisits extends Widget implements FetchableInterface
{
    use SettingsAccessAndAuthorizationAccess, Fetchable;

    public function fetchWidgetData(Widget $widget): Collection
    {

        $id = Auth::user();
        $alert = Alert::select('*')
            ->leftJoin('alerts_users', 'alerts.id', 'alerts_users.alert_id')
            ->leftJoin('alerts_sending_methods', 'alerts.id', 'alerts_sending_methods.alert_id')
            ->where('alerts_sending_methods.by_id', 2) // 2 is for widget types
            ->where('alerts.type', 'Without AM Visits') // Report type
            ->where('alerts_users.user_id', $id)
            ->first();

        if ($alert) {
            $period = $alert->period;
            $time = $alert->time;
            $by = $alert->by_id;
            $alert_id = $alert->alert_id;
            $role = $alert->role_filter;

            $data = (new AlertReportsService())->withoutVisits($period,'AM', $time, $by, $alert_id, '', '', $id, $role);
            return Collect(data_fill($widget, 'data', $data));
        }

        return Collect(data_fill($widget, 'data', []));


    }


}

