<?php

namespace App\Models\Widgets;

use App\DivisionType;
use App\Exceptions\CrmException;
use App\Interfaces\Widgets\ActionExecutionInterface;
use App\Interfaces\Widgets\FetchableInterface;
use App\Sale;
use App\SaleDetail;
use App\Scopes\Widgets\SalesTargetCoverageUnitScope;
use App\Target;
use App\TargetDetails;
use App\Traits\Widgets\BootableWidget;
use App\Traits\Widgets\Fetchable;
use App\Traits\Widgets\SettingsAccessAndAuthorizationAccess;
use App\Widget;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class SalesTargetCoverageUnit extends Widget implements FetchableInterface, ActionExecutionInterface
{
    use SettingsAccessAndAuthorizationAccess, Fetchable, BootableWidget;

    public function fetchWidgetData(Widget $widget): Collection
    {
        $from = Carbon::now()->subMonth()->firstOfMonth();
        $to = Carbon::now()->subMonth()->lastOfMonth();
        /**@var User  */
        $user = Auth::user();
        $divisionType = DivisionType::where('last_level', 1)->value('id');
        $divisions = collect([]);
        $lines = $user->userLines();
        foreach ($lines as $line) {
            $divisions = $divisions->merge($user->userDivisions($line)->where('division_type_id', $divisionType));
        }
        $divisions = $divisions->pluck('id')->unique()->toArray();
        $data = ['Sales' => round(SaleDetail::select('id', 'date', 'quantity')->whereIntegerInRaw('div_id', $divisions)->where(
            'date',
            [$from, $to]
        )->get()->sum('quantity'), 2), 'Target' => round(TargetDetails::select('id', 'date', 'target')->whereIntegerInRaw('div_id', $divisions)->where(
            'date',
            [$from, $to]
        )->get()->sum('target'), 2)];
        return Collect(data_fill($widget, 'data', $data));
    }

    public function execute(): void
    {
    }
}
