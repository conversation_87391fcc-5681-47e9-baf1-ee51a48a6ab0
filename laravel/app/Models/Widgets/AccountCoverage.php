<?php

namespace App\Models\Widgets;

use App\Account;
use App\ActualVisit;
use App\Interfaces\Widgets\ActionExecutionInterface;
use App\Interfaces\Widgets\FetchableInterface;
use App\Scopes\Widgets\AccountCoverageScope;
use App\Traits\Widgets\BootableWidget;
use App\Traits\Widgets\Fetchable;
use App\Traits\Widgets\SettingsAccessAndAuthorizationAccess;
use App\Widget;
use Illuminate\Support\Collection;

class AccountCoverage extends Widget implements FetchableInterface, ActionExecutionInterface
{
    use SettingsAccessAndAuthorizationAccess,Fetchable,BootableWidget;


    public function fetchWidgetData(Widget $widget): Collection
    {
        $account = Account::getAccountIdsInLineDivisionWithAuthUser();
        $data = ['Accounts' => count($account), 'Coverage' => count(ActualVisit::getActualVisitsOfAuthUserAgainstCollectionOfAccountIds($account, 'account_id')->unique('account_id'))];

        return Collect(data_fill($widget, 'data', $data));
    }

    public function execute(): void
    {
    }


}
