<?php

namespace App\Models\Widgets;

use App\DivisionType;
use App\Speciality;
use App\Traits\Widgets\BootableWidget;
use App\Traits\Widgets\SettingsAccessAndAuthorizationAccess;
use App\Scopes\Widgets\MedicalRepVisitPerSpecialityScope;
use App\Interfaces\Widgets\FetchableInterface;
use App\LineDivision;
use App\Traits\Widgets\Fetchable;
use App\Widget;
use App\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MedicalRepVisitPerSpeciality extends Widget implements FetchableInterface
{
    use SettingsAccessAndAuthorizationAccess, Fetchable, BootableWidget;

    public function fetchWidgetData(Widget $widget): Collection
    {
        /**@var User */
        $user = Auth::user();
        $lines = $user->userLines();
        $userIds = $user->belowUsersOfAllLinesWithPositions($lines)->pluck('id');
        $specialities = Speciality::get()->transform(function ($data) {
            $data->name = explode(' / ', $data->name)[0];
            return $data;
        });

        //        $data = User::select([
        //            'users.id as id',
        //            'users.fullname as name',
        //        ])
        //            ->leftJoin('line_users', function ($join) {
        //                $join->on('users.id', 'line_users.user_id')
        //                    ->where(
        //                        fn($q) => $q->where('line_users.to_date', '>=', (string)Carbon::now())
        //                            ->orWhere('line_users.to_date', null)
        //                    );
        //            })
        //            ->leftJoin('lines', 'line_users.line_id', 'lines.id')
        //            ->leftJoin('line_users_divisions', function ($join) {
        //                $join->on('users.id', 'line_users_divisions.user_id')
        //                    ->where(
        //                        fn($q) => $q->where('line_users_divisions.to_date', '>=', (string)Carbon::now())
        //                            ->orWhere('line_users_divisions.to_date', null));
        //            })
        //            ->leftJoin('line_divisions', 'line_users_divisions.line_division_id', 'line_divisions.id')
        //            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
        //            ->where('status', 'active')
        //            ->where('division_types.last_level', 1)
        //            ->where("line_divisions.is_kol", 0)
        //            ->whereIn("users.id", $userIds);
        //        foreach ($specialities as $speciality) {
        //            $name = explode(' / ', $speciality->name)[0];
        //            $data = $data->withCount(['actualVisits as ' . $name => function ($q) use ($speciality) {
        //                $q->whereHas("speciality", function ($q) use ($speciality) {
        //                    $q->where("specialities.id", $speciality->id);
        //                })->whereDate("visit_date", now());
        //            }]);
        //        }

        // Build the CTE query
        $query = DB::query()
            ->fromSub(fn($query) => // First CTE: Valid Users
            $query->from('users')
                ->select([
                    'users.id',
                    'users.fullname as name',
                    'division_types.last_level',
                    'line_divisions.is_kol'
                ])
                ->leftJoin('line_users', function ($join) {
                    $join->on('users.id', 'line_users.user_id')
                        ->where(function ($q) {
                            $q->where('line_users.to_date', '>=', Carbon::now())
                                ->orWhereNull('line_users.to_date');
                        });
                })
                ->leftJoin('lines', 'line_users.line_id', 'lines.id')
                ->leftJoin('line_users_divisions', function ($join) {
                    $join->on('users.id', 'line_users_divisions.user_id')
                        ->where(function ($q) {
                            $q->where('line_users_divisions.to_date', '>=', Carbon::now())
                                ->orWhereNull('line_users_divisions.to_date');
                        });
                })
                ->leftJoin('line_divisions', 'line_users_divisions.line_division_id', 'line_divisions.id')
                ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
                ->where('status', 'active')
                ->whereIn('users.id', $userIds)
                ->where('division_types.last_level', 1)
                ->where('line_divisions.is_kol', 0)
                ->whereNull('users.deleted_at'), 'valid_users');

        // Second CTE: Visit Counts
        $query->joinSub(
            fn($query) => $query->from('actual_visits as visits')
                ->select([
                    'visits.user_id',
                    'doctors.speciality_id',
                    DB::raw('COUNT(*) as visit_count')
                ])
                ->join('doctors as doctors', 'visits.account_dr_id', 'doctors.id')
                ->join('specialities as specialities', 'doctors.speciality_id', 'specialities.id')
                ->whereNull('visits.deleted_at')
                ->whereNull('doctors.deleted_at')
                ->whereNull('specialities.deleted_at')
                ->whereDate('visits.visit_date', Carbon::now())
                ->groupBy('visits.user_id', 'doctors.speciality_id'),
            'visit_counts',
            fn($join) => $join->on('valid_users.id', '=', 'visit_counts.user_id')
        );

        // Select statement with dynamic columns for each speciality
        $selectColumns = ['valid_users.id', 'valid_users.name'];
        foreach ($specialities as $speciality) {
            $name = explode(' / ', $speciality->name)[0];
            $selectColumns[] = DB::raw("COALESCE(SUM(
            CASE WHEN
                crm_visit_counts.speciality_id = {$speciality->id}
            THEN
                crm_visit_counts.visit_count
            ELSE 0 END
              ), 0) as {$name}");
        }

        $result = $query->select($selectColumns)
            ->groupBy('valid_users.id', 'valid_users.name')
            ->limit(30)
            ->get();

        $data = $result->unique('id')->values()
            ->transform(function ($user) use ($specialities) {
                $user->data = collect([]);
                foreach ($specialities as $speciality) {
                    $count = (int)$user->{$speciality->name};
                    if ($count > 0) {
                        $user->data->put(
                            $speciality->name,
                            $count
                        );
                    }
                    unset($user->{$speciality->name});
                }
                return $user;
            });

        return Collect(data_fill($widget, 'data', $data));
    }
}
