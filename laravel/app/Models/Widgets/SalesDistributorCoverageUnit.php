<?php

namespace App\Models\Widgets;

use App\Widget;
use App\Distributor;
use App\Traits\Widgets\Fetchable;
use Illuminate\Support\Collection;
use App\Traits\Widgets\BootableWidget;
use App\Interfaces\Widgets\FetchableInterface;
use App\Interfaces\Widgets\ActionExecutionInterface;
use App\Traits\Widgets\SettingsAccessAndAuthorizationAccess;

class SalesDistributorCoverageUnit extends Widget implements FetchableInterface, ActionExecutionInterface
{
    use SettingsAccessAndAuthorizationAccess,Fetchable,BootableWidget;
    public function fetchWidgetData(Widget $widget): Collection
    {
        $distributors=Distributor::salesInMonth()->get()->map(function($item){return [$item->name=>$item->sales->sum('quantity')];})->collapse();
        // $sales=['Sales'=>Sale::inMonth()->get()->sum('quantity')];
        // $data = collect($sales)->merge($distributors);
        return Collect(data_fill($widget, 'data', $distributors));
    }

    public function execute(): void
    {
    }
}
