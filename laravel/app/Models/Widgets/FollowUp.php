<?php

namespace App\Models\Widgets;

use App\Exceptions\CrmException;
use App\Interfaces\Widgets\FetchableInterface;
use App\Models\PlanLevel;
use App\PlanSetting;
use App\Reciever;
use App\Scopes\Widgets\FollowUpScope;
use App\Traits\Widgets\BootableWidget;
use App\Traits\Widgets\Fetchable;
use App\Traits\Widgets\SettingsAccessAndAuthorizationAccess;
use App\VisitProduct;
use App\Widget;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class FollowUp extends Widget implements FetchableInterface
{
    use SettingsAccessAndAuthorizationAccess, Fetchable, BootableWidget;
    public function fetchWidgetData(Widget $widget): Collection
    {
        /**@var User $user */
        $user = Auth::user();
        $followUps = VisitProduct::whereNotNull('follow_up')->where('follow_up_flag', null)
            ->whereBetween(
                'created_at',
                [Carbon::now()->startOfMonth()->toDateString(), Carbon::now()->endofMonth()->toDateString()]
            );
        if (!$user->hasRole('admin')) {
            $followUps = $followUps->with(['actualVisit' => function ($q) use ($user) {
                $q->where('user_id', $user->id);
            }]);
        }
        $data = $followUps->get()
            ->filter(fn($visit) => $visit->actualVisit != null)->values()
            ->map(function ($visit) {
                return [
                    'id' => $visit->actualVisit?->id,
                    'employee' => $visit->actualVisit?->user->fullname,
                    'division' => $visit->actualVisit?->division->name,
                    'account' => $visit->actualVisit?->account->name,
                    'doctor' => $visit->actualVisit?->doctor?->name ?? '',
                    'follow_up' => $this->actions($visit->follow_up, $visit->actualVisit?->id),
                    'date' => Carbon::parse($visit->created_at)->toDateString(),
                ];
            });

        // throw new CrmException($data);
        return Collect(data_fill($widget, 'data', collect($data) ?? []));
    }

    private function actions($followUp, $visit_id)
    {
        return [
            [
                'id' => $visit_id,
                'type' => 'text',
                'title' => $followUp,
                'transaction' => '',
                'color' => 'primary',
                'visible' =>  $followUp != '',
                'widget_type' => 'followUp'
            ],
        ];
    }
}
