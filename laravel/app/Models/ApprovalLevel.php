<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class ApprovalLevel extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'approval_flow_id',
        'level_number',
        'time_limit',
        'approver_type',
        'approver_id',
        'can_override',
        'is_required',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'can_override' => 'boolean',
        'is_required' => 'boolean',
    ];

    /**
     * Get the approval flow that owns this level.
     */
    public function approvalFlow(): BelongsTo
    {
        return $this->belongsTo(ApprovalFlow::class);
    }

    /**
     * Get the actions for this approval level.
     */
    public function actions(): HasMany
    {
        return $this->hasMany(ApprovalAction::class);
    }

    /**
     * Get the escalation configuration for this level.
     */
    public function escalation(): HasOne
    {
        return $this->hasOne(ApprovalEscalation::class);
    }

    /**
     * Get the approver based on approver_type.
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function getApprover()
    {
        switch ($this->approver_type) {
            case 'user':
                return User::find($this->approver_id);
            case 'role':
                return Role::find($this->approver_id);
            case 'department':
                // Assuming there's a Department model
                return LineDivision::find($this->approver_id);
            default:
                return null;
        }
    }

    /**
     * Get the time limit in seconds.
     *
     * @return int
     */
    public function getTimeLimitInSeconds(): int
    {
        return $this->time_limit * 3600; // Convert hours to seconds
    }

    /**
     * Get the next level in the approval flow.
     *
     * @return ApprovalLevel|null
     */
    public function getNextLevel(): ?ApprovalLevel
    {
        return $this->approvalFlow->levels()
            ->where('level_number', '>', $this->level_number)
            ->orderBy('level_number')
            ->first();
    }

    /**
     * Get the previous level in the approval flow.
     *
     * @return ApprovalLevel|null
     */
    public function getPreviousLevel(): ?ApprovalLevel
    {
        return $this->approvalFlow->levels()
            ->where('level_number', '<', $this->level_number)
            ->orderBy('level_number', 'desc')
            ->first();
    }
}