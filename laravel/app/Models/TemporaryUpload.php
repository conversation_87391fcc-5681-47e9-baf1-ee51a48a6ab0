<?php

namespace App\Models;

use App\Casts\SerializationRecords;
use App\Import;
use App\ModelsImported;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class TemporaryUpload extends Model
{
    const PENDING = 'pending';
    const PROCESSING = 'processing';
    const FINISHED = 'finished';
    const ERROR = 'error';

    protected $fillable = ['uuid','records', 'errors', 'model', 'status', 'case', 'files_imported_id'];
    protected $primaryKey='uuid';
    public $incrementing = false;

    protected $hidden = ['case'];

    protected $casts = [
        'records' => SerializationRecords::class,
        'errors' => 'json'
    ];

    public function fileImported(): BelongsTo
    {
        return $this->belongsTo(Import::class,'files_imported_id');
    }
}
