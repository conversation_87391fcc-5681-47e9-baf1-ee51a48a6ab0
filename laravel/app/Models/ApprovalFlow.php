<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ApprovalFlow extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'total_time_limit',
        'is_active',
    ];

    /**
     * Get the levels for this approval flow.
     */
    public function levels(): HasMany
    {
        return $this->hasMany(ApprovalLevel::class)->orderBy('level_number');
    }

    /**
     * Get the requests for this approval flow.
     */
    public function requests(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Request::class);
    }

    /**
     * Check if the flow has a total time limit.
     *
     * @return bool
     */
    public function hasTotalTimeLimit(): bool
    {
        return !is_null($this->total_time_limit);
    }

    /**
     * Calculate the total time limit in seconds.
     *
     * @return int|null
     */
    public function getTotalTimeLimitInSeconds(): ?int
    {
        return $this->total_time_limit ? $this->total_time_limit * 3600 : null;
    }
}