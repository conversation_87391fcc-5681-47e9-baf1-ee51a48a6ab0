<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProfilingDetail extends Model
{
    // use SoftDeletes;

    protected $guard_name = 'api';

    protected $table = 'profiling_details';

    protected $fillable = [
        'profiling_id','option', 'weight' ,
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];


    public function profiling()
    {
        return $this->belongsTo(Profiling::class);
    }
}
