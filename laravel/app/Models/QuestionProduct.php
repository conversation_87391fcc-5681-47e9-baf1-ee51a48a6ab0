<?php

namespace App\Models;

use App\Product;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuestionProduct extends Model
{
    use HasFactory;
    protected $guard_name = 'api';

    protected $table = 'question_product';

    protected $fillable = [
        'quiz_question_id',
        'product_id',
        'file_id',
    ];

    public function product(){
        return $this->belongsTo(Product::class,'product_id');
    }
    public function question(){
        return $this->belongsTo(QuizQuestion::class,'quiz_question_id');
    }
}
