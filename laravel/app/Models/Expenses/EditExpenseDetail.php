<?php

namespace App\Models\Expenses;

use App\User;
use Illuminate\Database\Eloquent\Model;

class EditExpenseDetail extends Model
{
    protected $guard_name = 'api';

    protected $table = 'edit_expense_details';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'expense_detail_id', 'user_id', 'notes'
    ];

    public function getExpenseDetail()
    {
        return $this->belongsTo(ExpenseDetails::class, 'expense_detail_id');
    }
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
