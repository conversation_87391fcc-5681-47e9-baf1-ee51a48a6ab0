<?php

namespace App\Models\CommercialRequest;

use App\PlanVisitDetails;
use App\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CommercialBill extends Model
{
    // use HasFactory;
    use SoftDeletes;
    public $timestamp = true;
    public $guard_name = 'api';
    protected $table = 'commercial_bills';

    protected $fillable = ['user_id', 'path'];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];
    public function details()
    {
        return $this->morphOne(PlanVisitDetails::class, 'visitable');
    }
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function commercialRequestBills()
    {
        return $this->hasMany(CommercialRequestBill::class . 'commercial_bill_id', 'id');
    }
}
