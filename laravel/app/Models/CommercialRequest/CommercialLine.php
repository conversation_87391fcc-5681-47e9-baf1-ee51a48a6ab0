<?php

namespace App\Models\CommercialRequest;

use App\Line;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class CommercialLine extends Model
{
    use SoftDeletes;


    protected $guard_name = 'api';

    protected $table = 'commercial_lines';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'request_id', 'line_id'
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }
    public function line()
    {
        return $this->belongsTo(Line::class);
    }
}
