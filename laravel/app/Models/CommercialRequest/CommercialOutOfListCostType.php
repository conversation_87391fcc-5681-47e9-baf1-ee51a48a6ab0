<?php

namespace App\Models\CommercialRequest;

use App\Models\CommercialRequest\Costs\CostType;
use Illuminate\Database\Eloquent\Model;

class CommercialOutOfListCostType extends Model
{
    protected $guard_name = 'api';

    protected $table = 'commercial_out_of_list_cost_types';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'request_id', 'out_of_list_id', 'cost_type_id'
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function costType()
    {
        return $this->belongsTo(CostType::class, 'cost_type_id');
    }
    public function outOfList()
    {
        return $this->belongsTo(CommercialOutOfList::class, 'out_of_list_id');
    }
}
