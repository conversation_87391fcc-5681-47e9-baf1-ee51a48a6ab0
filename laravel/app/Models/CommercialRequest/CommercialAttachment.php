<?php

namespace App\Models\CommercialRequest;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CommercialAttachment extends Model
{
    use SoftDeletes;
    public $timestamp = true;
    public $guard_name = 'api';
    protected $table = 'commercial_attachments';

    protected $fillable = ['request_id','name','path'];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];
    
    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }
}
