<?php

namespace App\Models\CommercialRequest;

use App\Line;
use App\LineDivision;
use App\Models\Attachment;
use App\Models\PaidRequest;
use App\Models\Policy;
use App\PlanVisitDetails;
use App\Product;
use App\Reason;
use App\RequestType;
use App\Traits\ModelImportable;
use App\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

class CommercialRequest extends Model
{
    use SoftDeletes;
    use ModelImportable;
    use HasRelationships;

    protected $guard_name = 'api';

    protected $table = 'commercial_requests';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'request_type_id',
        'from_date',
        'to_date',
        'archived',
        'description',
        'll',
        'lg',
        'amount',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function reasons()
    {
        return $this->morphToMany(Reason::class, 'reasonable');
    }
    public function details()
    {
        return $this->morphOne(PlanVisitDetails::class, 'visitable');
    }

    public function approvalFlows()
    {
        return $this->hasManyDeepFromRelations(
            $this->details(),
            (new PlanVisitDetails)->approvalFlows()
        );
    }

    public function paids()
    {
        return $this->morphMany(PaidRequest::class, 'paidable');
    }

    public function bills()
    {
        return $this->belongsToMany(CommercialBill::class, 'commercial_request_bills', 'id', 'request_id');
    }
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function serviceDone()
    {
        return $this->hasOne(ServiceComplete::class, 'request_id');
    }
    public function requestType()
    {
        return $this->belongsTo(RequestType::class, 'request_type_id');
    }
    public function lines()
    {
        return $this->hasMany(CommercialLine::class, 'request_id');
    }
    public function divisions()
    {
        return $this->hasMany(CommercialDivision::class, 'request_id');
    }
    public function types()
    {
        return $this->hasMany(CommercialCostType::class, 'request_id');
    }
    public function products()
    {
        return $this->hasMany(CommercialProduct::class, 'request_id');
    }
    public function pharmacies()
    {
        return $this->hasMany(CommercialPharmacy::class, 'request_id');
    }
    public function users()
    {
        return $this->hasMany(CommercialUser::class, 'request_id');
    }
    public function usersCost()
    {
        return $this->hasMany(CommercialUserCostType::class, 'request_id');
    }
    public function doctors()
    {
        return $this->hasMany(CommercialDoctor::class, 'request_id');
    }
    public function doctorsCost()
    {
        return $this->hasMany(CommercialDoctorCostType::class, 'request_id');
    }
    public function categoriesCosts()
    {
        return $this->hasMany(CommercialCategoriesCost::class, 'request_id');
    }
    public function outOfLists()
    {
        return $this->hasMany(CommercialOutOfList::class, 'request_id');
    }
    public function outOfListCostTypes()
    {
        return $this->hasMany(CommercialOutOfListCostType::class, 'request_id');
    }
    public function attachments()
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    public function policies()
    {
        return $this->morphMany(Policy::class, 'policiable');
    }

    public function getCommercialLines()
    {
        return $this->belongsToMany(Line::class, 'commercial_lines', 'request_id', 'line_id');
    }

    public function getCommercialProducts()
    {
        return $this->belongsToMany(Product::class, 'commercial_products', 'request_id', 'product_id');
    }
    public function getCommercialDivisions()
    {
        return $this->belongsToMany(LineDivision::class, 'commercial_divisions', 'request_id', 'div_id');
    }
    public function getCommercialUser()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function agendas()
    {
        return $this->hasMany(CommercialAgenda::class, 'request_id');
    }
    public function restore()
    {
        $this->withTrashed()->where('id', $this->id)->restore();
        CommercialLine::withTrashed()->where('request_id', $this->id)->restore();
        CommercialDivision::withTrashed()->where('request_id', $this->id)->restore();
        CommercialCostType::withTrashed()->where('request_id', $this->id)->restore();
        CommercialCategoriesCost::withTrashed()->where('request_id', $this->id)->restore();
        CommercialProduct::withTrashed()->where('request_id', $this->id)->restore();
        CommercialPharmacy::withTrashed()->where('request_id', $this->id)->restore();
        CommercialUserCostType::withTrashed()->where('request_id', $this->id)->restore();
        CommercialUser::withTrashed()->where('request_id', $this->id)->restore();
        CommercialDoctorCostType::withTrashed()->where('request_id', $this->id)->restore();
        CommercialDoctor::withTrashed()->where('request_id', $this->id)->restore();
        Attachment::withTrashed()->where('attachable_id', $this->id)
            ->where('attachable_type', CommercialRequest::class)->restore();
        CommercialAgenda::withTrashed()->where('request_id', $this->id)->restore();
    }

    public function forceDelete()
    {
        CommercialLine::where('request_id', $this->id)->withTrashed()->forceDelete();
        CommercialDivision::where('request_id', $this->id)->withTrashed()->forceDelete();
        CommercialCostType::where('request_id', $this->id)->withTrashed()->forceDelete();
        CommercialCategoriesCost::withTrashed()->where('request_id', $this->id)->forceDelete();
        CommercialProduct::where('request_id', $this->id)->withTrashed()->forceDelete();
        CommercialPharmacy::where('request_id', $this->id)->withTrashed()->forceDelete();
        CommercialUserCostType::where('request_id', $this->id)->withTrashed()->forceDelete();
        CommercialUser::where('request_id', $this->id)->withTrashed()->forceDelete();
        CommercialDoctorCostType::where('request_id', $this->id)->withTrashed()->forceDelete();
        CommercialDoctor::where('request_id', $this->id)->withTrashed()->forceDelete();
        Attachment::where('attachable_id', $this->id)
            ->where('attachable_type', CommercialRequest::class)->withTrashed()->forceDelete();
        CommercialAgenda::where('request_id', $this->id)->withTrashed()->forceDelete();
        CommercialRequest::withTrashed()->where('id', $this->id)->forceDelete();
    }


    public function feedbacks()
    {
        return $this->morphMany('App\Models\RequestFeedback', 'requestable');
    }
    static public function commercialsPerPeriod($userId, Carbon $from = null, Carbon $to = null, array $shiftIds = [])
    {
        return CommercialRequest::select('id', 'shift_id')
            ->leftJoin('request_types', 'commercial_requests.request_type_id', 'request_types.id')
            ->where('request_types.with_deduct', '1')->whereBetween('commercial_requests.created_at', [$from, $to])
            ->where('commercial_requests.user_id', $userId)
            ->where(fn($q) => $q->whereIn('shift_id', $shiftIds)->orWhere('shift_id', null))->count();
    }
}


// all: [
//     App\Models\CommercialRequest\CommercialDoctor {#3326
//       id: 6767,
//       doctor_id: 159160,
//       account_id: 90446,
//       request_id: 1067,
//       created_at: "2024-05-13 15:38:59",
//       updated_at: "2024-05-13 15:38:59",
//       deleted_at: null,
//     },
//   ],