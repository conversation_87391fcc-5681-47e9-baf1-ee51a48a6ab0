<?php

namespace App\Models;

use App\Mapping;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use <PERSON><PERSON><PERSON><PERSON>r\EloquentHasManyDeep\HasManyDeep;
use Staud<PERSON>meir\EloquentHasManyDeep\HasRelationships;

class SaleFactor extends Model
{
    use HasRelationships;

    const NORMAL = 1;
    const CR_TARGET = 2;
    const CR_SALES = 3;
    const DIRECT_SALES = 4;

    protected $fillable = [
        'id',
        'name'
    ];

    public function mappings(): BelongsToMany
    {
        return $this->belongsToMany(Mapping::class, "sale_factor_types")
            ->using(SaleFactorType::class);
    }

    public function mappingDetails(): Has<PERSON>any<PERSON>eep
    {
        return  $this->hasManyDeepFromRelations($this->mappings(), (new Mapping())->details());
    }
}
