<?php

namespace App\Models\Help;

use App\LogActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Keyword extends Model
{

    use SoftDeletes;
    protected $guard_name = 'api';
    protected $table = 'help_keywords';
    protected $fillable = ['name'];

    protected $hidden = ['pivot'];

    public function articles()
    {
        return $this->belongsToMany(Article::class, 'article_keyword');
    }
    public function logActivities()
    {
        return $this->morphMany(LogActivity::class, 'loggable');
    }
}
