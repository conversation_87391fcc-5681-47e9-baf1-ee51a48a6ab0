<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\User;
use App\Line;
use App\PlanVisitDetails;
use App\Product;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Staudenmeir\EloquentHasManyDeep\HasManyDeep;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

class Material extends Model
{
    use SoftDeletes;
    use HasRelationships;

    protected $table = 'materials';
    protected $fillable = [
        'id',
        'user_id',
        'material_type_id',
        'date',
        'vendor_id',
        'description',
        'quantity',
        'amount',
        'attachment'
    ];


    public function type(): BelongsTo
    {
        return $this->belongsTo(PromotionalMaterialType::class, 'material_type_id');
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(MaterialVendor::class, 'vendor_id');
    }

    public function materialLines(): HasMany
    {
        return $this->hasMany(MaterialLine::class);
    }

    public function materialProducts(): HasMany
    {
        return $this->hasMany(MaterialProduct::class);
    }
    public function attachments()
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function lines(): BelongsToMany

    {
        return $this->belongsToMany(Line::class, 'material_lines', 'material_id', 'line_id');
    }

    public function products(): BelongsToMany

    {
        return $this->belongsToMany(Product::class, 'material_products', 'material_id', 'product_id');
    }

    public function details()
    {
        return $this->morphOne(PlanVisitDetails::class, 'visitable');
    }

    public function approvalFlows()
    {
        return $this->hasManyDeepFromRelations(
            $this->details(),
            (new PlanVisitDetails)->approvalFlows()
        );
    }

    public function restore()
    {
        $this->withTrashed()->where('id', $this->id)->restore();
        MaterialLine::withTrashed()->where('material_id', $this->id)->restore();
        MaterialProduct::withTrashed()->where('material_id', $this->id)->restore();
        Attachment::withTrashed()->where('attachable_id', $this->id)
            ->where('attachable_type', Material::class)->restore();
    }

    public function forceDelete()
    {
        MaterialLine::where('material_id', $this->id)->withTrashed()->forceDelete();
        MaterialProduct::where('material_id', $this->id)->withTrashed()->forceDelete();
        MaterialStock::where('material_id', $this->id)->withTrashed()->forceDelete();
        Attachment::where('attachable_id', $this->id)
            ->where('attachable_type', Material::class)->withTrashed()->forceDelete();
        PlanVisitDetails::where('visitable_id', $this->id)
            ->where('visitable_type', Material::class)->delete();
        Material::withTrashed()->where('id', $this->id)->forceDelete();
    }


    public function feedbacks()
    {
        return $this->morphMany('App\Models\RequestFeedback', 'requestable');
    }
}
