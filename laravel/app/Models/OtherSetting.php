<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class OtherSetting extends Model
{
    use SoftDeletes;
    protected $guard_name = 'api';

    protected $table = 'other_settings';

    protected $fillable = ['name', 'key', 'value', 'type'];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];
    
    public function frequencyType()
    {
        return $this->belongsTo(FrequencyType::class,'value');
    }
}
