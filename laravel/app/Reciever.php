<?php

namespace App;

use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Database\Eloquent\SoftDeletes;

class Reciever extends Pivot
{
    use SoftDeletes;

    protected $guard_name = 'api';

    protected $table = 'recievers';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['task_id','reciever_id','mention'];
    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }
    public function user()
    {
        return $this->belongsTo(User::class,'user_id');
    }
    public function tasks()
    {
        return $this->belongsToMany(Task::class,'task_id');
    }
}
