<?php

namespace App\Rules;

use App\Distributor;
use App\Services\UnifiedService;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Log;

use function PHPUnit\Framework\isNull;

class UnifiedBrickExists implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct(private Distributor $distributor)
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $unified = new UnifiedService();
        $unibrick = $unified->getUnifiedBrick($this->distributor->unified_distributor_id, $value);
        Log::info($unibrick);
        return !isNullable($unibrick);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'the :attribute has no valid unified brick.';
    }
}
