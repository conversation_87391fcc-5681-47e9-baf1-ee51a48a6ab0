<?php

namespace App\Rules;

use App\ErrorMessages;
use App\Models\Help\Topic;
use Illuminate\Contracts\Validation\Rule;

class ExistsOrNull implements Rule
{
    protected $class=null;
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($class)
    {
        $this->class=$class;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if ($value === null || $value === 'null' ) {
            return true;
        }
        if ($this->class::find($value)) {
            return true;
        }

    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        $required_field = ErrorMessages::where('slug','field_exists_or_null')->first();
        return 'Error Code: '. $required_field->code .' :attribute '.$required_field->message;
    }
}
