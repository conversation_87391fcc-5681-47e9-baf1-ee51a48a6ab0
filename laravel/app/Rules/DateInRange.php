<?php

namespace App\Rules;

use App\Exceptions\CrmException;
use App\Helpers\CrmExcelDate;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Carbon;

class DateInRange implements Rule
{
    protected $first;
    protected $model;
    protected $from;
    protected $to;
    protected $data;
    protected $id;
    protected $helpersValidation;
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($model, $first, $from, $to,$data,$id='id')
    {
        $this->first = $first;
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->data = $data;
        $this->id=$id;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $element=$this->data[explode('.',$attribute)[0]];
        $item = resolve($this->model)->where($this->id, '=',$element[$this->first])->first()->toArray();

        $from =$item[$this->from];
        $to = $item[$this->to];
        $valueDate = Carbon::createFromFormat(CrmExcelDate::OFFICIAL_FORMAT, $value);

        if (!$from) return false;

        if (!$to) return $valueDate->gte($from);

        return $valueDate->gte($from) && $valueDate->lte($to);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'the :attribute is not in range of ' . class_basename($this->model) . ' .';
    }
    /**
     * @derecated
     */
    public function injectId($id)
    {
        $this->id=$id;
        return $this;
    }
}
