<?php

namespace App\Rules;

use App\Distributor;
use App\Mapping;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Cache;

class IsDuplicatedMapping implements ValidationRule
{
    public function __construct(private readonly bool $isMappingWithDistributor, private Distributor $distributor, private readonly int $file_id, private readonly int $cacheSavedHours)
    {
    }

    /**
     * Run the validation rule.
     *
     * @param \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $query = Mapping::whereCode($value);

        if ($this->isMappingWithDistributor) {
            $query->where("distributor_id", $this->distributor->id);
        }
        $count = Cache::remember(
            'validation: for duplicated Mapping file_id: ' . $this->file_id . ' mapping code: ' . $value,
            $this->cacheSavedHours,
            fn() => $query->count()
        );

        if ($count > 1) {
            $fail("Pharmacy Mapping is Duplicated.");
        }
    }
}
