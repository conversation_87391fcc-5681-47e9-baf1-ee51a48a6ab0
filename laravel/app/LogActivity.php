<?php

namespace App;

use App\Traits\ForceDeleteData;
use App\Traits\RestoreData;
use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Permission;

class LogActivity extends Model
{

    use RestoreData;
    use ForceDeleteData;
    protected $guard_name = 'api';

    protected $table = 'log_activities';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'ip', 'user_id', 'action_id', 'form_id', 'permission_id','model_id','model_type','ll','lg'
    ];

    public function permission()
    {
        return $this->belongsTo(Permission::class,'permission_id');
    }

    public function actions()
    {
        return $this->hasMany(Action::class);
    }

    public function form()
    {
        return $this->belongsTo(Form::class,'form_id');
    }


    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function loggable()
    {
        return $this->morphTo();
    }
}
