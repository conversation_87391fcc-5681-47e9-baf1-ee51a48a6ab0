<?php

namespace App;

use App\Exceptions\CrmException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class PositionDoubleVisitSetting extends Model
{

    const DOUBLE_VISITING = 1;
    const NOT_DOUBLE_VISITING = 0;

    protected $guard_name = 'api';

    protected $table = 'position_double_visit_settings';

    protected $guarded = ['id'];

    protected $fillable = ['user_position_id', 'double_visit'];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

    public function hasDoubleVisit()
    {
        return $this->double_visit == PositionDoubleVisitSetting::DOUBLE_VISITING;
    }

    public function userPosition()
    {
        return $this->hasMany(UserPosition::class, 'id', 'user_position_id');
    }

    public function lines()
    {
        return $this->belongsToMany(Line::class, 'line_user_positions', 'user_position_id', 'line_id')->whereNull('line_user_positions.deleted_at');
    }

    public static function users($line, $user_id = null)
    {
        /**@var User */
        $user = isNullable($user_id) ? $user = Auth::user() : User::find($user_id);
        if ($user->hasPosition()) {
            $line_user_positions = LineUserPosition::where('line_id', $line)
                ->whereHas('userPosition', fn($q) =>
                $q->where('user_positions.from_date', '<=', Carbon::now())
                    ->where(fn($q) => $q->where('user_positions.to_date', '=', null)
                        ->orWhere('user_positions.to_date', '>=', (string)Carbon::now())))->get()->pluck('user_position_id');
            return  PositionDoubleVisitSetting::where('double_visit', '=', PositionDoubleVisitSetting::DOUBLE_VISITING)
                ->with(['userPosition' => function ($q) use ($line_user_positions, $user) {
                    $q->whereIn('user_positions.id', $line_user_positions)
                        ->whereNot('user_id', $user->id);
                }])
                ->get()
                ->pluck('userPosition')->collapse()->map(function ($userPosition) {
                    return $userPosition->user;
                });
        } else {
            $division_type = DivisionType::where('last_level', '=', 1)->value('id');

            $line_user_positions = LineUserPosition::where('line_id', $line)
                ->whereHas('userPosition', fn($q) =>
                $q->where('user_positions.from_date', '<=', Carbon::now())
                    ->where(fn($q) => $q->where('user_positions.to_date', '=', null)
                        ->orWhere('user_positions.to_date', '>=', (string)Carbon::now())))->get();
            foreach ($line_user_positions as $line_user_position) {
                $belowDivisions = collect();
                $positionDivisions = LineDivisionPosition::where('user_position_id', $line_user_position->user_position_id)
                    ->whereHas('division', function ($q) use ($line) {
                        $q->where('line_id', $line);
                    })->get();
                if (!empty($positionDivisions)) {
                    foreach ($positionDivisions as $positionDivision) {
                        $belowDivisions = $belowDivisions->merge(LineDivision::find($positionDivision->line_div_id)->getBelowDivisions()->where('is_kol', 0)
                            ->where('division_type_id', $division_type)->unique('id')->pluck('id'));
                    }
                }

                $userDivisions = $user->allBelowDivisions(Line::find($line))->where('is_kol', 0)
                    ->where('division_type_id', $division_type)->whereIn('id', $belowDivisions)->unique('id')->pluck('id')->toArray();
                if (empty($userDivisions)) {
                    $line_user_positions = $line_user_positions->filter(fn($item) => $item->user_position_id != $line_user_position->user_position_id)->values();
                }
            }
            $line_user_positions = $line_user_positions->pluck('user_position_id')->toArray();

            return PositionDoubleVisitSetting::where('double_visit', '=', PositionDoubleVisitSetting::DOUBLE_VISITING)
                ->with(['userPosition' => function ($q) use ($line_user_positions) {
                    $q->whereIn('user_positions.id', $line_user_positions);
                }])
                ->get()
                ->pluck('userPosition')->collapse()->map(function ($userPosition) {
                    return $userPosition->user;
                });
        }
    }
}
