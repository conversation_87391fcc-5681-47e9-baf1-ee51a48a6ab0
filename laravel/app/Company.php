<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class Company extends Model
{
    use SoftDeletes;

    public $timestamp = true;

    public $guard_name = 'api';

    protected $appends = ['url'];

    protected $table = 'companies';

    protected $fillable = ['name', 'address', 'email', 'tel', 'logo'];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function getUrlAttribute()
    {
        return Storage::url($this->logo);
    }
    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }
}
