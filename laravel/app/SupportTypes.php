<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupportTypes extends Model
{
    use SoftDeletes ;
    public $timestamps = true;
    protected $guard_name = 'api';

    protected $table = 'support_types';

    protected $fillable = ['name'];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }
}
