<?php

namespace App\Data;

use App\Data\Transformers\PercentageTransformer;
use App\Data\Transformers\RoundedTransformer;
use App\Data\Transformers\PipeLineTransformer;
use Spatie\LaravelData\Attributes\Hidden;
use <PERSON>tie\LaravelData\Attributes\MapOutputName;
use <PERSON><PERSON>\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;

class SalesIncentiveHolderData extends Data
{

    public function __construct(
        public int    $id,
        public string $line,
        public string $in_charge_date = '',
        public string $division = '',
        public string $brick = '',
        public string $employee = '',
        public string $emp_code = '',
        public string $product = '',
        public string $brand = '',
        #[WithTransformer(RoundedTransformer::class)]
        public float  $p_w = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $product_value = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $cov = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $freq = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $call_r = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $kpis_value = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $coaching_ratio = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $covered_coaching = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $vacant_ratio = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $manager_coverage = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $m_k_ratio = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $total_incentive = 0.0,
        #[MapOutputName('75%')]
        #[WithTransformer(RoundedTransformer::class)]
        public float  $seventy_five_percent = 0.0,
        #[MapOutputName('25%')]
        #[WithTransformer(RoundedTransformer::class)]
        public float  $twenty_five_percent = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $sales_unit = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $sales_value = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $target_unit = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $target_value = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $achievement_unit = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $achievement_value = 0.0,
        public string $color = '',
        #[WithTransformer(RoundedTransformer::class)]
        public float  $kpis = 0.0,
        #[Hidden]
        #[WithTransformer(RoundedTransformer::class)]
        public float  $district_kpis = 0.0,
        #[Hidden]
        public ?int   $parent_id = 0,
        #[Hidden]
        public ?int   $parent_role_id = 0,
        #[Hidden]
        public bool   $is_total = false,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $tot_su = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $tot_sv = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $tot_tu = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $tot_tv = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $ach_u = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $ach_v = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Jan_SU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Jan_SV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Jan_TU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Jan_TV = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Jan_AU = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Jan_AV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Feb_SU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Feb_SV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Feb_TU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Feb_TV = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Feb_AU = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Feb_AV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Mar_SU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Mar_SV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Mar_TU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Mar_TV = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Mar_AU = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Mar_AV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Apr_SU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Apr_SV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Apr_TU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Apr_TV = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Apr_AU = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Apr_AV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $May_SU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $May_SV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $May_TU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $May_TV = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $May_AU = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $May_AV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Jun_SU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Jun_SV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Jun_TU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Jun_TV = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Jun_AU = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Jun_AV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Jul_SU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Jul_SV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Jul_TU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Jul_TV = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Jul_AU = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Jul_AV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Aug_SU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Aug_SV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Aug_TU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Aug_TV = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Aug_AU = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Aug_AV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Sep_SU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Sep_SV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Sep_TU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Sep_TV = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Sep_AU = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Sep_AV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Oct_SU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Oct_SV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Oct_TU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Oct_TV = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Oct_AU = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Oct_AV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Nov_SU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Nov_SV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Nov_TU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Nov_TV = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Nov_AU = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Nov_AV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Dec_SU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Dec_SV = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Dec_TU = 0.0,
        #[WithTransformer(RoundedTransformer::class)]
        public float  $Dec_TV = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Dec_AU = 0.0,
        #[WithTransformer(PipeLineTransformer::class, [
            RoundedTransformer::class,
            PercentageTransformer::class
        ])]
        public float  $Dec_AV = 0.0,
    )
    {
    }

}
