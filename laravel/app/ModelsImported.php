<?php

namespace App;

use App\Models\TemporaryUpload;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ModelsImported extends Model
{
    use SoftDeletes ;

    protected $guard_name = 'api';

    protected $table = 'models_imported';

    protected $fillable = [
        'files_imported_id', 'model_type'
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function fileImported()
    {
        return $this->belongsTo(Import::class);
    }
}
