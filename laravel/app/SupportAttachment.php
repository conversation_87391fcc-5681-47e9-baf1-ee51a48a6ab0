<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupportAttachment extends Model
{
    use SoftDeletes;
    public $timestamp = true;
    public $guard_name = 'api';
    protected $table = 'support_attachments';

    protected $fillable = ['support_id','name','path'];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];
    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }
}
