<?php

namespace App;

use App\Traits\ModelExportable;
use App\Traits\ModelImportable;
use App\Traits\PdfExportable;
use App\Traits\SendMail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DoctorFrequency extends Model
{
    use SoftDeletes;
    use ModelImportable;
    use ModelExportable;
    use PdfExportable;
    use SendMail;

    protected $guard_name = 'api';

    protected $table = 'doctor_frequencies';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id', 'account_id', 'doctor_id', 'line_id', 'date', 'frequency', 'file_id', 'year', 'month'
    ];

    protected $casts = [
        'date' => 'datetime',
        'deleted_at' => 'datetime',
        'year' => 'integer',
        'month' => 'integer',
    ];

    /**
     * Boot method to automatically set year and month when date is set
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if ($model->date) {
                $model->year = $model->date->year;
                $model->month = $model->date->month;
            }
        });

        static::updating(function ($model) {
            if ($model->isDirty('date') && $model->date) {
                $model->year = $model->date->year;
                $model->month = $model->date->month;
            }
        });
    }

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

    public function line()
    {
        return $this->belongsTo(Line::class);
    }
    public function account()
    {
        return $this->belongsTo(account::class);
    }
    public function doctor()
    {
        return $this->belongsTo(doctor::class);
    }
}
