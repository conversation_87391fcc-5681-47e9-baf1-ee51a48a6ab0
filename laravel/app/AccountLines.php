<?php

namespace App;

use App\Models\Grouping;
use App\Models\NewAccountDoctor;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

class AccountLines extends Model
{
    use SoftDeletes;

    protected $guard_name = 'api';

    protected $table = 'account_lines';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'account_id',
        'line_id',
        'line_division_id',
        'brick_id',
        'class_id',
        'group_id',
        'from_date',
        'to_date',
        'file_id',
        'll',
        'lg',
        'visit_id',
    ];


    protected $casts = [
        'from_date' => 'datetime',
        'to_date' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

    public function accounts()
    {
        return $this->belongsTo(Account::class, 'account_id');
    }
    public function details()
    {
        return $this->morphOne(PlanVisitDetails::class, 'visitable');
    }
    public function newAccountDoctors()
    {
        return $this->hasMany(NewAccountDoctor::class, 'account_lines_id');
    }
    public function line()
    {
        return $this->belongsTo(Line::class, 'line_id');
    }

    public function group()
    {
        return $this->belongsTo(Grouping::class, 'group_id');
    }

    public function class()
    {
        return $this->belongsTo(Classes::class, 'class_id');
    }

    public function line_division()
    {
        return $this->belongsTo(LineDivision::class, 'line_division_id');
    }

    public function brick()
    {
        return $this->belongsTo(Brick::class);
    }

    public function account_line_class()
    {
        return $this->belongsTo(Classes::class, 'class_id');
    }
    public function restore()
    {
        $this->withTrashed()->where('id', $this->id)->restore();
        NewAccountDoctor::withTrashed()->where('account_lines_id', $this->id)->restore();
    }

    public function forceDelete()
    {
        NewAccountDoctor::where('account_lines_id', $this->id)?->withTrashed()->forceDelete();
        AccountLines::where('id', $this->id)?->withTrashed()->forceDelete();
    }
}
