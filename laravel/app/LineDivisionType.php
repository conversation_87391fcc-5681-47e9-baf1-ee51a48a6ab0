<?php

namespace App;

use App\Traits\ModelAvailability;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class LineDivisionType extends Model
{
    use SoftDeletes ;
    use ModelAvailability;

    protected $guard_name = 'api';

    protected $table = 'line_division_types';

    // protected $appends=['active'];
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'divisiontype_id', 'line_id', 'from_date', 'to_date','file_id'
    ];

    protected $casts = [
        'from_date' => 'datetime',
        'to_date' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

    public function line()
    {
        return $this->belongsTo(Line::class);
    }

    public function linedivisions()
    {
        return $this->hasMany(LineDivision::class,'division_type_id');
    }

    public function divisiontype()
    {
        return $this->belongsTo(DivisionType::class, 'divisiontype_id');
    }
}
