<?php

namespace App\Helpers;

use Illuminate\Support\Carbon;

class SimpleDate
{
    public static function firstAndLastOFDay($day = null)
    {
        if ($day) {
            $date1 = Carbon::createFromFormat('d', $day);
            $date2 = Carbon::createFromFormat('d', $day);
            return [$date1->startOfDay(), $date2->endOfDay()];
        }
        return [Carbon::now()->startOfDay(), Carbon::now()->endOfDay()];
    }

    public static function firstAndLastOFMonth($month = null)
    {
        if ($month) {
            $date1 = Carbon::createFromFormat('m', $month);
            $date2 = Carbon::createFromFormat('m', $month);
            return [$date1->startOfMonth(), $date2->lastOfMonth()];
        }
        return [Carbon::now()->startOfMonth(), Carbon::now()->lastOfMonth()];
    }

    public static function firstAndLastOFYear($year = null)
    {

        if ($year) {
            $date1 = Carbon::createFromFormat('y', $year);
            $date2 = Carbon::createFromFormat('y', $year);

            return [$date1->startOfYear(), $date2->lastOfYear()];
        }
        return [Carbon::now()->startOfYear(), Carbon::now()->lastOfYear()];
    }

    public static function format($format,$time,$oldFormat='Y-m-d'){
        $date=Carbon::createFromFormat($oldFormat,$time);
        return $date->format($format);
    }
}
