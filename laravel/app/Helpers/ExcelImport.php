<?php

namespace App\Helpers;

use App\Interfaces\WithValidation;
use App\Services\Enums\TemporaryUploadCase;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

abstract class ExcelImport implements WithHeadingRow, WithValidation
{
    private array $processedData = [];
    private ?string $processedModel = null;

    public function __construct(
        protected int                 $file_id,
        protected string              $model_type,
        protected TemporaryUploadCase $case,
        private readonly int          $count = 0,
        protected bool                $validate = true,
        protected bool                $import = true,
        protected array               $array = [],
    )
    {
    }


    abstract public function rules(array $row): array;


    abstract public function model(array $row): bool;


    public function collection(Collection $rows): array|null
    {

        $rows = $this->handelDateCells($rows);

        if ($this->validate) {
            $rowsArray = $rows->toArray();
            $rules = $this->rules($rowsArray);
            $validator = Validator::make($rowsArray, $rules);
            if ($validator->fails()) {
                return $validator->errors()->toArray();
            }
        }

        if ($this->import) {
            DB::beginTransaction();
            $success = $this->model($rows->toArray());
            $success ? DB::commit() : DB::rollBack();
        }

        return null;
    }


    protected function bulkInsert(array $data, ?string $model = null): void
    {
        $this->processedData = $data;
        $this->processedModel = $model;
    }

    public function getProcessedData(): array
    {
        return [$this->processedModel,$this->processedData];
    }

    private function handelDateCells($row): Collection
    {
        return $row->map(function ($cell, $key) {
            if (ctype_space($cell)) {
                $cell = trim($cell);
            }

            if (Str::contains($key, ['date']) && $cell != null) {
                $cell = is_array($cell)
                    ? Carbon::parse($cell["date"], $cell["timezone"])
                    : $cell;
                $cell = Carbon::parse($cell)->format(CrmExcelDate::OFFICIAL_FORMAT);
            }


            if ($cell == '') $cell = null;

            return $cell;
        });
    }
}
