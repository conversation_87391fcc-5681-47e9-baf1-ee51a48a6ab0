<?php


namespace App\Helpers\FastExcelPlus;


use Illuminate\Support\Collection;

use OpenSpout\Common\Exception\IOException;
use OpenSpout\Common\Exception\UnsupportedTypeException;
use OpenSpout\Reader\Exception\ReaderNotOpenedException;
use OpenSpout\Reader\ReaderInterface;
use OpenSpout\Reader\SheetInterface;
use OpenSpout\Reader\CSV\Reader as CsvReader;
use OpenSpout\Reader\ODS\Reader as OdsReader;
use OpenSpout\Reader\XLSX\Reader as XlsxReader;
use OpenSpout\Reader\CSV\Options as CSVOptions;
use OpenSpout\Reader\ODS\Options as ODSOptions;
use OpenSpout\Reader\XLSX\Options as XLSXOptions;


trait Importable
{

    /**
     * @var int
     */
    private $sheet_number = 1;


    abstract protected function setOptions(&$options): void;


    public function import(string $path, callable $callback = null): Collection
    {
        $reader = $this->reader($path);

        foreach ($reader->getSheetIterator() as $key => $sheet) {
            if ($this->sheet_number != $key) {
                continue;
            }
            $collection = $this->importSheet($sheet, $callback);
        }
        $reader->close();

        return collect($collection ?? []);
    }


    public function importSheets(string $path, ?callable $callback = null): Collection
    {
        $reader = $this->reader($path);

        $collections = [];
        foreach ($reader->getSheetIterator() as $key => $sheet) {
            if ($this->with_sheets_names) {
                $collections[$sheet->getName()] = $this->importSheet($sheet, $callback);
            } else {
                $collections[] = $this->importSheet($sheet, $callback);
            }
        }
        $reader->close();

        return new Collection($collections);
    }


    /**
     * @throws IOException
     * @throws UnsupportedTypeException
     */
    public function reader(string $path): null|CsvReader|OdsReader|XlsxReader|ReaderInterface
    {
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));

        $options = match ($extension) {
            'csv' => new CSVOptions(),
            'xlsx' => new XLSXOptions(),
            'ods' => new ODSOptions(),
            default => throw new UnsupportedTypeException('No readers supporting the given type: ' . $extension),
        };

        $reader = match ($extension) {
            'csv' => new CSVReader($options),
            'xlsx' => new XLSXReader($options),
            'ods' => new ODSReader($options),
            default => throw new UnsupportedTypeException('No readers supporting the given type: ' . $extension),
        };
        $this->setOptions($options);
        /* @var ReaderInterface $reader */
        $reader->open($path);

        return $reader;
    }


    private function transposeCollection(array $array): array
    {
        $collection = [];

        foreach ($array as $row => $columns) {
            foreach ($columns as $column => $value) {
                data_set($collection, implode('.', [
                    $column,
                    $row,
                ]), $value);
            }
        }

        return $collection;
    }


    private function importSheet(SheetInterface $sheet, ?callable $callback = null): array
    {
        $headers = [];
        $collection = [];
        $count_header = 0;
        foreach ($sheet->getRowIterator() as $k => $rowAsObject) {
            $row = $rowAsObject->toArray();

            if ($this->end_row == 2) {
                if ($k >= $this->start_row) {
                    if ($this->with_header) {
                        if ($k == $this->start_row) {
                            $headers = $this->toStrings($row);
                            $count_header = count($headers);
                            continue;
                        }
                        if ($count_header > $count_row = count($row)) {
                            $row = array_merge($row, array_fill(0, $count_header - $count_row, null));
                        } elseif ($count_header < $count_row = count($row)) {
                            $row = array_slice($row, 0, $count_header);
                        }
                    }
                    if ($callback) {
                        if ($result = $callback(empty($headers) ? $row : array_combine($headers, $row))) {
                            $collection[] = $result;
                        }
                    } else {
                        $collection[] = empty($headers) ? $row : array_combine($headers, $row);
                    }
                }
            } else {
                if ($k >= $this->start_row && $k <= $this->end_row) {
                    if ($this->with_header) {
                        if ($k == $this->start_row) {
                            $headers = $this->toStrings($row);
                            $count_header = count($headers);
                            continue;
                        }
                        if ($count_header > $count_row = count($row)) {
                            $row = array_merge($row, array_fill(0, $count_header - $count_row, null));
                        } elseif ($count_header < $count_row = count($row)) {
                            $row = array_slice($row, 0, $count_header);
                        }
                        if ($k == $this->end_row) {
                            break;
                        }
                    }
                    if ($callback) {
                        if ($result = $callback(empty($headers) ? $row : array_combine($headers, $row))) {
                            $collection[] = $result;
                        }
                    } else {
                        $collection[] = empty($headers) ? $row : array_combine($headers, $row);
                    }
                }

            }
        }

        if ($this->transpose) {
            return $this->transposeCollection($collection);
        }

        return $collection;
    }

    /**
     * @throws IOException
     * @throws UnsupportedTypeException
     * @throws ReaderNotOpenedException
     */
    public function importIterated(string $path): \Generator
    {
        $reader = $this->reader($path);
        $headers = [];
        $count_header = 0;
        foreach ($reader->getSheetIterator() as $k => $sheet) {
            if ($k != 1) {
                continue;
            }
            foreach ($sheet->getRowIterator() as $key => $rowAsObject) {
                $row = $rowAsObject->toArray();
                if ($key >= 1) {
                    if ($key == 1) {
                        $headers = static::toStrings($row);
                        $count_header = count($headers);
                        continue;
                    }
                    if ($count_header > $count_row = count($row)) {
                        $row = array_merge($row, array_fill(0, $count_header - $count_row, null));
                    } elseif ($count_header < $count_row = count($row)) {
                        $row = array_slice($row, 0, $count_header);
                    }
                }
                $collection = empty($headers) ? $row : array_combine($headers, $row);
                yield $collection;
            }
        }

        $reader->close();
    }


    public static function toStrings(array $values): array
    {
        foreach ($values as &$value) {
            if ($value instanceof \DateTime) {
                $value = $value->format('Y-m-d H:i:s');
            } elseif ($value) {
                $value = (string)$value;
            }
        }

        return $values;
    }
}
