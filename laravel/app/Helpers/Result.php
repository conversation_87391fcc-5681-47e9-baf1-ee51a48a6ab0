<?php

namespace App\Helpers;

use LogicException;
use Throwable;

class Result {
    private bool $isSuccess;
    private mixed $value;
    private ?Throwable $error;

    /**
     * Private constructor to prevent direct instantiation.
     * Use static factory methods success() or failure() instead.
     */
    private function __construct(bool $isSuccess, mixed $value = null, ?Throwable $error = null) {
        $this->isSuccess = $isSuccess;
        $this->value = $value;
        $this->error = $error;
    }

    /**
     * Creates a successful result with the provided value.
     *
     * @param mixed $value The success value
     * @return Result A success result
     */
    public static function success(mixed $value): self {
        return new self(true, $value);
    }

    /**
     * Creates a failure result with the provided error.
     *
     * @param Throwable $error The error
     * @return Result A failure result
     */
    public static function failure(Throwable $error): self {
        return new self(false, null, $error);
    }

    /**
     * Checks if the result is successful.
     *
     * @return bool True if the result is successful, false otherwise
     */
    public function isSuccess(): bool {
        return $this->isSuccess;
    }

    /**
     * Checks if the result is a failure.
     *
     * @return bool True if the result is a failure, false otherwise
     */
    public function isFailure(): bool {
        return !$this->isSuccess;
    }

    /**
     * Gets the value of a successful result.
     *
     * @return mixed The success value
     * @throws LogicException if called on a failure result
     */
    public function getValue(): mixed {
        if (!$this->isSuccess) {
            throw new LogicException('Cannot get value from a failure result');
        }
        return $this->value;
    }

    /**
     * Gets the error of a failure result.
     *
     * @return Throwable The error
     * @throws LogicException if called on a success result
     */
    public function getError(): Throwable {
        if ($this->isSuccess) {
            throw new LogicException('Cannot get error from a success result');
        }
        return $this->error;
    }
}
