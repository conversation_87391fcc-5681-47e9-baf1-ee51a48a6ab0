<?php


namespace App\Helpers;

use App\Action;
use App\Exceptions\FileNotFoundException;
use App\Form;
use App\Helpers\LogActivity;
use Spatie\Permission\Models\Permission;

class Template
{
    public function getFileAttributes($filename)
    {
        $form_name = explode("_", $filename)[0];
        $file_path = storage_path() . "/app/downloads/" . $filename;
        $headers = [
            'Content-Type'=>'application/vnd.ms-excel',
            'Content-Disposition'=>'attachment; filename=' . $filename,
        ];
        if (!file_exists($file_path)) {
            throw new FileNotFoundException($filename, 404);
        }
        LogActivity::addLog();
        return ['path' => $file_path, 'headers' => $headers];
    }
}
