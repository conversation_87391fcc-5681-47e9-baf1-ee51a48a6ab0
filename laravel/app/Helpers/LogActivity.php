<?php


namespace App\Helpers;

use App\Exceptions\CrmException;
use App\LogActivity as LogActivityModel;
use App\Permission;
use Illuminate\Support\Facades\DB;

class LogActivity
{
    public static function addLog($model_id = null, $model_type = null, $user = null, $per = null)
    {

        $permission = Permission::where("name", request()->route()->getName())->first();
        if (!isNullable($per)) $permission = Permission::where("name", $per)->first();
        $log = [];
        $log['ip'] = request()->ip();
        $log['user_id'] = $user?->id ?? auth()->id();
        $log['action_id'] = $permission?->action_id;
        $log['form_id'] = $permission?->form_id;
        $log['permission_id'] = $permission?->id;
        $log['model_id'] = $model_id;
        $log['model_type'] = $model_type;
        $log = array_merge($log, getUserLocation($user));
        LogActivityModel::create($log);
    }

    public static function addLogs($model_ids=[], $model_type = null, $user = null, $per = null){
        $permission = Permission::where("name", request()->route()->getName())->first();
        if (!isNullable($per)) $permission = Permission::where("name", $per)->first();
        $logs = [];
        $userLocation=getUserLocation($user);
        foreach($model_ids as $model_id){
            $logs [] =[
                'ip' => request()->ip(),
                'user_id' => $user?->id ?? auth()->id(),
                'action_id' => $permission?->action_id,
                'form_id' => $permission?->form_id,
                'permission_id' => $permission?->id,
                'model_id' => $model_id,
                'model_type' => $model_type,
                'll'=>$userLocation['ll'],
                'lg'=>$userLocation['lg'],
                'created_at'=>now(),
                'updated_at'=>now(),
            ];
        }
        LogActivityModel::insert($logs);
    }


    public static function logActivityLists()
    {
        return LogActivityModel::latest()->get();
    }
}
