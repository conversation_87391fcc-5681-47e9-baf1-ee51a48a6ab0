<?php

namespace App\Imports;

use App\DivisionType;
use App\Helpers\ExcelImport;
use App\ModelsImported;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class DivisionTypesImport extends ExcelImport
{

    /**
     * @return array
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function rules(array $row): array
    {
        return [
            'name' => ['required', 'string', 'unique:division_types,name'],
            'parent' => ['nullable', 'numeric', 'exists_not_soft_deleted:division_types,id'],
            'level' => ['required', 'numeric'],
            'sort' => ['nullable', 'numeric', 'unique:division_types,sort'],
            'notes' => ['nullable', 'string'],
        ];
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row): bool
    {

        try {
            $this->bulkInsert([
                'file_id' => $this->file_id,
                'name' => $row['name'],
                'notes' => $row['notes'],
                'sort' => $row['sort'],
                'parent_id' => $row['parent_id'],
                'level' => $row['level'],
                'created_at' => now(),
                'updated_at' => now(),
            ],DivisionType::class);
        } catch (\Exception $e) {
            return false;
        }

        Cache::remember(
            "division_type_inserted_in_file:" . $this->file_id,
            now()->addHours(2),
            fn() =>  ModelsImported::insert([
                'files_imported_id' => $this->file_id,
                'model_type' => ('App\DivisionType'),
                'created_at' => now(),
                'updated_at' => now(),
            ])
        );


        return true;
    }
}
