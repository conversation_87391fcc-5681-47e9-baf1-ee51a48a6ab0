<?php

namespace App\Imports;

use App\Approvable;
use App\DivisionType;
use App\ErrorMessages;
use App\Exceptions\CrmException;
use App\Helpers\CrmExcelDate;
use App\Helpers\ExcelImport;
use App\Line;
use App\LineDivision;
use App\LineDivisionType;
use App\ModelsImported;
use App\Planable;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class LineDivisionsImport extends ExcelImport
{
    /**
     * @return array
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function rules(array $row): array
    {
        return [
            'line_division_type_id' => [
                'required', 'numeric',
                // function ($attribute, $value, $fail) {
                //     $division_type_id = DivisionType::where('parent_id', 0)->first()->id;
                //     $element = $this->rows[explode('.', $attribute)[0]];
                //     if ($division_type_id == $value) {
                //         $exists = LineDivision::where("division_type_id", $value)->where("line_id", $element['line'])->exists();
                //         if ($exists)
                //             $fail("The " . $attribute . " " . ErrorMessages::where("slug", "unique")->value("message_en"));
                //     }
                // }
            ],
            'line' => ['required', 'numeric', 'exists_not_soft_deleted:lines,id'],
            'division_name' => ['required', 'string'],
            'division_from_date' => ['required', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'division_to_date' => ['nullable', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT, 'after:division_from_date'],
            'is_kol' => ['numeric', 'in:0,1'],
            'll' => ['nullable'],
            'lg' => ['nullable'],
        ];
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row): bool
    {
        try {
            LineDivisionType::where('divisiontype_id', $row['line_division_type_id'])
                ->where('line_id', $row['line'])
                ->firstOr(function () use ($row) {
                    $type = LineDivisionType::create([
                        'divisiontype_id' => $row['line_division_type_id'],
                        'line_id' => $row['line'],
                        'from_date' => $row['division_from_date'],
                        'to_date' => $row['division_to_date'],
                        'file_id' => $this->file_id,
                    ]);
                    Planable::insert([
                        'line_id' => $row['line'],
                        'planable_id' => $row['line_division_type_id'],
                        'planable_type' => DivisionType::class,
                    ]);

                    if ($type->divisiontype->last_level !== 1) {
                        Approvable::insert([
                            'line_id' => $row['line'],
                            'approvable_id' => $row['line_division_type_id'],
                            'approvable_type' => DivisionType::class,
                        ]);
                    }

                    return $type;
                });
            $this->bulkInsert([
                'line_id' => $row['line'],
                'division_type_id' => $row['line_division_type_id'],
                'name' => $row['division_name'],
                'is_kol' => $row['is_kol'],
                'll' => $row['ll'],
                'lg' => $row['lg'],
                'from_date' => $row['division_from_date'],
                'to_date' => $row['division_to_date'],
                'file_id' => $this->file_id,
                'created_at' => now(),
                'updated_at' => now(),
            ], LineDivision::class);
        } catch (\Exception $e) {
            return false;
        }

        Cache::remember(
            "line_divisions_inserted_in_file:" . $this->file_id,
            now()->addHours(2),
            fn () => ModelsImported::insert([
                [
                    'files_imported_id' => $this->file_id,
                    'model_type' => ('App\LineDivision'),
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'files_imported_id' => $this->file_id,
                    'model_type' => LineDivisionType::class,
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'files_imported_id' => $this->file_id,
                    'model_type' => Planable::class,
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'files_imported_id' => $this->file_id,
                    'model_type' => Approvable::class,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            ])
        );


        return true;
    }
}
