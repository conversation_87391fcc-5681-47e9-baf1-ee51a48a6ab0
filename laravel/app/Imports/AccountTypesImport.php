<?php

namespace App\Imports;

use App\AccountType;
use App\Helpers\ExcelImport;
use App\ModelsImported;
use App\Shift;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class AccountTypesImport extends ExcelImport
{


    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */

    public function rules(array $row): array
    {
        return [
            'name' => ["required", "string", "unique:account_types,name"],
            "sort" => ["nullable", "numeric", "unique:account_types,sort"],
            "shift_id" => ["numeric"]
        ];
    }

    public function model(array $row): bool
    {

        try {
            $this->bulkInsert([
                'file_id' => $this->file_id,
                'name' => $row['name'],
                'notes' => $row['notes'],
                'sort' => $row['sort'],
                'parent_id' => $row['parent_id'] ?: 0,
                'shift_id' => $row['shift_id'],
                'created_at' => now(),
                'updated_at' => now(),
            ],AccountType::class);
        } catch (\Exception $e) {
            return false;
        }
        Cache::remember(
            "account_type_inserted_in_file:" . $this->file_id,
            now()->addHours(2),
            fn() => ModelsImported::insert([
                'files_imported_id' => $this->file_id,
                'model_type' => ('App\AccountType'),
                'created_at' => now(),
                'updated_at' => now(),
            ])
        );
        return true;
    }
}
