<?php

namespace App\Imports;

use App\Account;
use App\Helpers\ExcelImport;
use App\Mapping;
use App\Models\LinkedPharmacy;
use App\ModelsImported;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class LinkedPharmaciesImport extends ExcelImport
{
    /**
     * @return array
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function rules(array $row): array
    {
        return [
            'account_id' => ['required', 'exists_not_soft_deleted:accounts,id', function ($attribute, $value, $fail) use ($row) {
                $exists = Cache::remember(
                    "account_get_with_account_id:" . $row['account_id'].'_code:'.$row['code'],
                    now()->addHours(2),
                    fn() =>  LinkedPharmacy::where('account_id', $row['account_id'])->where('pharmable_id', $row['code'])->exists()
                );
                if ($exists) $fail('This Account linked to this pharmacy');
            }],
            'code' => ['required', 'exists:mappings,code'],
            'ratio' => ['required'],
        ];
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row): bool
    {
        $mapping=Cache::remember(
            "mapping_get_with_code:" . $row['code'],
            now()->addHours(2),
            fn() =>  Mapping::where('code', $row['code'])->first()
        );

        try {
            LinkedPharmacy::firstOrCreate([
                'account_id' => $row['account_id'],
                'pharmable_id' => $mapping->id,
                'pharmable_type' => Mapping::class,

            ],[
                'user_id' => $this->array['user_id'],
                'ratio' => $row['ratio'],
                'file_id' => $this->file_id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return false;
        }
        Cache::remember(
            "linked_pharmacy_inserted_in_file:" . $this->file_id,
            now()->addHours(2),
            fn() =>  ModelsImported::insert([
                'files_imported_id' => $this->file_id,
                'model_type' => LinkedPharmacy::class,
                'created_at' => now(),
                'updated_at' => now(),
            ])
        );

        return true;
    }
}
