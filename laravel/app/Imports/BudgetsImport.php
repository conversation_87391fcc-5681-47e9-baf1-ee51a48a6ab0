<?php

namespace App\Imports;

use App\Exceptions\CrmException;
use App\Helpers\CrmExcelDate;
use App\ModelsImported;
use App\Helpers\ExcelImport;
use App\Models\Budget;
use App\Models\CommercialRequest\CommercialRequest;
use App\Models\Expenses\Expense;
use App\Models\Material;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class BudgetsImport extends ExcelImport
{

    /**
     * @return array
     */
    public function rules(array $row): array
    {
        return [
            "line_id" => ["required", "exists:lines,id"],
            "div_id" => ["required", "exists:line_divisions,id"],
            "product_id" => ["required", "exists:products,id"],
            "type_id" => ["required", "exists:budget_types,id"],
            "sub_type_id" => ["nullable"],
            "budget" => ["required"],
            "from_date" => ["required", 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            "to_date" => ["required", 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
        ];
    }

    /**
     * @param array $row
     *
     * @return bool
     */
    public function model(array $row): bool
    {
        try {
            $this->bulkInsert([
                'file_id' => $this->file_id,
                'line_id' => $row['line_id'],
                'div_id' => $row['div_id'],
                'product_id' => $row['product_id'],
                'type_id' => $row['type_id'],
                'budgetable_id' => $row['sub_type_id'],
                'budgetable_type' => $row['type_id'] == 1
                    ? CommercialRequest::class : ($row['type_id'] == 2
                        ? Expense::class : Material::class),
                'amount' => $row['budget'],
                'from_date' => $row['from_date'],
                'to_date' => $row['to_date'],
                'created_at' => now(),
                'updated_at' => now(),
            ], Budget::class);
        } catch (\Exception $e) {
            Log::error("Budget import failed: " . $e->getMessage());
            return false;
        }
        Cache::remember(
            "budget_inserted_in_file:" . $this->file_id,
            now()->addHours(2),
            fn() => ModelsImported::insert([
                'files_imported_id' => $this->file_id,
                'model_type' => Budget::class,
                'created_at' => now(),
                'updated_at' => now(),
            ])
        );


        return true;
    }
}
