<?php

namespace App\Imports;

use App\MappingUnifiedCode;
use App\Helpers\ExcelImport;
use App\ModelsImported;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class MappingUnifiedCodesImport extends ExcelImport
{

    /**
     * @return array
     */
    public function rules(array $row): array
    {
        return [
            "code" => ["required", "numeric", "unique:mapping_unified_codes,code"],
            "name" => ["required"],
            "address" => ["nullable", "string"],
            "district" => ["nullable", "string"],
            "city" => ["nullable", "string"],
            "mobile" => ["nullable", "string"],
            "notes" => ["nullable", "string"],
            "tax_number" => ["nullable", "numeric"],
            "commercial_number" => ["nullable", "numeric"],
            "div_id" => ["nullable", "numeric"],
            "brick_id" => ["nullable", "numeric", 'exists_not_soft_deleted:bricks,id'],
            "type_id" => ["nullable", "numeric", 'exists:unified_pharmacy_types,id']
        ];
    }

    /**
     * @param array $row
     *
     * @return bool
     */
    public function model(array $row): bool
    {
        try {
            $this->bulkInsert([
                'file_id' => $this->file_id,
                'name' => $row['name'],
                'type_id' => $row['type_id'],
                'code' => $row['code'],
                'address' => $row['address'],
                'district' => $row['district'],
                'city' => $row['city'],
                'mobile' => $row['mobile'],
                'notes' => $row['notes'],
                'tax_number' => $row['tax_number'],
                'commercial_number' => $row['commercial_number'],
                'div_id' => $row['div_id'],
                'brick_id' => $row['brick_id'],
                'created_at' => now(),
                'updated_at' => now(),
            ], MappingUnifiedCode::class);
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return false;
        }
        Cache::remember(
            "mapping_with_unified_codes_in_file:" . $this->file_id,
            now()->addHours(2),
            fn() => ModelsImported::insert([
                'files_imported_id' => $this->file_id,
                'model_type' => MappingUnifiedCode::class,
                'created_at' => now(),
                'updated_at' => now(),
            ])
        );

        return true;
    }
}
