<?php

namespace App\Imports;

use App\Doctor;
use App\Family;
use App\Helpers\ExcelImport;
use App\ModelsImported;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class FamiliesImport extends ExcelImport
{

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */

    public function rules(array $row): array
    {
        return [
            'name' => ["required", "string", "unique:families,name"],
            "sort" => ["nullable", "numeric", "unique:families,sort"],
        ];
    }

    public function model(array $row): bool
    {
        try {
            $this->bulkInsert([
                'file_id' => $this->file_id,
                'name' => $row['name'],
                'notes' => $row['notes'],
                'sort' => $row['sort'],
                'created_at' => now(),
                'updated_at' => now(),
            ],Family::class);
        } catch (\Exception $e) {
            return false;
        }
        Cache::remember(
            "families_inserted_in_file:" . $this->file_id,
            now()->addHours(2),
            fn() =>   ModelsImported::insert([
                'files_imported_id' => $this->file_id,
                'model_type' => ('App\Family'),
                'created_at' => now(),
                'updated_at' => now(),
            ])
        );
        return true;
    }
}
