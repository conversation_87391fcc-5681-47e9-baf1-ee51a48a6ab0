<?php

namespace App\Imports\Expenses\PerLocation;

use App\Brand;
use App\Helpers\ExcelImport;
use App\Models\Expenses\PerLocation\ExpenseLocationSetting;
use App\ModelsImported;
use Illuminate\Support\Facades\DB;

class ExpenseLocationSettingsImport extends ExcelImport
{

    /**
     * @return array
     */
    public function rules(array $row): array
    {
        return [
            'name' => [
                "required", "string",
                // "unique:expense_location_settings,name"
            ],
            "sort" => ["nullable", "numeric", "unique:expense_location_settings,sort"]
        ];
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row): bool
    {
        try {

            ExpenseLocationSetting::insert([
                'file_id' => $this->file_id,
                'id' => $row['id'],
                'name' => $row['name'],
                'notes' => $row['notes'],
                'sort' => $row['sort'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } catch (\Exception $e) {
            return false;
        }
        ModelsImported::insert([
            'files_imported_id' => $this->file_id,
            'model_type' => ExpenseLocationSetting::class,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        return true;
    }
}
