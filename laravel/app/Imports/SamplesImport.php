<?php


namespace App\Imports;

use App\Helpers\CrmExcelDate;
use App\Helpers\ExcelImport;
use App\Models\ProductSample;
use App\Models\Sample;
use App\ModelsImported;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SamplesImport extends ExcelImport
{

    public function rules(array $row): array
    {
        return [
            'line_division_id' => ["required", "integer", "exists_not_soft_deleted:line_divisions,id"],
            "product_id" => ["required", "integer", "exists_not_soft_deleted:products,id"],
            "quantity" => ["required", "integer"],
            "date" => ["required", 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
        ];
    }

    public function model(array $row): bool
    {
        try {
            $sample = Sample::firstOrCreate([
                'file_id' => $this->file_id,
                'user_id' => $this->array['user_id'],
            ], [
                'date' => $row['date'],
                'total_samples' => 0,
            ]);
            Log::info($sample);
            $this->bulkInsert([
                'sample_id'     => $sample->id,
                'line_division_id'     => $row['line_division_id'],
                'product_id' => $row['product_id'],
                'quantity' => $row['quantity'],
                'edited_quantity' => $row['quantity'],
                'date'    => $row['date'],
                'file_id' => $this->file_id,
                'created_at' => now(),
                'updated_at' => now(),
            ], ProductSample::class);
        } catch (\Exception $e) {
            Log::error($e);
            return false;
        }
        Cache::remember(
            "samples_inserted_in_file:" . $this->file_id,
            now()->addHours(2),
            fn() => ModelsImported::insert([
                'files_imported_id' => $this->file_id,
                'model_type' => Sample::class,
                'created_at' => now(),
                'updated_at' => now(),
            ])
        );

        return true;
    }
}
