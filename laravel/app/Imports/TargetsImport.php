<?php

namespace App\Imports;

use App\Contribution;
use App\Exceptions\CrmException;
use App\Helpers\CrmExcelDate;
use App\Helpers\ExcelImport;
use App\Helpers\ExcelNormalUploader;
use App\LineDivision;
use App\ModelsImported;
use App\Product;
use App\SalesSetting;
use App\Target;
use App\TargetDetails;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class TargetsImport extends ExcelImport
{

    public function rules(array $row): array
    {
        $cotribution_per_line_or_product = SalesSetting::where('key', 'contribution_per_product_or_line')
            ->value('value') == 'Product' ? 'Product' : 'Line';
        $fromToManyToManyValidation = function ($attribute, $value, $fail, $model, $id, $key, $message) use ($row) {
            $MiddleModelItem = resolve($model)::where($id, '=', $row[$key])->first()
                ->allLines()->wherePivot("line_id", "=", $row["line_id"])
                ->where('line_products.from_date', '<=', Carbon::parse($value))
                ->where(fn($q) => $q->where('line_products.to_date', '>', Carbon::parse($value))
                    ->orWhere('line_products.to_date', null))
                ->first();
            $from = $MiddleModelItem?->from_date ? Carbon::createFromFormat(CrmExcelDate::OFFICIAL_FORMAT, $MiddleModelItem->from_date) : null;
            $to = $MiddleModelItem?->to_date ? Carbon::createFromFormat(CrmExcelDate::OFFICIAL_FORMAT, $MiddleModelItem->to_date) : null;
            $valueDate = Carbon::createFromFormat(CrmExcelDate::OFFICIAL_FORMAT, $value);

            if (!$from || !($valueDate->gte($from) || $valueDate->lte($to)))
                return $fail('the ' . $attribute . $message);
        };
        return [
            'line_id' => ['required', 'exists_not_soft_deleted:lines,id'],
            'type_id' => ['nullable', 'exists_not_soft_deleted:target_types,id'],
            'product' => ['required', 'exists_not_soft_deleted:products,id'],
            'quantity' => ['required', 'numeric'],
            'value' => ['nullable'],
            'date' => [
                'required',
                'date_format:' . CrmExcelDate::OFFICIAL_FORMAT,
                function ($attribute, $value, $fail) use ($fromToManyToManyValidation) {
                    $model = Product::class;
                    $id = 'id';
                    $key = 'product';
                    $message = ' Target must be in range of date of linked product .';
                    $fromToManyToManyValidation($attribute, $value, $fail, $model, $id, $key, $message);
                },
                function ($attribute, $value, $fail) use ($row, $cotribution_per_line_or_product) {
                    $targetMonth = Carbon::parse($value)->format('m');
                    $targetYear = Carbon::parse($value)->format('Y');
                    $exists = Contribution::whereMonth('date', $targetMonth)
                        ->whereYear('date', $targetYear)
                        ->where('line_id', $row['line_id'])
                        ->where('type_id', $row['type_id']);
                    if ($cotribution_per_line_or_product == 'Product') {
                        $exists = $exists->where('product_id', $row['product']);
                    }
                    $exists = $exists->exists();
                    if (!$exists)
                        $fail("This data doesn't have any contribution yet.");
                }
            ],
        ];
    }


    public function model(array $row): bool
    {
        $cotribution_per_line_or_product = Cache::remember(
            "contribution_per_product_or_line:" . $this->file_id,
            now()->addHours(2),
            fn() => SalesSetting::where('key', 'contribution_per_product_or_line')
                ->value('value') == 'Product' ? 'Product' : 'Line'
        );
        try {
            $target = Target::create([
                'line_id' => $row['line_id'],
                'type_id' => $row['type_id'],
                'product_id' => $row['product'],
                'quantity' => $row['quantity'],
                'value' => $row['value'],
                'date' => $row['date'],
                'file_id' => $this->file_id
            ]);
            $targetMonth = Carbon::parse($target->date)->format('m');
            $targetYear = Carbon::parse($target->date)->format('Y');
            $contributions = Contribution::whereMonth('date', $targetMonth)
                ->whereYear('date', $targetYear)
                ->where('line_id', $target->line_id);
            if ($cotribution_per_line_or_product == 'Product') {
                $contributions = $contributions->where('product_id', $target->product_id);
            }
            $contributions = $contributions->get();

            foreach ($contributions as $contribution) {
                $targetDetails[] = [
                    'line_id' => $target->line_id,
                    'type_id' => $target->type_id,
                    'target_id' => $target->id,
                    'date' => $target->date,
                    'product_id' => $target->product_id,
                    'div_id' => $contribution->div_id,
                    'brick_id' => $contribution->brick_id,
                    'target' => ($target->quantity * $contribution->contribution) / 100,
                    'value' => ($target->value * $contribution->contribution) / 100,
                    'file_id' => $this->file_id
                ];
            }
            if (!empty($targetDetails))
                $this->bulkInsert($targetDetails, TargetDetails::class);
        } catch (\Exception $e) {
            return false;
        }
        Cache::remember(
            "targets_in_file:" . $this->file_id,
            now()->addHours(2),
            fn() => ModelsImported::insert([
                'files_imported_id' => $this->file_id,
                'model_type' => Target::class,
                'created_at' => now(),
                'updated_at' => now()
            ])
        );

        return true;
    }
}
