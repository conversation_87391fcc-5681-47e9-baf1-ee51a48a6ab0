<?php


namespace App\Imports\Updates;

use App\Permission;
use App\Helpers\ExcelImport;
use App\Rules\ExcelUnique;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PermissionsImport extends ExcelImport
{

    public function rules(array $row):array
    {
        return [
            'name' => ["nullable", "string", (new ExcelUnique('permissions', collect($row), "name", 'permission_id'))],
            "sort" => ["nullable", "numeric", (new ExcelUnique('permissions', collect($row), "sort", 'permission_id'))],
            "permission_id" => ["required", "exists:permissions,id"]
        ];
    }

    public function model(array $row):bool
    {
        try {
            $permission = Permission::find($row["permission_id"]);

            $permission->update([
                'name'     => $row['name'] ?? $permission->name,
                'notes' => $row['notes'] ?? $permission->notes,
                'sort'    => $row['sort'] ?? $permission->sort,
            ]);
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return false;
        }

        return true;
    }
}
