<?php


namespace App\Imports\Updates;

use App\Speciality;
use App\Helpers\ExcelImport;
use App\Rules\ExcelUnique;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SpecialitiesImport extends ExcelImport
{


    public function rules(array $row): array
    {
        return [
            'name' => ["nullable", "string", (new ExcelUnique('specialities', collect($row), "name", 'speciality_id'))],
            "sort" => ["nullable", "numeric", (new ExcelUnique('specialities', collect($row), "sort", 'speciality_id'))],
            "speciality_id" => ["required", "exists_not_soft_deleted:specialities,id"]
        ];
    }

    public function model(array $row): bool
    {
        try {
            $speciality = Speciality::find($row["speciality_id"]);

            $speciality->update([
                'name'     => $row['name'] ?? $speciality->name,
                'notes' => $row['notes'] ?? $speciality->notes,
                'sort'    => $row['sort'] ?? $speciality->sort,
                'parent_id'    => $row['parent_id'] ?? $speciality->parent_id,
            ]);
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return false;
        }

        return true;
    }
}
