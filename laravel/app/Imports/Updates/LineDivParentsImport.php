<?php

namespace App\Imports\Updates;

use App\Helpers\CrmExcelDate;
use App\Helpers\ExcelImport;
use App\LineDivParent;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LineDivParentsImport extends ExcelImport
{

        /**
     * @return array
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function rules(array $row):array
    {
        return [
            "line_div_parent" => ["required", "exists_not_soft_deleted:line_div_parents,id"],
            "line_division" => ["nullable","numeric","exists_not_soft_deleted:line_divisions,id"],
            "division_parent" => ["nullable","numeric","exists_not_soft_deleted:line_divisions,id"],
            "from_date" => ["nullable","date_format:".CrmExcelDate::OFFICIAL_FORMAT],
            "to_date" => ["nullable","date_format:".CrmExcelDate::OFFICIAL_FORMAT,"after:form_date"],
        ];
    }

    public function model(array $row):bool
    {
        try {
            $lineDivParent = LineDivParent::find($row["line_div_parent"]);

            $lineDivParent->update([
                "line_div_id" => $row["line_division"] ?? $lineDivParent->line_div_id,
                "parent_id" => $row["division_parent"] ?? $lineDivParent->parent_id,
                "from_date" => $row["from_date"] ?? $lineDivParent->from_date,
                "to_date" => $row["to_date"] ?? $lineDivParent->to_date,
            ]);
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return false;
        }

        return true;
    }

}
