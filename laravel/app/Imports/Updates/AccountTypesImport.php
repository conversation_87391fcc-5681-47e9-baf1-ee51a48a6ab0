<?php


namespace App\Imports\Updates;

use App\AccountType;
use App\Helpers\ExcelImport;
use App\Rules\ExcelUnique;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AccountTypesImport extends ExcelImport
{


    public function rules(array $row): array
    {
        return [
            "name" => ["nullable", "string", (new ExcelUnique("account_types", collect($row), "name", "account_type_id"))],
            "sort" => ["nullable", "numeric", (new ExcelUnique("account_types", collect($row), "sort", "account_type_id"))],
            "account_type_id" => ["required", "exists_not_soft_deleted:account_types,id"],
            "shift_id"  => ["nullable", "integer", "min:1", "exists_not_soft_deleted:shifts,id"],
            "parent_id" => ["nullable", "integer", "min:0"],
        ];
    }

    public function model(array $row): bool
    {

        try {
            $accountType = AccountType::find($row["account_type_id"]);

            $accountType->update([
                "name"     => $row["name"] ?? $accountType->name,
                "notes" => $row["notes"] ?? $accountType->notes,
                "sort"    => $row["sort"] ?? $accountType->sort,
                "shift_id"    => $row["shift_id"] ?? $accountType->shift_id,
                "parent_id"    => $row["parent_id"] ?? $accountType->parent_id,
            ]);
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return false;
        }

        return true;
    }
}
