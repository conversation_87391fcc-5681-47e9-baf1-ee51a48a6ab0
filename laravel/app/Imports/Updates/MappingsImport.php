<?php

namespace App\Imports\Updates;


use App\Helpers\CrmExcelDate;
use App\Helpers\ExcelImport;
use App\LineBricks;
use App\Mapping;
use App\MappingDetail;
use App\ModelsImported;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class MappingsImport extends ExcelImport
{
    /**
     * @return array
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function rules(array $row): array
    {
        $rules = [
            'id' => ['required', 'exists:mappings,id'],
            'name' => ['nullable', 'string', 'max:255'],
        ];
        if (!empty($row["uc_code"])) {
            $rules['uc_code'] = ['required', 'string'];
        } else if (!empty($row["brick"])) {
            $rules["brick"] = [
                'required',
                'exists:bricks,id',
                function ($attribute, $value, $fail) use ($row) {
                    $mapping = Mapping::whereId($row['id'])->first();
                    if ($mapping->line_id == null) return;
                    $exists = $mapping->lineBricks()
                        ->where('line_bricks.line_id', $mapping->line_id)
                        ->where('line_bricks.brick_id', $value)
                        ->exists();
                    if (!$exists)
                        $fail('the ' . $attribute . ' must be linked to its related line.');
                }
            ];
            $rules['effect_date'] = ['required', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT];
        } else if (!empty($row['exception'])) {
            $rules['exception'] = ['required', 'string', Rule::in(['true', 'false'])];
        }
        return $rules;
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row): bool
    {
        try {

            if (!empty($row['uc_code'])) {
                Mapping::whereId($row['id'])
                    ->update([
                        "uc_code" => $row['uc_code'],
                    ]);
            }

            if (!empty($row['brick']) && !empty($row['effect_date'])) {
                $mapping = Mapping::whereId($row['id'])->first();
                $mapping->details()->update([
                    'brick_id' => $row['brick'],
                ]);

                $mapping->salesDetails()
                    ->whereDate('sales_details.date', '>=', $row['effect_date'])
                    ->get()->each(function ($detail) use ($row) {
                        Log::info($detail, ["row_id" => $row['id'], "detail_id" => $detail->id]);
                        $lineBrick = Cache::remember(
                            'file: ' . $this->file_id . ' line: ' . $detail->line_id . ' brick: ' . $row['brick'],
                            now()->addHours(2),
                            fn() => LineBricks::where([
                                "line_id" => $detail->line_id,
                                "brick_id" => $row["brick"],
                            ])
                                ->where('from_date', '<=', $row['effect_date'])
                                ->where(
                                    fn($q) => $q->where('to_date', '>', $row['effect_date'])
                                        ->orWhere('to_date', null)
                                )
                                ->first()
                        );
                        Log::info($lineBrick, ["row_id" => $row['id'], "detail_id" => $detail->id, "line_brick_id" => $lineBrick->id]);


                        if ($lineBrick == null) return;

                        $detail->update([
                            "div_id" => $lineBrick->line_division_id,
                            "brick_id" => $row['brick'],
                        ]);
                        Log::info($detail, ["row_id" => $row['id'], "detail_id" => $detail->id, "line_brick_id" => $lineBrick->id, "update" => true]);
                    });
            }

            if (!empty($row['name'])) {
                Mapping::whereId($row['id'])
                    ->update([
                        "name" => $row['name'],
                    ]);
            }

            if (!empty($row['exception'])) {
                Mapping::whereId($row['id'])
                    ->update([
                        "exception" => $row['exception'] == 'true',
                    ]);
            }

        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return false;
        }
        Cache::remember(
            "mapping_with_distributor_in_file:" . $this->file_id,
            now()->addHours(2),
            fn() => ModelsImported::insert([
                [
                    'files_imported_id' => $this->file_id,
                    'model_type' => Mapping::class
                ],
                [
                    'files_imported_id' => $this->file_id,
                    'model_type' => MappingDetail::class
                ]
            ])
        );

        return true;
    }
}
