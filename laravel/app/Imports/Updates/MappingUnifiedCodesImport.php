<?php

namespace App\Imports\Updates;

use App\MappingUnifiedCode;
use App\Helpers\ExcelImport;
use App\ModelsImported;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class MappingUnifiedCodesImport extends ExcelImport
{

    /**
     * @return array
     */
    public function rules(array $row): array
    {
        return [
            "code" => ["nullable", "numeric", "unique:mapping_unified_codes,code"],
            "name" => ["nullable"],
            "address" => ["nullable", "string"],
            "district" => ["nullable", "string"],
            "city" => ["nullable", "string"],
            "mobile" => ["nullable", "string"],
            "notes" => ["nullable", "string"],
            "tax_number" => ["nullable", "numeric"],
            "commercial_number" => ["nullable", "numeric"],
            "div_id" => ["nullable", "numeric"],
            "brick_id" => ["nullable", "numeric", 'exists_not_soft_deleted:bricks,id'],
            "type_id" => ["nullable", "numeric", 'exists:unified_pharmacy_types,id']
        ];
    }

    /**
     * @param array $row
     *
     * @return bool
     */

    public function model(array $row): bool
    {
        try {
            Log::info($row);
            $unifiedCode = MappingUnifiedCode::find($row["mapping_unified_id"]);
            // Log::info($unifiedCode);
            $unifiedCode->update([
                "type_id"     => $row["type_id"] ?? $unifiedCode->type_id,
                "div_id"     => $row["div_id"] ?? $unifiedCode->div_id,
                "brick_id"     => $row["brick_id"] ?? $unifiedCode->brick_id,
                "code"     => $row["code"] ?? $unifiedCode->code,
                "name"     => $row["name"] ?? $unifiedCode->name,
                "address"     => $row["address"] ?? $unifiedCode->address,
                "district"     => $row["district"] ?? $unifiedCode->district,
                "city"     => $row["city"] ?? $unifiedCode->city,
                "mobile"     => $row["mobile"] ?? $unifiedCode->mobile,
                "notes"     => $row["notes"] ?? $unifiedCode->notes,
                "tax_number"     => $row["tax_number"] ?? $unifiedCode->tax_number,
                "commercial_number"     => $row["commercial_number"] ?? $unifiedCode->commercial_number,
            ]);
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return false;
        }

        return true;
    }
}
