<?php

namespace App\Imports\Updates\Product;

use App\Helpers\ExcelImport;
use App\Models\Product\ProductMessage;
use App\Rules\ExcelUnique;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProductMessagesImport extends ExcelImport
{


    public function rules(array $row):array
    {
        return [
            "name" => ["nullable", "string", (new ExcelUnique("product_messages", collect($row), "name", "id"))],
            "sort" => ["nullable", "numeric", (new ExcelUnique("product_messages", collect($row), "sort", "id"))],
            "id" => ["required", "exists_not_soft_deleted:product_messages,id"]
        ];
    }

    public function model(array $row):bool
    {
        try {
            $message = ProductMessage::find($row["id"]);

            $message->update([
                "message"     => $row["message"] ?? $message->message,
                "sort"    => $row["sort"] ?? $message->sort,
            ]);
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return false;
        }

        return true;
    }
}
