<?php


namespace App\Imports\Updates;

use App\Country;
use App\Helpers\ExcelImport;
use App\Rules\ExcelUnique;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CountriesImport extends ExcelImport
{

    public function rules(array $row):array
    {
        return [
            "name" => ["nullable", "string", (new ExcelUnique("countries", collect($row), "name", "country_id"))],
            "sort" => ["nullable", "numeric", (new ExcelUnique("countries", collect($row), "sort", "country_id"))],
            "country_id" => ["required", "exists_not_soft_deleted:countries,id"]
        ];
    }

    public function model(array $row):bool
    {
        try {
            $country = Country::find($row["country_id"]);

            $country->update([
                "name"     => $row["name"] ?? $country->name,
                "notes" => $row["notes"] ?? $country->notes,
                "sort"    => $row["sort"] ?? $country->sort,
            ]);
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return false;
        }

        return true;
    }
}
