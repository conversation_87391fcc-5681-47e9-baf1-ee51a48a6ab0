<?php

namespace App\Imports\Updates;

use App\Classes;
use App\Helpers\ExcelImport;
use App\Rules\ExcelUnique;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ClassesImport extends ExcelImport
{


    public function rules(array $row):array
    {
        return [
            "name" => ["nullable", "string", (new ExcelUnique("classes", collect($row), "name", "class_id"))],
            "sort" => ["nullable", "numeric", (new ExcelUnique("classes", collect($row), "sort", "class_id"))],
            "class_id" => ["required", "exists_not_soft_deleted:classes,id"]
        ];
    }


    public function model(array $row):bool
    {
        try {
            $class = Classes::find($row["class_id"]);

            $class->update([
                "name"     => $row["name"] ?? $class->name,
                "notes" => $row["notes"] ?? $class->notes,
                "sort"    => $row["sort"] ?? $class->sort,
            ]);
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return false;
        }

        return true;
    }
}
