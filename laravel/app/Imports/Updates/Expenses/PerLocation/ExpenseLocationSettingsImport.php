<?php


namespace App\Imports\Updates\Expenses\PerLocation;

use App\Helpers\ExcelImport;
use App\Models\Expenses\PerLocation\ExpenseLocationSetting;
use App\Rules\ExcelUnique;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ExpenseLocationSettingsImport extends ExcelImport
{

    public function rules(array $row): array
    {
        return [
            "name" => ["nullable", "string", (new ExcelUnique("expense_location_settings", collect($row), "name", "expense_location_setting_id"))],
            "sort" => ["nullable", "numeric", (new ExcelUnique("expense_location_settings", collect($row), "sort", "expense_location_setting_id"))],
            "expense_location_setting_id" => ["required", "exists_not_soft_deleted:expense_location_settings,id"]
        ];
    }

    public function model(array $row): bool
    {

        try {
            $setting = ExpenseLocationSetting::find($row["expense_location_setting_id"]);

            $setting->update([
                "name"     => $row["name"] ?? $setting->name,
                "notes" => $row["notes"] ?? $setting->notes,
                "sort"    => $row["sort"] ?? $setting->sort,
            ]);
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return false;
        }
        return true;
    }
}
