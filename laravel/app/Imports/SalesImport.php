<?php

namespace App\Imports;


use App\Contribution;
use App\Distributor;
use App\Helpers\CrmExcelDate;
use App\Helpers\ExcelImport;
use App\LineBricks;
use App\LineProduct;
use App\Mapping;
use App\Models\{PharmacyType, SaleFactor, UnifiedBrickMapping};
use App\ModelsImported;
use App\Product;
use App\Rules\{CompositeExists, IsDuplicatedMapping, UnifiedBrickExists, IsProductLinesIntersectedWithDistributorLines};
use App\Sale;
use App\SaleDetail;
use App\SalesSetting;
use App\Services\{Enums\Ceiling, Enums\SaleDistribution, SalesService, UnifiedService};
use Illuminate\Support\{Carbon, Collection, Facades\Cache, Facades\DB, Facades\Log, Facades\Hash};

class SalesImport extends ExcelImport
{
    private const CACHE_HOURS = 2;
    private Distributor $distributor;
    private array $config = [];
    private array $row;


    public function __construct(...$params)
    {
        parent::__construct(...$params);

        $importData = $params[array_key_last($params)];

        $this->initializeConfiguration($importData);
    }


    private function initializeConfiguration(array $importData): void
    {
        $this->distributor = $this->fetchDistributor($importData['distributor']);
        $this->config = [
            'type' => $importData['type'],
            'date' => $importData['date'],
            'mode' => $importData['mode'],
            'salesMappingLevel' => $this->getCachedSetting('sales_mapping_level'),
            'postMapping' => $this->getCachedSetting('post_mapping') === 'Yes',
            'salesContribution' => explode(',', $this->getCachedSetting('sales_contribution_base_on')),
            'unifiedSales' => $this->getCachedSetting('unified_sales') === 'Yes',
            'mappingWithDistributor' => $this->getCachedSetting('mapping_with_distributor') === 'Yes'
        ];
    }

    private function getCachedSetting(string $key)
    {
        return Cache::remember(
            $key,
            now()->addHours(self::CACHE_HOURS),
            fn() => (string)SalesSetting::where('key', $key)->value('value')
        );
    }

    private function fetchDistributor(int $distributorId)
    {
        return Cache::remember(
            "distributor:{$distributorId}",
            now()->addHours(self::CACHE_HOURS),
            fn() => Distributor::whereId($distributorId)->with("lines")->first()
        );
    }

    private function getBrickValidationRules(): array
    {
        $rules = ['required'];

        if ($this->config['unifiedSales'] && $this->distributor->hasUnifiedDistributor()) {
            $rules[] = new UnifiedBrickExists($this->distributor);
        }

        if ((!$this->config['postMapping'] && $this->config['mappingWithDistributor'])
            && (!$this->config['unifiedSales'] || !$this->distributor->hasUnifiedDistributor())
        ) {
            $rules[] = 'exists_not_soft_deleted:mappings,code';
            //            $rules[] = new IsDuplicatedMapping(
            //                $this->config['mappingWithDistributor'],
            //                $this->distributor,
            //                $this->file_id,
            //                self::CACHE_HOURS,
            //            );
            $rules[] = new CompositeExists(
                Mapping::class,
                self::CACHE_HOURS,
                $this->file_id,
                [
                    'mapping_type_id' => $this->config['type'],
                    'distributor_id' => $this->distributor->id,
                ],
                'code'
            );
        }

        return $rules;
    }

    private function getBrickIdValidationRules(): array
    {
        return [
            $this->config['postMapping'] ? 'required' : 'nullable',
            $this->config['salesMappingLevel'] === 'Brick'
                ? 'exists_not_soft_deleted:bricks,id'
                : 'exists_not_soft_deleted:line_divisions,id'
        ];
    }

    private function getProductValidationRules(): array
    {
        return [
            'required',
            'exists_not_soft_deleted:product_distributors,code',
            new IsProductLinesIntersectedWithDistributorLines(
                $this->config['date'],
                $this->distributor,
                $this->file_id,
                self::CACHE_HOURS
            )
        ];
    }

    /**
     * @return array
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function rules(array $row): array
    {
        $brickRules = $this->getBrickValidationRules();

        return [
            'brick' => $brickRules,
            'brick_id' => $this->getBrickIdValidationRules(),
            'sales' => ['required', 'numeric', 'max:9999999'],
            'bonus' => ['required', 'numeric'],
            'region' => ['required', 'numeric'],
            'product' => $this->getProductValidationRules(),
            'value' => ['required', 'numeric', 'max:99999999'],
            'date' => ['nullable', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
        ];
    }


    public function model(array $row): bool
    {
        try {
            $product = $this->fetchProduct($row);
            $sale = $this->createSale($row, $product);

            $this->row = $row;

            $this->processSalesDetails($row, $sale, $product);
            $this->recordImportedModels();

            return true;
        } catch (\Exception $e) {
            Log::channel('sales-import')->error('Sales Import Error: ' . $e->getMessage());
            Log::channel('sales-import')->error('Sales Import Error Line: ' . $e->getLine());

            return false;
        }
    }

    private function fetchProduct(array $row): Product
    {
        return Cache::remember(
            "file_id:{$this->file_id}_product_code:{$row['product']}_distributor_id:{$this->distributor->id}",
            now()->addHours(self::CACHE_HOURS),
            fn() => $this->distributor
                ->products()
                ->with([
                    "allLines" => fn($q) => $q
                        ->where("line_products.from_date", "<=", $this->config['date'])
                        ->where(
                            fn($q) => $q
                                ->where("line_products.to_date", ">", $this->config['date'])
                                ->orWhere("line_products.to_date", null)
                        )
                ])
                ->wherePivot("code", "=", $row['product'])
                ->first()
        );
    }

    private function createSale(array $row, Product $product): Sale
    {
        return Sale::create([
            'quantity' => $row['sales'],
            'bonus' => $row['bonus'],
            'region' => $row['region'],
            'distributor_id' => $this->distributor->id,
            'product_id' => $product->id,
            'value' => $row['value'],
            'date' => $row['date'] ?? $this->config['date'],
            'file_id' => $this->file_id,
        ]);
    }

    private function processSalesDetails(array $row, Sale $sale, Product $product): void
    {

        $this->config['unifiedSales'] && $this->distributor->hasUnifiedDistributor()
            ? $this->applyUnifiedSales($row, $product, $sale)
            : $this->applyNonUnifiedSales($row, $product, $sale);
    }

    private function recordImportedModels(): void
    {
        $this->cacheModelImport(Sale::class, 'sales');
        $this->cacheModelImport(SaleDetail::class, 'sales_details');
    }

    private function cacheModelImport(string $modelClass, string $cachePrefix): void
    {
        Cache::remember(
            "{$cachePrefix}:{$this->file_id}",
            now()->addHours(self::CACHE_HOURS),
            fn() => ModelsImported::insert([
                'files_imported_id' => $this->file_id,
                'model_type' => $modelClass
            ])
        );
    }

    public function applyUnifiedSales(array $row, Product $product, Sale $sale): void
    {
        $unified = new UnifiedService();
        $unibrick = $unified->getUnifiedBrick($this->distributor->unified_distributor_id, $row['brick']);
        $unifiedMappings = UnifiedBrickMapping::where("unified_brick_id", $unibrick['id'])->get();
        $distributorLines = $this->distributor->lines->pluck('id');
        $lineIds = $product->lines()->whereIn('lines.id', $distributorLines)->get()->pluck("id");
        $salesData = [];
        $unifiedMappings->each(
            fn(UnifiedBrickMapping $unifiedMapping) => LineBricks::whereIn("line_id", $lineIds)
                ->where("brick_id", $unifiedMapping->brick_id)
                ->where('line_bricks.from_date', '<=', now())
                ->where(
                    fn($q) => $q->where('line_bricks.to_date', '>', (string)Carbon::now())
                        ->orWhere('line_bricks.to_date', null)
                )
                ->get()
                ->each(function (LineBricks $lineBrick) use ($sale, $unifiedMapping, &$salesData) {
                    $salesData[] = [
                        'sale_id' => $sale->id,
                        'div_id' => $lineBrick->line_division_id,
                        'brick_id' => $unifiedMapping->brick_id,
                        'line_id' => $lineBrick->line_id,
                        'date' => $sale->date,
                        'quantity' => ($unifiedMapping->percentage / 100) * $sale->quantity,
                        'bonus' => ($unifiedMapping->percentage / 100) * $sale->bonus,
                        'value' => ($unifiedMapping->percentage / 100) * $sale->value,
                        'file_id' => $this->file_id,
                        'created_at' => now(),
                        'updated_at' => now(),
                        'ratio' => $unifiedMapping->percentage / 100
                    ];
                })
        );
        if (!empty($salesData))
            SaleDetail::insert($salesData);
    }


    public function applyNonUnifiedSales(array $row, Product $product, Sale $sale): void
    {
        [$lineIds, $mappings] = $this->getMappingsAndLinesIds($row, $product, $sale);

        $sales = [];
        if ($this->config['mode'] === 'Normal') {

            foreach ($mappings as $mapping) {
                $id = $mapping->saleFactors->first()->id;
                Log::info($id);
                $sales = [...$sales, ...match ($id) {
                    SaleFactor::NORMAL => $this->addNormalSales($sale, $product, $mapping, $lineIds),
                    SaleFactor::CR_TARGET => $this->addContributionSales($sale, $product),
                    SaleFactor::CR_SALES, SaleFactor::DIRECT_SALES => $this->addSales($sale, $product, $mapping, $lineIds) //$this->addDirectSales($sale, $product, $mapping, $lineIds, $row['region']),
                }];
            }
        }

        if ($this->config['mode'] === 'Transfer') {
            foreach ($mappings as $mapping) {
                $sales = [
                    ...$sales,
                    ...$this->addNormalSales($sale, $product, $mapping, $lineIds)
                ];
            }
        }

        if (!empty($sales))
            $this->bulkInsert($sales, SaleDetail::class);
    }

    private function getMappingsAndLinesIds($row, Product $product, Sale $sale): array
    {
        $this->addPostMapping($row);
        $mappingsQuery = Mapping::select([
            "id",
            "line_id",
            "code"
        ])->with(["saleFactors", "details:mapping_id,percent,div_id,brick_id"])
            ->where('code', '=', (string)$row['brick'])
            ->where('mapping_type_id', '=', $this->config['type'])
            ->where('mappings.from_date', '<=', now())
            ->where(
                fn($q) => $q->where('mappings.to_date', '>', (string)Carbon::now())
                    ->orWhere('mappings.to_date', null)
            );
        if ($this->config['mappingWithDistributor']) {
            $mappingsQuery = $mappingsQuery->where('distributor_id', '=', $this->distributor->id);
        }
        $mappings = Cache::remember(
            'file_id' . $this->file_id . 'brick_code:' . $row['brick'] . '_mapping_type_id:' . $this->config['type'] . '_distributor_id:' . $this->distributor->id,
            now()->addHours(self::CACHE_HOURS),
            fn() => $mappingsQuery->get()
        );

        $distributorLines = Cache::remember(
            'file_id' . $this->file_id . 'line_ids_on_distributor_id:' . $this->distributor->id,
            now()->addHours(self::CACHE_HOURS),
            fn() => $this->distributor->lines()->get()->pluck('id')
        );
        $lineIds = Cache::remember(
            'file_id' . $this->file_id . 'line_ids_on_product_id:' . $product->id . ':on_distributor_id:' . $this->distributor->id,
            now()->addHours(self::CACHE_HOURS),
            fn() => $product
                ->allLines()
                ->where('line_products.from_date', '<=', $sale->date)
                ->where(fn($q) => $q->where('line_products.to_date', '>', $sale->date)
                    ->orWhere('line_products.to_date', null))
                ->whereIntegerInRaw('lines.id', $distributorLines)->get()->pluck("id")
        );

        $sale->mappings()->sync($mappings->pluck("id")->toArray());
        return [$lineIds, $mappings];
    }


    private function addPostMapping($row): void
    {
        if (!$this->config['postMapping']) return;
        $emptyPharmactType = new PharmacyType();
        $emptyPharmactType->id = null;

        $pharmactType = $row['type']
            ? PharmacyType::firstOrCreate(['name' => $row['type']])
            : $emptyPharmactType;
        $cacheKey = 'file_id:' . $this->file_id . 'brick_code:' . $row['brick'] . '_mapping_type_id:' . $this->config['type'];

        $exists = Cache::remember(
            $cacheKey,
            now()->addHours(self::CACHE_HOURS),
            fn() => Mapping::where('code', $row['brick'])
                ->where('mapping_type_id', $this->config['type'])->exists()
        );
        if ($exists) return;

        DB::statement('CALL create_normal_mapping(?)', [
            json_encode([
                'code' => $row['brick'],
                'mapping_type_id' => $this->config['type'],
                'line_id' => null,
                'pharmacy_type_id' => $pharmactType->id,
                'distributor_id' => $this->config['mappingWithDistributor'] ? $this->distributor->id : null,
                'name' => $row['brick_name'],
                'address' => $row['address'],
                'from_date' => Carbon::now()->firstOfYear()->toDateTimeString(),
                'to_date' => null,
                'file_id' => $this->file_id,
                'div_id' => $this->config['salesMappingLevel'] === 'Division' ? $row['brick_id'] : null,
                'brick_id' => $this->config['salesMappingLevel'] === 'Brick' ? $row['brick_id'] : null,
                'percent' => 100,
            ])
        ]);

        Cache::put($cacheKey, true);
    }


    private function addNormalSales(Sale $sale, Product $product, Mapping $mapping, Collection $lineIds): array
    {
        $salesData = [];
        $intersectedLineIds = $this->intersectLine($lineIds, $mapping);

        foreach ($intersectedLineIds as $lineId) {
            $salesData = array_merge(
                $salesData,
                $this->config['salesMappingLevel'] === 'Division'
                    ? $this->processDivisionSales($sale, $product, $lineId, $mapping)
                    : $this->processBrickSales($sale, $product, $lineId, $mapping)
            );
        }

        return $salesData;
    }

    private function processDivisionSales(Sale $sale, Product $product, int $lineId, Mapping $mapping): array
    {
        $salesData = [];
        $productRatio = $this->getProductRatio($product, $lineId) / 100;
        foreach ($mapping->details as $item) {
            $sharablePercentage = $productRatio * ($item->percent / 100);
            $salesData[] = $this->addDetails(
                $sale,
                $lineId,
                null,
                $item->div_id,
                $sharablePercentage
            );
        }
        return $salesData;
    }

    private function getProductRatio(Product $product, int $lineId)
    {
        return $product->allLines->where("id", $lineId)->first()?->pivot->ratio ?? 100;
    }

    private function processBrickSales(Sale $sale, Product $product, int $lineId, Mapping $mapping): array
    {
        $salesData = [];
        $saleDate = Carbon::parse($sale->date);
        $productRatio = $this->getProductRatio($product, $lineId) / 100;

        foreach ($mapping->details as $item) {

            $brickId = $this->config['mode'] == 'Transfer' ? $this->row['brick_id'] : $item->brick_id;

            $divisions = $this->getDivisionsForBrick($product, $lineId, $brickId, $saleDate);

            foreach ($divisions as $division) {
                $sharablePercentage = ($division->ratio / 100) * ($item->percent / 100) * $productRatio;
                $salesData[] = $this->addDetails(
                    $sale,
                    $lineId,
                    $brickId,
                    $division->line_division_id,
                    $sharablePercentage
                );
            }
        }

        return $salesData;
    }

    private function getDivisionsForBrick(Product $product, int $lineId, int $brickId, Carbon $saleDate): Collection
    {
        return Cache::remember(
            "file_id:{$this->file_id}:product:{$product->id}:line:{$lineId}:brick:{$brickId}",
            now()->addHours(self::CACHE_HOURS),
            fn() => $product->lineBricksInDates($saleDate, $saleDate)
                ->where("line_bricks.line_id", $lineId)
                ->where("line_bricks.brick_id", $brickId)
                ->where('line_bricks.from_date', '<=', $saleDate->toDateString())
                ->where(
                    fn($q) => $q->where('line_bricks.to_date', '>', $saleDate->toDateString())
                        ->orWhereNull('line_bricks.to_date')
                )
                ->get()
        );
    }

    private function getProductRatios(Product $product, int $lineId, Carbon $saleDate): Collection
    {
        return Cache::remember(
            "file_id:{$this->file_id}:product:{$product->id}:line:{$lineId}",
            now()->addHours(self::CACHE_HOURS),
            fn() => LineProduct::where("line_products.line_id", $lineId)
                ->where("line_products.product_id", $product->id)
                ->where('line_products.from_date', '<=', $saleDate->toDateString())
                ->where(
                    fn($q) => $q->where('line_products.to_date', '>', $saleDate->toDateString())
                        ->orWhereNull('line_products.to_date')
                )
                ->get()
        );
    }

    private function addContributionSales(Sale $sale, Product $product): array
    {
        $date = Carbon::parse($this->config['date']);

        $contributions = Contribution::select([
            "div_id",
            "brick_id",
            "contribution"
        ])
            ->where("product_id", $product->id)
            ->whereIn(
                "line_id",
                $product->allLines->pluck('id')
                    ->intersect($this->distributor->lines->pluck("id"))
                    ->toArray()
            )
            ->whereYear("date", $date->year)
            ->whereMonth("date", $date->month)
            ->with(["line", "lineOfDivision"])
            ->get();

        $salesData = [];

        foreach ($contributions as $item) {
            $salesData[] = $this->addDetails(
                $sale,
                $item->line?->id ?? $item->lineOfDivision->id,
                $item->brick_id,
                $item->div_id,
                $item->contribution / 100
            );
        }
        return $salesData;
    }

    private function addSales(Sale $sale, Product $product, Mapping $mapping, Collection $lineIds): array
    {
        $lineId = $this->IntersectLine($lineIds, $mapping);
        if ($lineId->isEmpty()) return [];
        $divisionsBricks = SalesService::make(SaleDistribution::DIRECT)->getRatiosForDistribution(
            $this->config['date'],
            $product->id,
            $this->config['salesContribution']
        );
        $salesData = [];
        Log::info($divisionsBricks);
        foreach ($divisionsBricks as $divisionBrick) {
            $salesData[] = $this->addDetails(
                $sale,
                $mapping->line?->id ?? $divisionBrick->line_id,
                $divisionBrick->brick_id,
                $divisionBrick->div_id,
                $divisionBrick->percentage
            );
        }

        $sale->update([
            "ceiling" => Ceiling::DISTRIBUTED
        ]);

        return $salesData;
    }


    private function IntersectLine(Collection $lineIds, $mapping): Collection
    {
        return Cache::remember(
            'file_id' . $this->file_id . 'intersection:' . Hash::make(json_encode($lineIds) . '_' . $mapping->id),
            now()->addHours(self::CACHE_HOURS),
            fn() => $mapping->line_id ? $lineIds->intersect([$mapping->line_id]) : $lineIds
        );
    }


    private function addDetails(Sale $sale, $line_id, $brick_id, $line_division_id, $percentage): array
    {
        return [
            'sale_id' => $sale->id,
            'line_id' => $line_id,
            'div_id' => $line_division_id,
            'brick_id' => $brick_id,
            'date' => $sale->date,
            'quantity' => $percentage * $sale->quantity,
            'bonus' => $percentage * $sale->bonus,
            'value' => $percentage * $sale->value,
            'file_id' => $this->file_id,
            'created_at' => now(),
            'updated_at' => now(),
            'ratio' => round(abs($percentage) / 100, 10)
        ];
    }

}
