<?php

namespace App\Imports;

use App\Helpers\CrmExcelDate;
use App\Helpers\ExcelImport;
use App\LineDivParent;
use App\ModelsImported;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class LineDivParentsImport extends ExcelImport
{


    /**
     * @return array
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function rules(array $row): array
    {
        return [
            'line_division' => ['required','numeric','exists_not_soft_deleted:line_divisions,id'],
            'division_parent' => ['nullable','numeric','exists_not_soft_deleted:line_divisions,id'],
            'from_date' => ['required','date_format:'.CrmExcelDate::OFFICIAL_FORMAT],
            'to_date' => ['nullable','date_format:'.CrmExcelDate::OFFICIAL_FORMAT,'after:form_date'],
        ];
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row):bool
    {

        try {

            $this->bulkInsert([
                'file_id' => $this->file_id,
                'line_div_id' => $row['line_division'],
                'parent_id' => $row['division_parent'],
                'from_date' => $row['from_date'],
                'created_at' => now(),
                'updated_at' => now(),
            ],LineDivParent::class);

        } catch (\Exception $e) {
            return false;
        }
        Cache::remember(
            "line_divisions_parents_inserted_in_file:" . $this->file_id,
            now()->addHours(2),
            fn() =>  ModelsImported::insert([
                'files_imported_id' => $this->file_id,
                'model_type' => ('App\LineDivParent'),
                'created_at' => now(),
                'updated_at' => now(),
            ])
        );
        return true;
    }

}
