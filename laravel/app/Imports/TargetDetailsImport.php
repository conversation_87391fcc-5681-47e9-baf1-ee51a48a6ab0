<?php

namespace App\Imports;

use App\Helpers\CrmExcelDate;
use App\Helpers\ExcelImport;
use App\LineBricks;
use App\ModelsImported;
use App\Product;
use App\SalesSetting;
use App\TargetDetails;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

use function PHPUnit\Framework\returnSelf;

class TargetDetailsImport extends ExcelImport
{

    public function rules(array $row): array
    {
        $fromToManyToManyValidation = function ($attribute, $value, $fail, $model, $id, $key, $message) use ($row) {
            $MiddleModelItem = Cache::remember(
                'model:' . $model . '_id:' . $id . '_value:' . $row[$key] . '_line:' . $row['line_id'] . '_file_id:' . $this->file_id,
                now()->addHours(2),
                fn() => resolve($model)::where($id, '=', $row[$key])->first()?->lines()->wherePivot('line_id', '=', $row['line_id'])->first()
            );
            $from = $MiddleModelItem?->from_date ? Carbon::createFromFormat(CrmExcelDate::OFFICIAL_FORMAT, $MiddleModelItem->from_date) : null;
            $to = $MiddleModelItem?->to_date ? Carbon::createFromFormat(CrmExcelDate::OFFICIAL_FORMAT, $MiddleModelItem->to_date) : null;
            $valueDate = Carbon::createFromFormat(CrmExcelDate::OFFICIAL_FORMAT, $value);

            if (!$from || !($valueDate->gte($from) || $valueDate->lte($to)))
                return $fail('the ' . $attribute . $message);
        };
        return [
            'line_id' => ['required', 'exists_not_soft_deleted:lines,id'],
            'type_id' => ['nullable', 'exists_not_soft_deleted:target_types,id'],
            'division_id' => ['nullable', 'exists_not_soft_deleted:line_divisions,id'],
            'brick_id' => ['nullable', 'exists_not_soft_deleted:bricks,id'],
            'product_id' => ['required', 'exists_not_soft_deleted:products,id'],
            'quantity' => ['required', 'numeric'],
            'value' => ['nullable'],
            'date' => [
                'required',
                'date_format:' . CrmExcelDate::OFFICIAL_FORMAT,
                function ($attribute, $value, $fail) use ($fromToManyToManyValidation) {
                    $model = Product::class;
                    $id = 'id';
                    $key = 'product_id';
                    $message = ' Target must be in range of date of linked product .';
                    $fromToManyToManyValidation($attribute, $value, $fail, $model, $id, $key, $message);
                },
            ],
        ];
    }


    public function model(array $row): bool
    {
        try {
            $withBrick = Cache::remember(
                'target_level:' . $this->file_id,
                now()->addHours(2),
                fn() => SalesSetting::where('key', 'target_level')->value('value') == 'Brick'
            );
            $targets = [];
            if ($withBrick) {
                LineBricks::where('line_id', $row['line_id'])->where('brick_id', $row['brick_id'])
                    ->where('from_date', '<=', $row['date'])
                    ->where(fn($q) => $q->where('to_date', '=', null)->orWhere('to_date', '>=', $row['date']))
                    ->get()
                    ->each(function (LineBricks $lineBrick) use ($row, &$targets) {
                        $targets[] = [
                            'line_id' => $row['line_id'],
                            'type_id' => $row['type_id'],
                            'date' => $row['date'],
                            'product_id' => $row['product_id'],
                            'div_id' => $lineBrick->line_division_id,
                            'brick_id' => $row['brick_id'],
                            'target' => ($row['quantity'] * $lineBrick->ratio) / 100,
                            'value' => ($row['value'] * $lineBrick->ratio) / 100,
                            'file_id' => $this->file_id
                        ];
                    });
            } else {
                $targets[] = [
                    'line_id' => $row['line_id'],
                    'type_id' => $row['type_id'],
                    'date' => $row['date'],
                    'product_id' => $row['product_id'],
                    'div_id' => $row['division_id'],
                    'brick_id' => $row['brick_id'],
                    'target' => $row['quantity'],
                    'value' => $row['value'],
                    'file_id' => $this->file_id
                ];
            }

            if (!empty($targets))
                $this->bulkInsert($targets, TargetDetails::class);
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return false;
        }
        Cache::remember(
            "target_details_in_file:" . $this->file_id,
            now()->addHours(2),
            fn() => ModelsImported::insert([
                'files_imported_id' => $this->file_id,
                'model_type' => TargetDetails::class,
                'created_at' => now(),
                'updated_at' => now()
            ])
        );
        return true;
    }
}
