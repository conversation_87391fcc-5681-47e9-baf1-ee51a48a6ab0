<?php

namespace App\Imports;

use App\Helpers\CrmExcelDate;
use App\Helpers\ExcelImport;
use App\Mapping;
use App\Models\BranchMapping;
use App\ModelsImported;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class BranchMappingsImport  extends ExcelImport
{

    public function rules(array $row): array
    {
        return [
            'line' => ['required', 'numeric', 'exists_not_soft_deleted:lines,id'],
            'distributor' => ['required', 'numeric', 'exists_not_soft_deleted:distributors,id'],
            'division' => ['required', 'numeric', 'exists_not_soft_deleted:line_divisions,id'],
            'percentage' => ['required', 'numeric', 'between:0.01,100.1'],
            'code' => ['required', 'string'],
            'name' => ['required', 'string'],
            'from_date' => ['required', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'to_date' => ['nullable', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT, 'after:from'],
        ];
    }

    public function model(array $row): bool
    {
        try{
            $this->bulkInsert([
                'line_id' => $row['line'],
                'distributor_id' => $row['distributor'],
                'div_id' => $row['division'],
                'percent' => $row['percentage'],
                'code' => $row['code'],
                'name' => $row['name'],
                'from_date' => $row['from_date'],
                'to_date' => $row['to_date'],
                'file_id' => $this->file_id,
            ],BranchMapping::class);
        }catch(Exception $e){
            Log::info('Error Mappings Import : '.$e->getMessage());
            Log::info('Error Mappings Import Line : '.$e->getLine());

            return false;
        }


        Cache::remember(
            "branch_mapping_with_distributor_in_file:" . $this->file_id,
            now()->addHours(2),
            fn () => ModelsImported::insert([
                [
                    'files_imported_id' => $this->file_id,
                    'model_type' => BranchMapping::class
                ],
                [
                    'files_imported_id' => $this->file_id,
                    'model_type' => BranchMapping::class
                ]
            ])
        );

        return true;
    }
}
