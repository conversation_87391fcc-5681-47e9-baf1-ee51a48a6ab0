<?php

namespace App\Imports;

use App\Distributor;
use App\Helpers\CrmExcelDate;
use App\Helpers\ExcelImport;
use App\Models\BranchMapping;
use App\Models\BranchSale;
use App\Models\Distributors\ProductMapping;
use App\ModelsImported;
use App\Product;
use App\Rules\CompositeExists;
use App\Rules\IsProductLinesIntersectedWithDistributorLines;
use App\Sale;
use Exception;
use Illuminate\Support\{Carbon, Collection, Facades\Cache, Facades\DB, Facades\Log, Facades\Hash};

class BranchSalesImport  extends ExcelImport
{
    /**
     * @return array
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function rules(array $row): array
    {

        return [
            'brick' => ['required', 'exists:branch_mappings,code'],
            'sales' => ['required', 'numeric', 'max:9999999'],
            'bonus' => ['required', 'numeric'],
            'region' => ['required', 'numeric'],
            'product' => [
                'required',
                'exists_not_soft_deleted:product_distributors,code'
            ],
            'distributor' => [
                'required',
                'exists_not_soft_deleted:distributors,id'
            ],
            'value' => ['required', 'numeric', 'max:99999999'],
            'date' => ['required', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
        ];
    }

    public function model(array $row): bool
    {

        try {
            $product = Product::whereHas('productDistributors', fn($q) => $q->where('code', $row['product'])->where('distributor_id', $row['distributor'])
                ->where('product_distributors.from_date', '<=', (string)Carbon::now())->where(fn($q) => $q->where('product_distributors.to_date', '=', null)
                    ->orWhere('product_distributors.to_date', '>=', (string)Carbon::now())))->first();

            $distributor = Distributor::find($row['distributor']);
            $distributorLineIds = $distributor->lines()->get()->pluck('id');
            $lineIds = $product->allLines()
                ->where('line_products.from_date', '<=', $row['date'])
                ->where(fn($q) => $q->where('line_products.to_date', '>=', $row['date'])
                    ->orWhere('line_products.to_date', null))
                ->whereIntegerInRaw('lines.id', $distributorLineIds)->get()->pluck("id");
            $branchMappings = BranchMapping::where('distributor_id', $distributor->id)
                ->where('from_date', '<=', (string)Carbon::now())->where(fn($q) => $q->whereNull('to_date')
                    ->orWhere('to_date', '>=', (string)Carbon::now()))
                ->whereIn('line_id', $lineIds)
                ->where('code', $row['brick'])
                ->get();
            Log::info($branchMappings);
            foreach ($branchMappings as $branchMapping) {
                BranchSale::create([
                    'line_id' => $branchMapping->line_id,
                    'distributor_id' => $row['distributor'],
                    'branch_mapping_id' => $branchMapping->id,
                    'div_id' => $branchMapping->div_id,
                    'product_id' => $product->id,
                    'file_id' => $this->file_id,
                    'value' => $row['value'],
                    'bonus' => $row['bonus'],
                    'region' => $row['region'],
                    'quantity' => $row['sales'],
                    'date' => $row['date'],
                ]);
            }
        } catch (Exception $e) {
            Log::info('Error Mappings Import : ' . $e->getMessage());
            return false;
        }


        Cache::remember(
            "branch_sale_with_distributor_in_file:" . $this->file_id,
            now()->addHours(2),
            fn() => ModelsImported::insert([
                [
                    'files_imported_id' => $this->file_id,
                    'model_type' => BranchSale::class
                ]
            ])
        );

        return true;
    }
}
