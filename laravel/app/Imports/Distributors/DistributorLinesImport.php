<?php

namespace App\Imports\Distributors;

use App\Helpers\CrmExcelDate;
use App\Helpers\ExcelImport;
use App\Models\Distributors\DistributorLine;
use App\ModelsImported;
use Illuminate\Support\Facades\DB;

class DistributorLinesImport extends ExcelImport
{
    /**
     * @return array
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function rules(array $row): array
    {
        return [
            'line_id' => ['required', 'numeric', 'exists_not_soft_deleted:lines,id'],
            'distributor_id' => ['required', 'numeric', 'exists_not_soft_deleted:distributors,id'],
            'from_date' => ['required', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'to_date' => ['nullable', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT, 'after:from_date'],
        ];
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row): bool
    {
        try {

            DistributorLine::insert([
                'line_id' => $row['line_id'],
                'distributor_id' => $row['distributor_id'],
                'from_date' => $row['from_date'],
                'to_date' => $row['to_date'],
                'file_id' => $this->file_id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } catch (\Exception $e) {
            return false;
        }
        ModelsImported::insert([
            'files_imported_id' => $this->file_id,
            'model_type' => DistributorLine::class,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        return true;
    }
}
