<?php

namespace App\Imports;

use App\Helpers\CrmExcelDate;
use App\Helpers\ExcelImport;
use App\Models\UnhashedPassword;
use App\Rules\RoleMustExists;
use App\User;
use App\UserDetails;
use App\ModelsImported;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UsersImport extends ExcelImport
{

    /**
     * @return array
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function rules(array $row): array
    {
        return array_merge(
            $this->userDetailsRules(),
            $this->userRules()
        );
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row): bool
    {
        try {
            if (User::active()->count() >= config('app.users_limit')) {
                throw new \Exception("You have exceeding the limit of active users. please contact your service provider upgrade your service plan.");
            }
            $unhashedPass = $row['password'];
            $user = User::create([
                'name' => $row['name'],
                'fullname' => $row['fullname'],
                'email' => $row['email'],
                'personal_email' => $row['personal_email'],
                'menuroles' => $row['menuroles'],
                'emp_code' => $row['emp_code'],
                'status' => $row['status'],
                'hiring_date' => $row['hiring_date'],
                'password' => Hash::make($row['password']),
                'api_token' => Str::random(10),
                'file_id' => $this->file_id,
            ]);
            UnhashedPassword::insert([
                'user_id' => $user->id,
                'password' => $unhashedPass
            ]);
            $roles = explode(',', $row['menuroles']);
            foreach ($roles as $key => $role) {
                $user->assignRole($role);
            }

            $this->bulkInsert([
                'user_id' => $user->id,
                'dob' => $row['date_of_birth'],
                'ssn' => $row['ssn'],
                'mobile' => $row['mobile'],
                'tel' => $row['tel'],
                'address' => $row['address'],
                'gender' => $row['gender'],
                'file_id' => $this->file_id,
                'created_at' => now(),
                'updated_at' => now(),
            ], UserDetails::class);

        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return false;
        }
        Cache::remember(
            "users_in_file:" . $this->file_id,
            now()->addHours(2),
            fn() => ModelsImported::insert([
                [
                    'files_imported_id' => $this->file_id,
                    'model_type' => ('App\User'),
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'files_imported_id' => $this->file_id,
                    'model_type' => ('App\UserDetails'),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            ])
        );
        return true;
    }

    public function userRules(): array
    {
        return [
            'name' => ['required', 'string'],
            'fullname' => ['required', 'string'],
            'email' => ['nullable', 'email'],
            'personal_email' => ['nullable', 'email'],
            'emp_code' => ['nullable'],
            'menuroles' => ['required', 'string',new RoleMustExists()],
            'status' => ['required', 'in:active,inactive'],
            'password' => ['required'],
            'hiring_date' => ['nullable', 'string', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
        ];
    }

    public function userDetailsRules(): array
    {
        return [
            'date_of_birth' => ['nullable', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'ssn' => ['nullable'],
            'mobile' => ['nullable', 'string'],
            'tel' => ['nullable', 'numeric'],
            'address' => ['nullable', 'string'],
            'gender' => ['required', 'string', 'in:male,female']
        ];
    }
}
