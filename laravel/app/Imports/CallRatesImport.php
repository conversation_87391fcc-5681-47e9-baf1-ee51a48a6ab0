<?php

namespace App\Imports;

use App\CallRate;
use App\Helpers\CrmExcelDate;
use App\Helpers\ExcelImport;
use App\ModelsImported;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class CallRatesImport extends ExcelImport
{


    public function rules(array $row): array
    {
        return [
            'line_id' => ["required", "numeric", "exists_not_soft_deleted:lines,id"],
            "division_id" => ["required", "numeric", "exists_not_soft_deleted:line_divisions,id"],
            "shift_id" => ["required", "numeric", "exists_not_soft_deleted:shifts,id"],
            "call_rate" => ["required", "numeric"],
            "date" => ['required', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT]
        ];
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row): bool
    {

        try {
            $this->bulkInsert([
                'file_id' => $this->file_id,
                'line_id' => $row['line_id'],
                'division_id' => $row['division_id'],
                'call_rate' => $row['call_rate'],
                'date' => $row['date'],
                'shift_id' => $row['shift_id'],
                'created_at' => now(),
                'updated_at' => now(),
            ],CallRate::class);
        } catch (\Exception $e) {
            return false;
        }
        Cache::remember(
            "call_rate_inserted_in_file:" . $this->file_id,
            now()->addHours(2),
            fn() => ModelsImported::insert([
                'files_imported_id' => $this->file_id,
                'model_type' => CallRate::class,
                'created_at' => now(),
                'updated_at' => now(),
            ])
        );

        return true;
    }
}
