<?php

namespace App\Scopes\Widgets;

use App\Models\Widgets\MedicalRepVisitPerMember;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class MedicalRepVisitPerMemberScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        $builder->where('Widgetable_type','=',MedicalRepVisitPerMember::class);
    }
}
