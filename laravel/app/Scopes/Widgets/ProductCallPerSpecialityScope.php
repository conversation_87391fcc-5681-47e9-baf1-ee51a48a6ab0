<?php

namespace App\Scopes\Widgets;

use App\Models\Widgets\AccountCoverage;
use App\Models\Widgets\ProductCallPerSpeciality;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class ProductCallPerSpecialityScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        $builder->where('Widgetable_type','=',ProductCallPerSpeciality::class);
    }
}
