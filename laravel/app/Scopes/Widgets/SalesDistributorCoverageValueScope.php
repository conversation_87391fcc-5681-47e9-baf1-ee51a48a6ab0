<?php
namespace App\Scopes\Widgets;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use App\Models\Widgets\SalesDistributorCoverageValue;

class SalesDistributorCoverageValueScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        $builder->where('widgetable_type','=',SalesDistributorCoverageValue::class);
    }
}
