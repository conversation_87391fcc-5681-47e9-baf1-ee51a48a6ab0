<?php
namespace App\Scopes\Widgets;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use App\Models\Widgets\SalesDistributorCoverageUnit;

class SalesDistributorCoverageUnitScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        $builder->where('widgetable_type','=',SalesDistributorCoverageUnit::class);
    }
}
