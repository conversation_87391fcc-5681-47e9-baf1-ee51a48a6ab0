<?php

namespace App\Scopes\Widgets;

use App\Models\Widgets\AlertBelowFrequencies;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class AlertBelowFrequenciesScope implements scope{

    public function apply(Builder $builder, Model $model)
    {
        $builder->where('Widgetable_type','=',AlertBelowFrequencies::class);
    }

};