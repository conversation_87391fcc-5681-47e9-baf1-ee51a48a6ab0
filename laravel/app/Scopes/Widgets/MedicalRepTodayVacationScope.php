<?php

namespace App\Scopes\Widgets;

use App\Models\Widgets\MedicalRepTodayVacation;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class MedicalRepTodayVacationScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        $builder->where('Widgetable_type','=',MedicalRepTodayVacation::class);
    }
}
