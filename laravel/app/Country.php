<?php

namespace App;

use App\Traits\ModelExportable;
use App\Traits\ModelImportable;
use App\Traits\SendMail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Country extends Model
{
    use SoftDeletes ;
    use ModelImportable;
    use ModelExportable;
    use SendMail;

    protected $guard_name = 'api';

    protected $table = 'countries';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name', 'sort','file_id'
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

    public function lines()
    {
        return $this->hasMany(Line::class);
    }
}
