<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AccountSchedule extends Model
{
    use SoftDeletes;

    protected $guard_name = 'api';

    protected $table = 'account_schedules';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'account_id', 'day'
        , 'open_form_time', 'open_to_time'
        , 'visit_from_time', 'visit_to_time','file_id'
        , 'deleted_at'
    ];

    protected $casts = [
        'open_form_time' => 'time',
        'open_to_time' => 'time',
        'visit_from_time' => 'time',
        'visit_to_time' => 'time',
    ];

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

}
