<?php

namespace App\Http\Resources\Coaching;

use Illuminate\Http\Resources\Json\JsonResource;

class QuestionResource extends JsonResource
{
    /**
     * The "data" wrapper that should be applied.
     *
     * @var string|null
     */
    public static $wrap = 'question';

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'sort' => $this->sort,
            'notes' => $this->notes,
            'answers' => AnswerResource::collection($this->whenLoaded('answers')),
            'categories' => CategoryResource::collection($this->whenLoaded('categories')),
        ];
    }
}
