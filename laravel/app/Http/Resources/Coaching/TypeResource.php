<?php

namespace App\Http\Resources\Coaching;

use Illuminate\Http\Resources\Json\JsonResource;

class TypeResource extends JsonResource
{

    /**
     * The "data" wrapper that should be applied.
     *
     * @var string|null
     */
    public static $wrap = 'type';


    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        return [
            'id' => $this->id,
            'name' => $this->name,
            'line' => $this->line->name ?? '',
            'line_id' => $this->line_id ?? '',
            'sort' => $this->sort,
            'notes' => $this->notes,
            'categories' => CategoryResource::collection($this->whenLoaded('categories')),
        ];
    }
}
