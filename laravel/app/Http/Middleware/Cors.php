<?php

namespace App\Http\Middleware;

use Closure;

class Cors
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $response = $next($request);

        $response->headers->set('Access-Control-Allow-Origin', '*');
        $response->headers->set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        $response->headers->set('allowed_headers', 'Origin,X-Requested-With,Content-Type,Accept,Authorization');
        $response->headers->set('max_age', 0);
        $response->headers->set('exposed_headers', '');
        $response->headers->set('allowed_origins_patterns', '');
        $response->headers->set('supports_credentials', 'false');

        return $response;
    }
}
