<?php

namespace App\Http\Middleware;

use App\Models\Trace;
use App\Services\TracingService;
use App\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class TraceUser
{
    public function __construct(private readonly TracingService $tracingService)
    {
    }

    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (!auth()->check()) {
            return $next($request);
        }

        try {
            // Get current location
            $location = $this->getUserLocation($request->user());

            $this->tracingService->processLocationTracking($location);
        } catch (\Exception $e) {
            // Log error but don't interrupt the request flow
            Log::error('Location tracking failed: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'exception' => $e,
            ]);
        }

        return $next($request);
    }

    /**
     * Get user location (wrapper for global function)
     *
     * @param User $user
     * @return array|null
     */

    private function getUserLocation(User $user): ?array
    {
        // Check if the global function exists
        if (!function_exists('getUserLocation')) {
            Log::warning('getUserLocation function not found');
            return null;
        }

        try {
            return getUserLocation($user) ?? null;
        } catch (\Exception $e) {
            Log::warning('Error getting user location: ' . $e->getMessage());
            return null;
        }
    }


}
