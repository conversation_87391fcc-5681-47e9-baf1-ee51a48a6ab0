<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Line;
use App\Models\CheckLocation;
use App\Services\ActualService;
use App\Services\WorkingHours\CheckLocationService;
use App\Services\WorkingHours\DefaultService;
use App\Shift;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class WorkingHoursReportController extends ApiController
{
    public function __construct(
        private readonly DefaultService $defaultService,
        private readonly CheckLocationService $checkLocationService,
    )
    {
    }

    public function filter(Request $request)
    {
        $data = match ($request->workingFilter['view']) {
            'Check Location' => (function () {
                $this->checkLocationService->applyFilters(request()->workingFilter);
                return $this->checkLocationService->fetch(Auth::user());
            })(),
            default => (function () {
                $this->defaultService->applyFilters(request()->workingFilter);
                return $this->defaultService->fetch(Auth::user());
            })(),
        };

        return response()->json($data);
    }

}
