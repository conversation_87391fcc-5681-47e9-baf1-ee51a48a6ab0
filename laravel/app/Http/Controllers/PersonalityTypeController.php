<?php

namespace App\Http\Controllers;

use App\PersonalityType;
use Illuminate\Http\Request;
use App\Form;
use App\Action;
use App\Exports\PersonalityTypesExport;
use App\Helpers\ExcelImporter;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use App\Setting;
use Spatie\Permission\Models\Permission;
use App\Helpers\LogActivity;
use App\Http\Requests\ImportRequest;
use App\Http\Requests\MailRequest;
use App\Http\Requests\PersonalityTypeRequest;
use App\Import;
use App\Imports\PersonalityTypesImport;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Excel as ExcelType;
use Illuminate\Support\Facades\Response;
use Meneses\LaravelMpdf\Facades\LaravelMpdf as PDF;
use Illuminate\Support\Facades\Mail;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;
use App\Imports\Updates\PersonalityTypesImport as UpdatesPersonalityTypesImport;

class PersonalityTypeController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $personalitytypes = DB::table('personality_types')
        ->select('personality_types.id', 'personality_types.name', 'personality_types.notes', 'personality_types.sort')
        ->whereNull('deleted_at')
        ->orderBy('personality_types.sort', 'asc')
        ->get();
        $total = count($personalitytypes);
        
        
        
        
        
        
        LogActivity::addLog();
        return response()->json( compact('personalitytypes','total') );
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $max_sort = PersonalityType::withTrashed()->max('sort');
        if(!$max_sort){
            $max_sort = 100;
        }else{
            $max_sort += 100;
        }
        return response()->json( ['status' => 'success','max_sort' => $max_sort] );
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(PersonalityTypeRequest $request)
    {
        $max_sort = PersonalityType::withTrashed()->max('sort');
        if(!$max_sort){
            $max_sort = 100;
        }else{
            $max_sort += 100;
        }

        
        
        
        
        
        

        $personalitytype = new PersonalityType();
        $personalitytype->name = $request->input('name');
        $personalitytype->notes = $request->input('notes');
        $personalitytype->sort = $max_sort;
        $personalitytype->save();

        $model_id = $personalitytype->id;
        $model_type = ('App\PersonalityType');

        LogActivity::addLog($model_id,$model_type);
        return response()->json( ['status' => 'success'] );
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\PersonalityType  $personalityType
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $personalitytype = DB::table('personality_types')
        ->select('personality_types.id', 'personality_types.name', 'personality_types.notes', 'personality_types.sort')
        ->where('personality_types.id', '=', $id)
        ->first();
        
        
        
        
        
        
        $model_id = $id;
        $model_type = ('App\PersonalityType');

        LogActivity::addLog($model_id,$model_type);
        return response()->json( $personalitytype );
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\PersonalityType  $personalityType
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $personalitytype = DB::table('personality_types')
        ->select('personality_types.id', 'personality_types.name', 'personality_types.notes', 'personality_types.sort')
        ->where('personality_types.id', '=', $id)
        ->first();
        
        
        
        
        
        
        $model_id = $id;
        $model_type = ('App\PersonalityType');
        LogActivity::addLog($model_id,$model_type);
        return response()->json( $personalitytype );
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\PersonalityType  $personalityType
     * @return \Illuminate\Http\Response
     */
    public function update(PersonalityTypeRequest $request, $id)
    {
        
        
        
        
        
        

        $personalitytype = PersonalityType::find($id);
        $personalitytype->name       = $request->input('name');
        $personalitytype->notes      = $request->input('notes');
        $personalitytype->sort      = $request->input('sort');
        $personalitytype->save();
        $model_id = $id;
        $model_type = ('App\PersonalityType');

        LogActivity::addLog($model_id,$model_type);

        return response()->json( ['status' => 'success'] );
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\PersonalityType  $personalityType
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        
        
        
        
        
        
        $personalitytype = PersonalityType::find($id);
        $doctorPersonality= DB::table("doctors")->where("personality_type_id", $id)->count();
        if($personalitytype){
            if ($doctorPersonality > 0)
            {
                return response()->json(['statusText'=>'failed'],422);
            }
            $personalitytype->delete();
        }
        $model_id = $id;
        $model_type = ('App\PersonalityType');

        LogActivity::addLog($model_id,$model_type);
        return response()->json( ['status' => 'success'] );
    }

    public function import(ImportRequest $request)
    {
        PersonalityType::import($request);
        return $this->respondSuccess();
    }

    public function updateByImport(ImportRequest $request)
    {
        PersonalityType::import(request: $request, update: true);

        return $this->respondSuccess();
    }

    public function exportpersonalitytypes()
    {
        return PersonalityType::export(ExcelType::XLSX);
    }

    public function exportcsv()
    {
        return PersonalityType::export(ExcelType::CSV);
    }
    public function exportpdf()
    {
        $personalitytypes = PersonalityType::where('deleted_at',null)->get();
        return PersonalityType::exportPdf($personalitytypes);
    }
    public function sendmail(MailRequest $request)
    {
        $personalitytypes = PersonalityType::where('deleted_at',null)->get();
        return PersonalityType::sendMail($request,$personalitytypes);
    }
}
