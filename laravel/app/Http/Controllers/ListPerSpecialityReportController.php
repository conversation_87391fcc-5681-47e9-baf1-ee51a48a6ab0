<?php

namespace App\Http\Controllers;

use App\Account;
use App\AccountType;
use App\ActualVisit;
use App\Classes;
use App\DivisionType;
use App\Doctor;
use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Line;
use App\LineDivision;
use App\Services\AccountService;
use App\Services\DoctorService;
use App\Services\ListService;
use App\Speciality;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon as SupportCarbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ListPerSpecialityReportController extends ApiController
{
    public function getLines()
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = $user->userLines();
        $account_types = AccountType::select('account_types.id', 'account_types.name')->orderBy('sort', 'ASC')->get();
        $users = $user->belowUsersOfAllLinesWithPositions($lines);
        return response()->json([
            'lines' => $lines,
            'users' => $users,
            'account_types' => $account_types,
        ]);
    }

    public function getDataWithLine(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = Line::whereIntegerInRaw('id', $request->lines)->get();
        $divisions = collect([]);
        foreach ($lines as $line) {
            $divisions = $divisions->merge($user->userDivisions($line));
        }
        $specialities = Speciality::whereHas(
            'lineSpecialities',
            fn($q) =>
            $q->whereIntegerInRaw('line_specialities.line_id', $request->lines)->where('line_specialities.from_date', '<=', now())
                ->where(fn($q) => $q->where('line_specialities.to_date', '>', (string)Carbon::now())
                    ->orWhere('line_specialities.to_date', null))
        )->get();
        $users = $user->belowUsersOfAllLinesWithPositions($lines, 'Active');

        return response()->json([
            'divisions' => $divisions->values(),
            'users' => $users,
            'specialities' => $specialities,
        ]);
    }
    public function filter(Request $request)
    {
        // throw new CrmException()
        /**@var User authUser */
        $authUser = Auth::user();
        $list = $request->listFilter;
        $lines = Line::when(!empty($list['lines']), fn($q) => $q->whereIn("id", $list['lines']))->get();
        $account_types = AccountType::when(!empty($list['accountTypes']), fn($q) => $q->whereIn("id", $list['accountTypes']))->get();
        $specialities = Speciality::when(!empty($list['specialities']), fn($q) => $q->whereIn("id", $list['specialities']))->get();
        $classes = Classes::when(!empty($list['classes']), fn($q) => $q->whereIn("id", $list['classes']))->get();
        $division_type = DivisionType::where('last_level', '=', 1)->first()->id;
        $filter = $list['filter'] == 1 ? 1 : 2;
        $countBy = 2;
        $fields = [];

        $filter == 2 ? $fields = collect(['line', 'employee'])
            :
            $fields = collect(['line', 'division', 'brick', 'employee']);
        foreach ($specialities as $speciality) {
            $fields = $fields->merge($speciality->name);
            $fields = $fields->merge($speciality->name . ' ratio');
        }
        $fields = $fields->merge('total');
        $clickable_fields = collect([]);

        $clickable_fields = $clickable_fields->merge($specialities->pluck('name'));

        LogActivity::addLog();
        $filtered = new Collection([]);
        $data = new Collection([]);
        $users = new Collection([]);
        $divisions = new Collection([]);
        $from_date = SupportCarbon::parse($list['fromDate'])->startOfMonth();
        $to_date = SupportCarbon::parse($list['fromDate'])->endOfMonth();



        if ($filter == 1) {
            foreach ($lines as $l) {

                $divisions = $l->divisions($from_date, $to_date)->where("deleted_at", null)
                    ->when(!empty($list['divisions']), fn($q) => $q->whereIn("line_divisions.id", $list['divisions']))->get();
                $filtered = $filtered->merge($authUser->filterDivisions($l, $divisions, $list, $from_date, $to_date));

                $perBricks = collect([]);

                $filtered->each(function ($object) use ($from_date, $to_date, $perBricks, $l, $list, $data, $filter, $account_types, $specialities, $classes, $division_type, $countBy) {
                    $data = $data->push($this->statistics($object, $l, $from_date, $to_date, $list, $filter, $account_types, $specialities, $classes, $division_type, $countBy, $perBricks));
                });
            }
        }

        // throw new CrmException($date); 


        if ($filter == 2) {

            foreach ($lines as $l) {

                $users = $l->users($from_date, $to_date)->wherePivot("deleted_at", null)
                    ->when(!empty($list['users']), fn($q) => $q->whereIn("line_users.user_id", $list['users']))->get();
                $filtered = $filtered->merge($authUser->filterUsers($l, $users, $list, $from_date, $to_date));
                $perBricks = collect([]);
                $filtered->each(function ($object) use ($from_date, $to_date, $perBricks, $l, $list, $data, $filter, $account_types, $specialities, $classes, $division_type, $countBy) {
                    $data = $data->push($this->statistics($object, $l, $from_date, $to_date, $list, $filter, $account_types, $specialities, $classes, $division_type, $countBy, $perBricks));
                });
            }
        }

        return response()->json([
            'data' => $list['checked'] ? $data->collapse()->unique(function ($item) {
                return $item['id'] . $item['brick_id'];
            })->values()
                : $data->unique("id")->values(),
            'fields' => $fields,
            'clickable_fields' => $clickable_fields
        ]);
    }

    private function statistics($object, $line, $from_date, $to_date, $list, $filter, $accountTypes, $specialities, $classes, $division_type, $countBy, $perBricks)
    {
        // throw new CrmException($list['checked']); 

        $data = collect([]);
        if ($list['checked']) {
            $data = $this->withBricks($object, $line, $from_date, $to_date, $list, $filter, $accountTypes, $specialities, $classes, $division_type, $countBy, $perBricks);
        } else {
            $data = $this->getList($object, $line, $from_date, $to_date, $list, $filter, $accountTypes, $specialities, $classes, $division_type, $countBy);
        }
        return $data;
    }
    public function getList($object, $line, $from_date, $to_date, $list, $filter, $accountTypes, $specialities, $classes, $division_type, $countBy)
    {
        $data = collect([
            'id' => $object->id,
            'line' => $line->name,
            'division' => $filter == 1 ? $object?->name : $object->divisions($from_date, $to_date)->where('line_divisions.line_id', $line->id)->pluck('name')->implode(','),
            'brick_id' => null,
            'employee' => $filter == 1 ? $object->users($from_date, $to_date)?->where('line_users_divisions.line_id', $line->id)->pluck('fullname')->implode(',') : $object?->fullname,
            'color' => $filter == 1 ? $object?->DivisionType->color : $object->division($line, $from_date, $to_date)?->DivisionType->color,
        ]);

        if ($filter == 1) {
            $divisions = $object->getBelowDivisions($from_date, $to_date)
                ->where('division_type_id', '=', $division_type)->where('is_kol', 0)->unique('id')->pluck('id')->toArray();
        }
        if ($filter == 2) {
            $divisions = $object?->allBelowDivisions($line, $from_date, $to_date)
                ->where('division_type_id', '=', $division_type)->where('is_kol', 0)->unique('id')->pluck('id')->toArray();
        }

        if ($list['type'] == 'Active') {
            $accounts = (new DoctorService())->getDoctorsِCoverage([$line->id], $divisions, $from_date, $to_date, $specialities->pluck('id')->toArray(), $accountTypes->pluck('id')->toArray());
        } elseif ($list['type'] == 'Inactive') {
            $accounts = (new ListService())->getInactiveList($from_date, $to_date, [$line->id], $divisions, [], $specialities->pluck('id')->toArray(), $accountTypes->pluck('id')->toArray());
        } else {
            $accounts = (new ListService())->getAllList($from_date, $to_date, [$line->id], $divisions, [], $specialities->pluck('id')->toArray(), $accountTypes->pluck('id')->toArray());
        }
        $totalList = $accounts->count();
        $specialities->each(function ($speciality) use ($accounts, $data, $totalList) {
            $countSpecialities = $accounts->where('speciality_id', $speciality->id)->count();
            $data = $data->put($speciality->name, $countSpecialities);
            $data = $data->put($speciality->name . ' ratio', $totalList > 0 ? round($countSpecialities / $totalList * 100, 2) . ' %' : 0 . ' %');
        });
        $data = $data->put('total', $totalList);

        // throw new CrmException($data);

        return $data;
    }
    public function withBricks($object, $line, $from_date, $to_date, $list, $filter, $accountTypes, $specialities, $classes, $division_type, $countBy, $results)
    {
        $bricks = $object->bricks;
        if (count($bricks) != 0) {
            foreach ($bricks as $brick) {
                $data = collect([
                    'id' => $object->id,
                    'line' => $line->name,
                    'division' => $object?->name,
                    'brick' => $brick?->name,
                    'brick_id' => $brick?->id,
                    'employee' =>  $object->users($from_date, $to_date)?->where('line_users_divisions.line_id', $line->id)->pluck('fullname')->implode(','),
                    'color' => $object?->DivisionType->color,
                ]);
                if ($filter == 1) {
                    $divisions = $object->getBelowDivisions($from_date, $to_date)
                        ->where('division_type_id', '=', $division_type)->where('is_kol', 0)->unique('id')->pluck('id')->toArray();
                }
                if ($filter == 2) {
                    $divisions = $object?->allBelowDivisions($line, $from_date, $to_date)
                        ->where('division_type_id', '=', $division_type)->where('is_kol', 0)->unique('id')->pluck('id')->toArray();
                }

                if ($list['type'] == 'Active') {
                    $accounts = (new DoctorService())->getDoctorsِCoverage([$line->id], $divisions, $from_date, $to_date, $specialities->pluck('id')->toArray(), $accountTypes->pluck('id')->toArray(), [], [$brick->id]);
                    // throw new CrmException($accounts);
                } elseif ($list['type'] == 'Inactive') {
                    $accounts = (new ListService())->getInactiveList($from_date, $to_date, [$line->id], $divisions, [$brick->id], $specialities->pluck('id')->toArray(), $accountTypes->pluck('id')->toArray());
                } else {
                    $accounts = (new ListService())->getAllList($from_date, $to_date, [$line->id], $divisions, [$brick->id], $specialities->pluck('id')->toArray(), $accountTypes->pluck('id')->toArray());
                }

                $totalList = $accounts->count();
                $specialities->each(function ($speciality) use ($accounts, $data, $totalList) {
                    $countSpecialities = $accounts->where('speciality_id', $speciality->id)->count();
                    $data = $data->put($speciality->name, $countSpecialities);
                    $data = $data->put($speciality->name . ' ratio', $totalList > 0 ? round($countSpecialities / $totalList * 100, 2) . ' %' : 0 . ' %');
                });
                $data = $data->put('total', $totalList);
                $results = $results->push($data);
            }
        } else {
            $results = $results->push($this->getList($object, $line, $from_date, $to_date, $list, $filter, $accountTypes, $specialities, $classes, $division_type, $countBy));
        }

        return $results;
    }

    public function showData(Request $request)
    {
        $division_type = DivisionType::where('last_level', '=', 1)->first()->id;
        $list = $request->listFilter;
        $account_types = $request->listFilter['accountTypes'];
        $user = User::where('id', $request->user)->first();
        $division = LineDivision::where('id', $request->division)->first();
        $line = Line::where('name', $request->line)->first();

        $divisions = [];
        $from_date = SupportCarbon::parse($list['fromDate'])->startOfMonth();
        $to_date = SupportCarbon::parse($list['fromDate'])->endOfMonth();
        if ($list['filter'] == 1) {
            $divisions = $division->getBelowDivisions($from_date, $to_date)
                ->where('division_type_id', '=', $division_type)->where('is_kol', 0)->unique('id')->pluck('id')->toArray();
        } else {
            $divisions = $user->allBelowDivisions($line, $from_date, $to_date)->where('division_type_id', '=', $division_type)->unique('id')->values()->pluck('id')->toArray();
        }

        $column = null;
        $column_name = null;
        $speciality = Speciality::where("name", $request->column)->first()?->id;
        $data = collect([]);
        // if ($speciality) {
        //     $column = $speciality;
        //     $column_name = 'speciality_id';
        // }

        if ($list['type'] == 'Active') {
            $data = (new DoctorService())->getDoctors([$line->id], $divisions, $from_date, $to_date, [$speciality], $account_types);
            // throw new CrmException($accounts);
        } elseif ($list['type'] == 'Inactive') {
            $data = (new ListService())->getInactiveList($from_date, $to_date, [$line->id], $divisions, [], [$speciality], $account_types);
        } else {
            $data = (new ListService())->getAllList($from_date, $to_date, [$line->id], $divisions, [], [$speciality], $account_types);
        }
        $data = $data->values()
            ->map(function ($account) {
                // throw new CrmException($account);
                return [
                    'line' => $account->line,
                    'division' => $account->division,
                    'brick' => $account->brick,
                    'account' => $account->account,
                    'type' => $account->account_type,
                    'account_id' => $account->account_id,
                    'doctor' => $account->doctor,
                    'doctor_id' => $account->doctor_id,
                    'speciality' => $account->speciality,
                ];
            });

        return  $this->respond($data);
    }
}
