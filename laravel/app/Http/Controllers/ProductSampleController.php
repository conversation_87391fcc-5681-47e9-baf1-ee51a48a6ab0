<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Http\Requests\ImportRequest;
use App\Models\ProductSample;
use App\Models\Sample;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ProductSampleController extends ApiController
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $accounts = ProductSample::select([
            'product_samples.id',
            'lines.name as line',
            'line_divisions.name as division',
            'products.name as product',
            'product_samples.quantity as quantity',
            'product_samples.date as date',
            DB::raw("
                    CASE
                        WHEN crm_product_samples.status = 1 THEN 'Approved'
                        ELSE 'Pending'
                    END as status
                "),
        ])
            ->selectRaw('IFNULL(crm_users.fullname,"") AS approved_by')

            ->leftJoin('line_divisions', 'product_samples.line_division_id', '=', 'line_divisions.id')
            ->leftJoin('users', 'product_samples.status_by', '=', 'users.id')
            ->leftJoin('lines', 'line_divisions.line_id', '=', 'lines.id')
            ->leftJoin('products', 'product_samples.product_id', '=', 'products.id')
            ->where(
                fn($q) => $q->where('product_samples.id', 'Like', '%' . request('query') . '%')
                    ->orWhere('line_divisions.name', 'Like', '%' . request('query') . '%')
                    ->orWhere('products.name', 'Like', '%' . request('query') . '%')
                    ->orWhere('product_samples.quantity', 'Like', '%' . request('query') . '%')
                    ->orWhere('product_samples.date', 'Like', '%' . request('query') . '%')
            )->get();

        LogActivity::addLog();

        return $this->respondAll($accounts);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request) {}

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }


    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        ProductSample::find($id)->delete();
        return $this->respondSuccess();
    }

    public function import(ImportRequest $request)
    {
        Sample::import(request: $request, array: ['user_id' => auth()->id()]);
        return $this->respondSuccess();
    }

    public function approveSamples(Request $request)
    {
        ProductSample::find($request->id)->update(['status' => 1, 'status_by' => auth()->id()]);
        return $this->respondSuccess();
    }

    public function editSamples(Request $request)
    {
        ProductSample::find($request->id)->update([
            'edited_quantity' => $request->quantity,
            'status' => 1,
            'status_by' => Auth::id(),
        ]);
        return $this->respondSuccess();
    }
}
