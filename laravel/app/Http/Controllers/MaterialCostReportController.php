<?php

namespace App\Http\Controllers;

use App\ActualVisitSetting;
use App\Exceptions\CrmException;
use App\Line;
use App\Models\Material;
use App\Models\PromotionalMaterialType;
use App\Product;
use App\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MaterialCostReportController extends ApiController
{
    public function getMaterials($material, $from, $to, $types)
    {
        $materials = DB::table('materials')->select(
            'materials.id as id',
            'materials.description as title',
            DB::raw('IFNULL(DATE_FORMAT(crm_materials.date,"%d-%m-%Y"),"") as activity_date'),
            DB::raw('IFNULL(DATE_FORMAT(crm_materials.created_at,"%d-%m-%Y"),"") as insertion_date'),
            DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
            'promotional_material_types.name as type',
            'users.fullname as employee',
            'users.emp_code as emp_code',
            'users.id as user_id',
            "brands.name as brand",
            "brands.id as brand_id",
            'material_product.ratio as ratio',
            DB::raw('IFNULL(crm_materials.amount,"") as total'),
            DB::raw('IFNULL(crm_plan_visit_details.approval,"pending") as status'),
        )
            ->selectRaw('(crm_material_product.ratio * crm_materials.amount) / 100  as pro_cost')
            ->leftJoin('promotional_material_types', 'materials.material_type_id', 'promotional_material_types.id')
            ->leftJoin('users', 'materials.user_id', 'users.id')
            ->leftJoin('material_lines', 'materials.id', 'material_lines.material_id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('materials.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', Material::class);
                }
            )
            ->leftJoin('lines', 'material_lines.line_id', 'lines.id')
            ->leftJoin('material_product', 'materials.id', 'material_product.material_id')
            ->leftJoin('products', 'material_product.product_id', 'products.id')
            ->leftJoin('product_brands', 'products.id', 'product_brands.product_id')
            ->leftJoin('brands', 'product_brands.brand_id', 'brands.id')
            ->orderBy('materials.id', 'DESC')
            ->whereNull('materials.deleted_at')
            ->whereIntegerInRaw('materials.material_type_id', $types->pluck('id')->toArray())
            ->whereBetween('materials.date', [$from, $to]);
        $materials = match ($material['approval']) {
            1 => $materials->whereNull('plan_visit_details.approval'),
            2 => $materials->where('plan_visit_details.approval', 1),
            3 => $materials->where('plan_visit_details.approval', 0),
            4 => $materials->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null)),
            5 => $materials,
        };

        if (!empty($material['lines'])) {
            $materials = $materials->whereIntegerInRaw('lines.id', $material['lines']);
        }
        if (!empty($material['users'])) {
            $materials = $materials->whereIntegerInRaw('users.id', $material['users']);
        }

        $materials = $materials->groupBy("materials.id", "brands.id", 'material_product.ratio', 'plan_visit_details.approval')->get();

        return $materials;
    }

    public function getLines()
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = $user->userLines();
        $users = $user->belowUsersOfAllLinesWithPositions($lines);
        $types = PromotionalMaterialType::select('id', 'name')->get();
        return $this->respond(['lines' => $lines, 'types' => $types,'users' => $users]);
    }
    public function getLineData(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $products = Product::whereHas(
            'lineproducts',
            fn($q) =>
            $q->whereIntegerInRaw('line_products.line_id', $request->lines)->where('line_products.from_date', '<=', now())
                ->where(fn($q) => $q->where('line_products.to_date', '>', (string)Carbon::now())
                    ->orWhere('line_products.to_date', null))
        )->get();
        $lines = Line::whereIntegerInRaw('id', $request->lines)->get();
        $users = $user->belowUsersOfAllLinesWithPositions($lines, 'Active');
        return $this->respond(['products' => $products, 'users' => $users]);
    }
    public function filter(Request $request)
    {
        $perBrand = Setting::where('key', 'reports_level')->value('value') == 'Brand';

        /**@var User authUser */
        $authUser = Auth::user();
        $material = $request->materialFilter;
        $from = Carbon::parse($material['fromDate'])->startOfDay();
        $to = Carbon::parse($material['toDate'])->endOfDay();
        $types = PromotionalMaterialType::select('id', 'name')
            ->when(!empty($material['types']), fn($q) => $q->whereIn("id", $material['types']))->get();
        $fields = collect([
            'id',
            'title',
            'employee',
            'emp_code',
            'line',
            'type',
            'insertion_date',
            'activity_date',
            'status',
            'product',
            'pro_cost',
        ]);
        $clickable_fields = collect([]);
        $data = $this->statistics($types, $material, $from, $to, $perBrand);
        return response()->json([
            'data' => $data,
            'fields' => $fields,
            'clickable_fields' => $clickable_fields
        ]);
    }
    private function statistics($types, $material, $from, $to, $perBrand)
    {
        $materials = $this->getMaterials($material, $from, $to, $types);
        // throw new CrmException($materials);
        $data = [];
        foreach ($materials as $object) {
            $data[] = [
                'id' => $object->id,
                'title' => $object->title,
                'employee' => $object->employee,
                'emp_code' => $object->emp_code,
                'line' => $object->line,
                'type' => $object->type,
                'insertion_date' => $object->insertion_date,
                'activity_date' => $object->activity_date,
                'status' => $object->status == 1 ? 'Approved' : ($object->status == 0 ? 'DisApproved' : 'Pending'),
                'product' => $perBrand ? $object->brand
                    : $object->product,
                'pro_cost' => round($object?->pro_cost, 2) ?? 0,
            ];
        }
        return $data;
    }
}
