<?php

namespace App\Http\Controllers;

use App\DivisionType;
use App\Exceptions\CrmException;
use App\Helpers\Notifications\NotificationHelper;
use App\Models\ApprovalFlowUser;
use App\Models\ApprovalSetting;
use App\Models\ChangePlan;
use App\Notifications\ChangePlanApprovedNotification;
use App\PlanVisit;
use App\PlanVisitDetails;
use App\Position;
use App\Reason;
use App\Reasonable;
use App\Services\PlanService;
use App\User;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ChangePlanApprovalController extends ApiController
{
    public function getChangePlans(Request $request)
    {
        $from_date = null;
        $to_date = null;
        $reasons = Reason::where('request_type', ChangePlan::class)->get();
        $changePlans = collect([]);
        // if ($request->flag == 1) {
        //     $user = User::find($request->user);
        //     $changePlans =  $changePlans->merge(Vacation::where('user_id', $user->id)
        //         // ->where('from_date', '>=', Carbon::now()->toDateString())
        //         ->whereHas('details', function ($q) {
        //             $q->whereNull('approval');
        //         })
        //         ->get());
        // } else {
        $from_date = Carbon::parse($request->from_date)->startOfDay();
        $to_date = Carbon::parse($request->to_date)->endOfDay();
        $changePlans = ChangePlan::whereIn('user_id', $request->users_id)
            ->where(fn($q) => $q->whereBetween('from', [$from_date, $to_date])
                ->orwhereBetween('to', [$from_date, $to_date]))
            ->whereHas('details', function ($q) {
                $q->whereNull('approval');
            })
            ->get();

        $approvalSetting = ApprovalSetting::where('key', 'change_plan_approval_center_flow')->value('value');
        /**@var User $authUser */
        $authUser = Auth::user();
        $now = Carbon::now()->format('Y-m');
        $approvalData = $authUser->userApprovals($from_date, $to_date);
        $lines = $approvalData['lines'];
        $linesAapprovables = $approvalData['linesAapprovables'];
        $required = 0;
        $dataFlow = '';
        $scanLevel = 1;
        $changePlans = $changePlans->filter(
            function (ChangePlan $changePlan) use ($now, $from_date, $to_date, $lines, $linesAapprovables, $approvalSetting, $scanLevel, $authUser, &$required, $dataFlow) {
                if ($approvalSetting == 'No') {
                    return is_null($changePlan->details?->approval);
                } else {
                    if (isNullable($changePlan->details?->approval)) {
                        $approvablesCountOnThisShit = $changePlan->details?->approvalFlows()->count();
                        $data = $authUser->approvalWidget($changePlan, $authUser, ChangePlan::class, $from_date, $to_date, $lines, $linesAapprovables);
                        $dataFlow = $data['linesDataFlow'];
                        $currentFlow = $dataFlow?->flow;
                        $required = $dataFlow?->required;
                        $showData = $dataFlow?->show_data;
                        $vacantCount = $data['vacantCount'];
                        $haveToApprove = $approvablesCountOnThisShit === $currentFlow - ($vacantCount + $scanLevel);
                        return $haveToApprove || $required || $showData;
                    }
                }
            }
        )
            ->map(function ($changePlan) use ($reasons, $required) {
                return [
                    'id' => $changePlan->id,
                    'employee' => $changePlan->user->fullname,
                    'from' => Carbon::parse($changePlan->from)->format('Y-m-d'),
                    'to' => Carbon::parse($changePlan->to)->format('Y-m-d'),
                    'approval' => $changePlan->details?->approval,
                    'required' => $required,
                    'visitable_type' => ChangePlan::class,
                    'reasons' => $reasons,
                    'reason_id' => null
                ];
            });
        $fields = ['s', 'id', 'employee', 'from', 'to', 'actions'];
        return $this->respond(['changePlans' => $changePlans->values(), 'fields' => $fields]);
    }

    public function accept(Request $request)
    {
        $approvalSetting = ApprovalSetting::where('key', 'change_plan_approval_center_flow')->value('value');
        $all_changePlans = $request->changePlans;

        if (empty($all_changePlans)) {
            throw new CrmException('No Change Plans Found');
        }

        $authUser = Auth::user();
        $users = collect();
        $changePlans = collect();
        DB::transaction(function () use ($all_changePlans, $authUser, $users, $changePlans, $approvalSetting) {
            foreach ($all_changePlans as $changePlan) {
                /**
                 * @var ChangePlan $objChangePlan
                 */
                $objChangePlan = resolve($changePlan['visitable_type'])->find($changePlan['visitable_id']);
                $changePlans = $changePlans->push($objChangePlan);

                $users->push($objChangePlan->user);
                $detail = $objChangePlan->details;
                if ($approvalSetting == 'Yes') {
                    ApprovalFlowUser::firstOrCreate([
                        'detail_id' => $detail->id,
                        'user_id' => $authUser->id,
                        'approval' => 1,
                    ]);
                    if ($changePlan['required'] == 1) {
                        $detail->user_id = $authUser->id;
                        $detail->approval = 1;
                        $detail->save();
                        $this->deletePlans($objChangePlan);
                    }
                } else {
                    $detail->user_id = $authUser->id;
                    $detail->approval = 1;
                    $detail->save();
                    $this->deletePlans($objChangePlan);
                }
                $objChangePlan->user->givePermissionTo(["create_plan_visits"]);
            }
        });



        NotificationHelper::send(
            $users->unique('id'),
            new ChangePlanApprovedNotification('Change Plan Get Approved', auth()->user())
        );
        // LogActivity::addLog();
        return response()->json(['status' => 'success']);
    }
    public function deletePlans($changePlan)
    {
        // throw new CrmException($changePlan);
        $plans = collect([]);
        $period = CarbonPeriod::create($changePlan->from, $changePlan->to);
        foreach ($period as $date) {
            $user = User::find($changePlan->user_id);
            $plans = (new PlanService)->getUserPlansPerDate($user, $date);
            $planIds = $plans->pluck('id');
            PlanVisitDetails::whereIn('visitable_id', $planIds)->where('visitable_type', PlanVisit::class)->delete();
            Reasonable::whereIn('reasonable_id', $planIds)->where('reasonable_type', PlanVisit::class)->delete();
            $plans->each(fn($plan) => $plan->forceDelete());
        }
    }
    public function reject(Request $request)
    {
        $approvalSetting = ApprovalSetting::where('key', 'change_plan_approval_center_flow')->value('value');
        $users = collect([]);
        /**@var User $authUser */
        $authUser = Auth::user();
        foreach ($request->changePlans as $changePlan) {
            $objChangePlan = resolve($changePlan['visitable_type'])->find($changePlan['visitable_id']);
            $users->push($objChangePlan->user);
            $detail = $objChangePlan->details;
            if ($approvalSetting == 'Yes') {
                ApprovalFlowUser::firstOrCreate([
                    'detail_id' => $detail->id,
                    'user_id' => $authUser->id,
                    'approval' => 0,
                ]);
                if ($changePlan['required'] == 1) {
                    $detail->user_id = $authUser->id;
                    $detail->approval = 0;
                    $detail->save();
                }
            } else {
                // throw new CrmException($authUser);
                $detail->user_id = $authUser->id;
                $detail->approval = 0;
                $detail->save();
            }
            if ($changePlan['reason_id'] != null) {
                $reason = Reason::find($changePlan['reason_id']);
                $objChangePlan->reasons()->attach($reason, [
                    'user_id' => $authUser->id,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
        }

        return response()->json(['status' => 'reject']);
    }
    public function filterOfEmployees(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $users = collect([]);
        $types = [DivisionType::class, Position::class];
        foreach ($request->line_id as $line) {
            foreach ($types as $type) {
                $users = $users->merge($user->planableUsers($line, ChangePlan::class, $type));
            }
        }
        return response()->json(['filtered_users' => $users->unique('id')->values()]);
    }
}
