<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Services\AchievementService;

use Illuminate\Http\Request;
use Illuminate\Support\Carbon;

class SalesAchievementReportController extends ApiController
{

    public function __construct(private readonly AchievementService $achievementService) {}

    public function getLines(Request $request)
    {
        $from = Carbon::parse($request->from)->startOfDay();
        $to = Carbon::parse($request->to)->endOfDay();
        return $this->respond($this->achievementService->getLines($from, $to));
    }

    public function getLineData(Request $request)
    {
        $from = Carbon::parse($request->from)->startOfDay();
        $to = Carbon::parse($request->to)->endOfDay();
        // throw new CrmException([$from,$to]);
        return $this->respond($this->achievementService->getLineData($request->lines, $from, $to));
    }


    public function getProductData(Request $request)
    {
        $from = Carbon::parse($request->from)->startOfDay();
        $to = Carbon::parse($request->to)->endOfDay();
        return $this->respond($this->achievementService->getProductData($request->lines, $request->type_id, $from, $to));
    }


    public function filter(Request $request)
    {
        return response()->json($this->achievementService->filter($request->saleFilter));
    }
}
