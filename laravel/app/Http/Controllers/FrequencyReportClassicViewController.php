<?php

namespace App\Http\Controllers;

use App\Account;
use App\ActualVisit;
use App\ClassFrequency;
use App\DivisionType;
use App\Doctor;
use App\Exceptions\CrmException;
use App\Http\Controllers\Controller;
use App\Line;
use App\LineDivision;
use App\Models\ListType;
use App\Shift;
use App\User;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class FrequencyReportClassicViewController extends ApiController
{
    public function getData(Request $request)
    {
        $line = Line::find($request->line);
        $data = $line->classes;
        return $this->respond($data);
    }

    private function doctors($list, $object, $filter, $line, $month, $year, $class, $from, $to, $shifts)
    {
        $divisions = [];
        $doctors = collect([]);
        $division_type = DivisionType::where('last_level', '=', 1)->first()->id;
        if ($filter == 1) {
            $divisions = $object->getBelowDivisions()
                ->where('division_type_id', '=', $division_type)->unique('id')->pluck('id')->toArray();
        }
        if ($filter == 2) {
            $divisions = $object?->allBelowDivisions($line, $from, $to)
                ->where('division_type_id', '=', $division_type)->unique('id')->pluck('id')->toArray();
        }
        if (!empty($divisions)) {
            $setting = ListType::first()->type == 'Default List';
            $doctors = Account::select(
                [
                    'doctors.id as doctor_id',
                    'doctors.id as id',
                    DB::raw('IFNULL(group_concat(distinct crm_accounts.name),"") as account'),
                    DB::raw('IFNULL(group_concat(distinct crm_accounts.id),"") as account_id'),
                    DB::raw('IFNULL(group_concat(distinct crm_accounts.code),"") as acc_code'),
                    DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                    DB::raw('IFNULL(group_concat(distinct crm_account_types.name),"") as account_type'),
                    DB::raw('IFNULL(group_concat(distinct crm_account_types.shift_id),"") as acc_shift_id'),
                    DB::raw('IFNULL(group_concat(distinct crm_account_types.id),"") as account_type_id'),
                    DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                    'lines.name as line',
                    'lines.id as line_id',
                    'line_divisions.name as division',
                    'line_divisions.id as div_id',
                    'doctors.name as doctor',
                    'doctors.ucode as ucode',
                    DB::raw('IFNULL(group_concat(distinct crm_d.name),"") as class'),
                    DB::raw('IFNULL(group_concat(distinct crm_d.id),"") as doc_class_id'),
                    'specialities.name as speciality',
                    'specialities.id as speciality_id',
                    DB::raw("DATE_FORMAT(crm_doctors.active_date,'%Y-%m-%d') as creation_date"),
                ]
            )
                ->leftJoin('account_types', 'accounts.type_id', 'account_types.id');
            if (!$setting) {
                $doctors = $doctors
                    ->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
                    ->leftJoin(
                        'new_account_doctors',
                        function ($join) {
                            $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                            $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id');
                        }
                    );
            } else {
                $doctors = $doctors->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
                    ->leftJoin('new_account_doctors', function ($join) use ($list, $to) {
                        $join->on('accounts.id', 'new_account_doctors.account_id')
                            ->where('new_account_doctors.line_id', $list['line'])
                            ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)
                                ->orWhere('new_account_doctors.to_date', '>=', $to->toDateString()));
                    });
            }
            $doctors = $doctors->leftJoin('doctors', 'new_account_doctors.doctor_id', 'doctors.id')
                ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
                ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
                ->leftJoin('classes as d', 'doctors.class_id', 'd.id')
                ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
                ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
                ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
                ->where(fn($q) => $q->where('accounts.inactive_date', '=', null)->orWhere('accounts.inactive_date', '>=', (string)Carbon::now()))
                ->where(fn($q) => $q->where('doctors.inactive_date', '=', null)->orWhere('doctors.inactive_date', '>=', (string)Carbon::now()))
                ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)->orWhere('new_account_doctors.to_date', '>=', $to->toDateString()))
                ->where('accounts.active_date', '<=', Carbon::now())
                ->where('doctors.active_date', '<=', Carbon::now())
                ->where('new_account_doctors.from_date', '<=', $from->toDateString())
                ->where('new_account_doctors.line_id', $list['line'])
                ->where('account_lines.line_id', $list['line'])
                ->where('d.id', $class->id)
                ->whereIntegerInRaw('line_divisions.id', $divisions)
                ->where('accounts.deleted_at', null)
                ->where('account_lines.deleted_at', null)
                ->where('new_account_doctors.deleted_at', null)
                ->where('doctors.deleted_at', null)
                ->orderBy('doctors.ucode', 'asc');

            if (!empty($list['types'])) {
                $doctors = $doctors->whereIntegerInRaw('account_types.id', $list['types']);
            }
            if (!empty($list['specialities'])) {
                $doctors = $doctors->whereIntegerInRaw('specialities.id', $list['specialities']);
            }
            if (!empty($shifts)) {
                $doctors = $doctors->whereIntegerInRaw('account_types.shift_id', $list['shifts']);
            }
            $doctors = $doctors->groupBy("doctors.id", "specialities.id", 'lines.id', 'line_divisions.id')->get();;
        }
        return $doctors;
    }
    private function frequencyType($doctor, $line, $month, $year)
    {
        // throw new CrmException($line);
        $frequencies = ClassFrequency::select(
            'class_frequencies.id',
            'lines.name as line',
            'classes.name as class',
            'class_frequencies.frequency as frequency',
            DB::raw("DATE_FORMAT(crm_class_frequencies.date,'%Y-%m-%d') as date"),
        )
            ->leftJoin('lines', 'class_frequencies.line_id', 'lines.id')
            ->leftJoin('classes', 'class_frequencies.class_id', 'classes.id')
            ->where('class_frequencies.line_id', $line['id'])
            ->where('class_frequencies.class_id', $doctor['doc_class_id'])
            ->whereBetween(DB::raw("(DATE_FORMAT(crm_class_frequencies.date,'%m'))"), $month)
            ->whereYear('class_frequencies.date', $year)->groupBy('id', 'class_frequencies.frequency')->get();
        // throw new CrmException($frequencies);
        return $frequencies;
    }

    private function actuals($list, $object, $table, $line, $doctors, $month, $year,$from,$to)
    {
        $data = ActualVisit::select(
            'actual_visits.id as id',
            'actual_visits.visit_date',
            'actual_visits.account_dr_id',
            DB::raw("DATE_FORMAT(crm_actual_visits.created_at,'%Y-%m-%d %H:%s:%i') as date"),
            'specialities.id as speciality_id',
            'bricks.name as brick',
            'lines.name as line',
            'line_divisions.name as division',
            'users.fullname as user',
            'accounts.name as account',
            'accounts.id as account_id',
            'account_types.name as acc_type',
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            DB::raw('IFNULL(group_concat(distinct crm_products.name),"") as product'),
            'specialities.name as speciality',
            'visit_types.name as type',
            'plan_visit_details.approval as status'
        )
            ->leftJoin('lines', 'actual_visits.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'actual_visits.div_id', 'line_divisions.id')
            ->leftJoin('users', 'actual_visits.user_id', 'users.id')
            ->leftJoin('accounts', 'actual_visits.account_id', 'accounts.id')
            ->leftJoin('bricks', 'actual_visits.brick_id', 'bricks.id')
            ->leftJoin('account_types', 'actual_visits.acc_type_id', 'account_types.id')
            ->leftJoin('doctors', 'actual_visits.account_dr_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('visit_types', 'actual_visits.visit_type_id', 'visit_types.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('actual_visits.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', 'App\ActualVisit');
                }
            )
            ->leftJoin('actual_visit_products', 'actual_visits.id', 'actual_visit_products.visit_id')
            ->leftJoin('products', 'actual_visit_products.product_id', 'products.id')
            ->where('actual_visits.deleted_at', '=', null)
            ->where('actual_visits.line_id', $line->id)
            ->whereIntegerInRaw('actual_visits.account_dr_id', $doctors)
            ->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null))
            ->where($table, $object->id)
            ->whereBetween('visit_date', [$from, $to]);
        if (!empty($list['types'])) {
            $data = $data->whereIntegerInRaw('actual_visits.acc_type_id', $list['types']);
        }
        if (!empty($list['specialities'])) {
            $data = $data->whereIntegerInRaw('specialities.id', $list['specialities']);
        }
        $data = $data->groupBy('id', 'plan_visit_details.approval')->get();
        return $data;
    }
    public function filter(Request $request)
    {
        $list = $request->listFilter;
        $from = Carbon::parse($list['fromDate'])->startOfDay();
        $to = Carbon::parse($list['toDate'])->endOfDay();
        $month = [Carbon::parse($from)->format('m'), Carbon::parse($to)->format('m')];
        $year = Carbon::parse($from)->format('Y');

        $months = [];
        $period = CarbonPeriod::create($from, '1 month', $to);
        foreach ($period as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $year,
        ];

        $shifts = Shift::when(!empty($list['shifts']), fn($q) => $q->whereIntegerInRaw("shifts.id", $list['shifts']))
            ->get()->pluck('id')->toArray();
        $line = Line::find($list['line']);
        $classes = new Collection([]);
        if ($list['shape'] == 1 && $list['freqType'] == 1) {
            $classes = $line->classes()->when(!empty($list['classes']), fn($q) => $q->whereIn("classes.id", $list['classes']))->get();
        }
        $fields = collect(['line', 'division', 'employee']);
        $clickable_fields = collect([]);

        foreach ($classes as $class) {
            $fields = $fields->push($class->name . ' ' . '(Docs)');
            $fields = $fields->push($class->name . ' ' . '(Target)');
            $fields = $fields->push($class->name . ' ' . '(Visits)');
            $fields = $fields->push($class->name . ' ' . '(Achievements)');
            $clickable_fields = $clickable_fields->push($class->name . ' ' . '(Docs)');
            $clickable_fields = $clickable_fields->push($class->name . ' ' . '(Target)');
            $clickable_fields = $clickable_fields->push($class->name . ' ' . '(Visits)');
            $clickable_fields = $clickable_fields->push($class->name . ' ' . '(Achievements)');
        }
        /**@var User $user */
        $user = Auth::user();
        $data = collect([]);

        $filtered = new Collection([]);
        $data = new Collection([]);
        if ($list['filter'] == 1) {
            $divisions = $line->divisions()->where("deleted_at", null)
                ->when(!empty($list['divisions']), fn($q) => $q->whereIn("line_divisions.id", $list['divisions']))->get();
            $filtered = $filtered->merge($user->filterDivisions($line, $divisions, $list));
        }
        if ($list['filter'] == 2) {
            $users = $line->users($from, $to)->wherePivot("deleted_at", null)
                ->when(!empty($list['users']), fn($q) => $q->whereIn("line_users.user_id", $list['users']))->get();
            $filtered = $filtered->merge($user->filterUsers($line, $users, $list, $from, $to));
        }

        $filtered->each(function ($object) use ($line, $month, $year, $data, $list, $classes, $from, $to, $shifts) {
            $data = $data->push($this->statistics($line, $object, $list, $list['filter'], $month, $year, $classes, $from, $to, $shifts));
        });
        return response()->json([
            'data' => $data->unique("id")->values(),
            'fields' => $fields,
            'dates' => $dates,
            'clickable_fields' => $clickable_fields
        ]);
    }

    private function statistics($line, $object, $list, $filter, $month, $year, $classes, $from, $to, $shifts)
    {
        $frequency = collect([
            'id' => $object->id,
            'line' => $line->name,
            'division' => $filter == 1 ? $object?->name : $object->divisions($from, $to)->where('line_divisions.line_id', $line->id)->pluck('name')->implode(','),
            'employee' => $filter == 1 ? $object->users()?->where('line_users_divisions.line_id', $line->id)->pluck('fullname')->implode(',') : $object?->fullname,
            'color' => $filter == 1 ? $object?->DivisionType->color : $object->division($line)?->DivisionType->color,
        ]);
        $classes->each(function ($class) use ($list, $object, $filter, $line, $month, $year, &$frequency, $from, $to, $shifts) {
            $doctors = $this->doctors($list, $object, $filter, $line, $month, $year, $class, $from, $to, $shifts);
            $doctorIds = $doctors->pluck('id');
            $actuals = $this->actuals($list, $object, $filter == 1 ? 'line_divisions.id' : 'users.id', $line, $doctorIds, $month, $year,$from,$to);
            $freq = $this->frequencies($doctors, $actuals, $list, $line, $month, $year);
            $achievement = $doctors->count() != 0 ? round(($freq['actualCount'] / $doctors->count()) * 100, 2) . ' %' : 0 . ' %';
            $frequency = $frequency->put($class->name . ' ' . '(Docs)', $doctors->count());
            $frequency = $frequency->put($class->name . ' ' . '(Target)', $freq['total']);
            $frequency = $frequency->put($class->name . ' ' . '(Visits)', $actuals->count());
            $frequency = $frequency->put($class->name . ' ' . '(Achievements)', $achievement);
        });
        return $frequency;
    }

    public function frequencies($doctors, $actuals, $list, $line, $month, $year)
    {
        $total = 0;
        $actualCount = 0;
        $doctors->each(function ($doctor) use ($actuals, $list, $line, $month, $year, &$total, &$actualCount) {
            $frequency = $this->frequencyType($doctor, $line, $month, $year);
            $frequency = (int)$frequency->sum('frequency');
            $total += $frequency;
            $actual = $actuals->where('account_dr_id', $doctor->id)->count();
            if ($actual && $actual > $frequency) $actualCount += $frequency;
            if ($actual && $actual <= $frequency) $actualCount += $actual;
        });
        return array(
            'total' => $total,
            'actualCount' => $actualCount,
        );
    }

    public function showData(Request $request)
    {
        $list = $request->listFilter;
        $line = Line::find($list['line']);
        $from = Carbon::parse($list['fromDate'])->startOfDay();
        $to = Carbon::parse($list['toDate'])->endOfDay();
        $shifts = Shift::when(!empty($list['shifts']), fn($q) => $q->whereIntegerInRaw("shifts.id", $list['shifts']))
            ->get()->pluck('id')->toArray();
        $month = [Carbon::parse($from)->format('m'), Carbon::parse($to)->format('m')];
        $year = Carbon::parse($from)->format('Y');
        $column = $request->column;
        $classes = $line->classes()->select('classes.id', 'classes.name')
            ->when(!empty($list['classes']), fn($q) => $q->whereIntegerInRaw("classes.id", $list['classes']))->get();


        $fieldCalls = $classes->map(function ($class) {
            return [
                'class' => $class,
                'docs' => $class->name . ' ' . '(Docs)',
                'visits' => $class->name . ' ' . '(Visits)',
                'target' => $class->name . ' ' . '(Target)',
                'uncov' => $class->name . ' ' . '(uncov)',
            ];
        });
        // throw new CrmException($fieldCalls);
        $object = '';
        if ($request->div != null) {
            $object = LineDivision::find($request->div);
        } else {
            $object = User::find($request->user);
        }

        $data = null;
        foreach ($fieldCalls as $field) {
            $doctors = $this->doctors($list, $object, $list['filter'], $line, $month, $year, $field['class'], $from, $to, $shifts);
            $doctorIds = $doctors->pluck('id');
            $actuals = $this->actuals($list, $object, $list['filter'] == 1 ? 'line_divisions.id' : 'users.id', $line, $doctorIds, $month, $year,$from,$to);
            $freq = $this->frequencyType($field['class'], $line, $month, $year);
            if ($field['visits'] == $column) {
                $data = $actuals->values()->map(function ($actual) {
                    return [
                        'id' => $actual->id,
                        'line' => $actual->line,
                        'division' => $actual->division ? $actual->division : '',
                        'brick' => $actual->brick,
                        'employee' => $actual->user ? $actual->user : '',
                        'account' => $actual->account,
                        'acc_type' => $actual->acc_type,
                        'doctor' => $actual->doctor ? $actual->doctor : '',
                        'speciality' => $actual->speciality,
                        'date' => $actual->date,
                        'type' => $actual->type,
                    ];
                });
            }
            if ($field['docs'] == $column || $field['target'] == $column) {
                $data = $doctors->filter(fn($doctor) => $doctor != null)->values()->map(function ($doctor) use ($actuals, $line, $month, $year) {
                    $frequency = $this->frequencyType($doctor, $line, $month, $year);
                    $frequency = (int)$frequency->sum('frequency');
                    return [
                        'doctor_id' => $doctor->id,
                        'line' => $doctor->line,
                        'division' => $doctor->division ? $doctor->division : '',
                        'brick' => $doctor->brick,
                        'account' => $doctor->account,
                        'account_id' => $doctor->account_id,
                        'acc_type' => $doctor->account_type,
                        'doctor' => $doctor->doctor,
                        'speciality' => $doctor->speciality,
                        'frequency' => $frequency,
                        'actuals' => $actuals->where('account_dr_id', $doctor->id)->count(),
                    ];
                });
            }
        }
        return $this->respond($data);
    }

    private function editDoctorForFrequency(&$doctor, $actual, $frequency, $condition,)
    {
        if ($condition) {
            $doctor->setAttribute('frequency', $frequency ?? '');
            $doctor->setAttribute('actual', $actual ?? '');
        } else {
            $doctor = null;
        }
    }
    private function showAllActual($actuals)
    {
        $actuals = $actuals->map(function ($actual) {
            return [
                'id' => $actual->id,
                'line' => $actual->line,
                'division' => $actual->division ? $actual->division : '',
                'brick' => $actual->brick,
                'employee' => $actual->user ? $actual->user : '',
                'account' => $actual->account,
                'acc_type' => $actual->acc_type,
                'doctor' => $actual->doctor ? $actual->doctor : '',
                'speciality' => $actual->speciality,
                'date' => $actual->date,
                'type' => $actual->type,
            ];
        })->toArray();
        return $actuals;
    }
}
