<?php

namespace App\Http\Controllers;

use App\Distributor;
use App\DivisionType;
use App\Line;
use App\LineBricks;
use App\LineDivision;
use App\Models\SaleFactor;
use App\SalesSetting;
use App\SalesTypes;

class LineDistributorSalesTypeController extends Controller
{
    public function index()
    {
        $lines = Line::all();
        $distributers = Distributor::all();
        $salesTypes = SalesTypes::all();
        $setting = SalesSetting::where('id','=',1)->get(['id','value']);
        $perDivision = SalesSetting::where('key','sales_mapping_level')->value('value') == 'Division';
        $factors = SaleFactor::select('sale_factors.id', 'sale_factors.name')->orderBy('id')->get();
        return response()->json([
            "lines" => $lines,
            "distributors" => $distributers,
            "salesTypes" => $salesTypes,
            "perDivision" => $perDivision,
            'factors'=>$factors,
            "setting" => (int) $setting->first()->value
        ]);
    }
    public function getLineDivisions($line_id)
    {
        $division_type = DivisionType::where('last_level', '=', 1)->get('id');
        $divisions = LineDivision::select('line_divisions.id', 'line_divisions.name')
            ->where('division_type_id', '=', $division_type->first()->id)
            ->where('line_id','=',$line_id)
            ->orderBy('line_divisions.id', 'ASC')
            ->get();
        $bricks = LineDivision::select('line_divisions.id', 'line_divisions.name')
            ->where('division_type_id', '=', $division_type->first()->id)
            ->where('line_id','=',$line_id)
            ->orderBy('line_divisions.id', 'ASC')
            ->with('bricks')->get()->pluck('bricks')->collapse();
        return response()->json(['divisions' => $divisions,'bricks'=>$bricks]);
    }

}
