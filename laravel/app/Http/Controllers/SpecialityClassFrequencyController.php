<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Http\Requests\ImportRequest;
use App\Http\Requests\MailRequest;
use App\Http\Requests\SpecialityClassFrequencyRequest;
use App\Line;
use App\Models\SpecialityClassFrequency;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Excel as ExcelType;

class SpecialityClassFrequencyController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $speciality_class_frequencies = DB::table('speciality_class_frequencies')
            ->select(
                'speciality_class_frequencies.id',
                'lines.name as line',
                'specialities.name as speciality',
                'classes.name as class',
                'speciality_class_frequencies.frequency',
                'speciality_class_frequencies.date'
            )
            ->leftJoin('lines', 'speciality_class_frequencies.line_id', '=', 'lines.id')
            ->leftJoin('specialities', 'speciality_class_frequencies.speciality_id', '=', 'specialities.id')
            ->leftJoin('classes', 'speciality_class_frequencies.class_id', '=', 'classes.id')
            ->get();
        LogActivity::addLog();
        return response()->json(compact('speciality_class_frequencies'));
    }

    public function getLineSpecialities(Line $line)
    {
        $specialities = $line->specialities()->get()->filter(function ($speciality) {
            return $speciality->pivot?->to_date == null || $speciality->pivot?->to_date >= (string)Carbon::now();
        });
        return response()->json(['line_specialities_names' => $specialities]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(SpecialityClassFrequencyRequest $request)
    {
        $speciality_frequency = SpecialityClassFrequency::create($request->validated());

        $model_id = $speciality_frequency->id;
        $model_type = SpecialityClassFrequency::class;

        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\SpecialityClassFrequency  $SpecialityClassFrequency
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $speciality_class_frequency = DB::table('speciality_class_frequencies')
            ->select(
                'speciality_class_frequencies.id',
                'lines.name as line',
                'lines.id as line_id',
                'specialities.name as speciality',
                'specialities.id as speciality_id',
                'classes.name as class',
                'classes.id as class_id',
                'speciality_class_frequencies.frequency',
                'speciality_class_frequencies.date'
            )
            ->leftJoin('lines', 'speciality_class_frequencies.line_id', '=', 'lines.id')
            ->leftJoin('specialities', 'speciality_class_frequencies.speciality_id', '=', 'specialities.id')
            ->leftJoin('classes', 'speciality_class_frequencies.class_id', '=', 'classes.id')
            ->where('speciality_class_frequencies.id', '=', $id)
            ->first();
        // throw new CrmException($speciality_class_frequency);
        $lines = Line::where(fn($q) => $q->where('to_date', '=', null)->orWhere('to_date', '>=', (string)Carbon::now()))
            ->orderBy('sort', 'ASC')->select('lines.id', 'lines.name')->get();
        $line = Line::find($speciality_class_frequency->id);
        $line_specialities_names = $line->specialities()->get()->filter(function ($speciality) {
            return $speciality->pivot?->to_date == null || $speciality->pivot?->to_date >= (string)Carbon::now();
        });
        $classes = $line->classes()->get()->filter(function ($class) {
            return $class->pivot?->to_date == null || $class->pivot?->to_date >= (string)Carbon::now();
        });
        $model_id = $id;
        $model_type = SpecialityClassFrequency::class;

        LogActivity::addLog($model_id, $model_type);
        return response()->json([
            'speciality_class_frequency' => $speciality_class_frequency,
            'lines' => $lines,
            'specialities' => $line_specialities_names,
            'classes' => $classes
        ]);
    }


    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\SpecialityClassFrequency  $SpecialityClassFrequency
     * @return \Illuminate\Http\Response
     */
    public function update(SpecialityClassFrequencyRequest $request, $id)
    {
        $speciality_frequency = SpecialityClassFrequency::find($id);
        $speciality_frequency->line_id = $request->input('line_id');
        $speciality_frequency->class_id = $request->input('class_id');
        $speciality_frequency->speciality_id = $request->input('speciality_id');
        $speciality_frequency->frequency = $request->input('frequency');
        $speciality_frequency->date = $request->input('date');
        $speciality_frequency->save();

        $model_id = $id;
        $model_type = SpecialityClassFrequency::class;

        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\SpecialityClassFrequency  $SpecialityClassFrequency
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        SpecialityClassFrequency::find($id)->delete();
        $model_id = $id;
        $model_type = SpecialityClassFrequency::class;
        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }
    public function import(ImportRequest $request)
    {
        SpecialityClassFrequency::import($request);
        return $this->respondSuccess();
    }
    public function updateByImport(ImportRequest $request)
    {
        SpecialityClassFrequency::import(request: $request, update: true);
        return $this->respondSuccess();
    }

    public function exportxlsx()
    {
        return SpecialityClassFrequency::export(ExcelType::XLSX);
    }

    public function exportcsv()
    {
        return SpecialityClassFrequency::export(ExcelType::CSV);
    }

    public function exportpdf()
    {
        $specialityClassFrequencies = SpecialityClassFrequency::get();
        return SpecialityClassFrequency::exportPdf($specialityClassFrequencies);
    }

    public function sendmail(MailRequest $request)
    {
        $specialityClassFrequencies = SpecialityClassFrequency::get();
        return SpecialityClassFrequency::sendmail($request, $specialityClassFrequencies);
    }
}
