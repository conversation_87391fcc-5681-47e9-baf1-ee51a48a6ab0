<?php

namespace App\Http\Controllers;

use App\Http\Requests\AlertRequest;
use App\Models\Alert;

use App\Models\AlertBy;
use App\Services\Enums\AlertTypeReport;
use Illuminate\Http\Request;
use App\Helpers\LogActivity;
use App\User;
use App\Services\Alerts\AlertReportsService;
use App\Services\Alerts\AlertSendingMethodsService;


class AlertController extends ApiController
{


    public function __construct(
        private readonly AlertSendingMethodsService $alertSendingMethodsService
    )
    {
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $alerts = Alert::with("users")
            ->get()
            ->map(function (Alert $alert) {
                return [
                    "id" => $alert->id,
                    "type" => $alert->type,
                    "employee" => $alert->users->pluck("fullname")->implode(" , "),
                    "user_id" => $alert->users->pluck("id"),
                    "period" => $alert->period,
                    "time" => $alert->time->format("H:i"),
                    "format" => $alert->format,
                    "by" => $alert->by->first()->sending_method,
                    "last_run" => $alert->last_time_run?->format("Y-m-d H:i") ?? "",
                ];
            });
        LogActivity::addLog();
        return $this->respond($alerts);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(AlertRequest $request)
    {

        $alert = Alert::create([
            'period' => $request->period,
            'time' => $request->time,
            'format' => $request->input('format'),
            'type' => $request->type,
        ]);

        $alert->users()->attach($request->users);
        $alert->by()->attach($request->methods);

        $model_id = $alert->id;
        $model_type = alert::class;
        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Models\Alert $alert
     * @return \Illuminate\Http\JsonResponse
     */
    public function edit($id)
    {
        $alert = Alert::select(['id', 'type', 'format', 'time', 'period'])
            ->where('alerts.id', '=', $id)
            ->with(["users:id","by:id"])
            ->first();

        $model_id = $id;
        $model_type = Alert::class;
        LogActivity::addLog($model_id, $model_type);
        return response()->json([
            'alert' => $alert,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Alert $alert
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(AlertRequest $request, $id)
    {
        $alert = Alert::findOrFail($id);
        $alert->update([
            'period' => $request->period,
            'time' => $request->time,
            'format' => $request->input('format'),
            'type' => $request->type,
        ]);

        $alert->users()->sync($request->users);
        $alert->by()->sync($request->methods);

        $model_id = $alert->id;
        $model_type = alert::class;
        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Models\Alert $alert
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {

        $alert = Alert::find($id);
        $alert->users()->detach();
        $alert->by()->detach();
        $alert->delete();

        $model_id = $id;
        $model_type = Alert::class;

        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'Alert Successfully Deleted']);
    }

    public function getUsers()
    {
        $users = User::select(['id', 'fullname'])->get();
        LogActivity::addLog();

        return response()->json($users);
    }

    public function getSendingMethods()
    {
        $methods = AlertBy::select(['id', 'sending_method'])->get();
        LogActivity::addLog();

        return response()->json($methods);
    }

    public function getAlertTypes()
    {
        return response()->json(AlertTypeReport::cases());
    }

    public function sendAlert(Request $request)
    {
        $alert = $request->input('item');
        // throw new CrmException($alert['by']);

        $methods = explode(' , ', $alert['by']);
        // throw new CrmException($methods);

        foreach ($methods as $method) {


            if ($method[0] === 'Internal Messaging') {
                // throw new CrmException($method);
                $this->sendInternalMessage();
            }
            // elseif($method[0] === 'Email'){
            //     $this->sendmail();
            // }
            elseif ($method[0] === 'Widget') {
                // throw new CrmException($method);
                $this->sendWidget();
            }
        }

    }

    public function sendmail()
    {
        $this->alertSendingMethodsService->sendmail();
    }

    public function sendWidget()
    {
        // Log::info('inside widget');
        $this->alertSendingMethodsService->sendWidget();
        return response()->json(['status' => 'success']);
    }


    public function sendInternalMessage()
    {

        // Log::info('inside internal messaging');
        $this->alertSendingMethodsService->sendInternalMessaging();
        return $this->respondSuccess();
    }

    public function search(Request $request)
    {

        $alert_id = $request->input('alert_id');
        $widget_id = $request->input('widget_id');
        $search = $request->input('search');
        $alert = Alert::find($alert_id);
        $alert_period = $alert->period;
        $alert_time = $alert->time;
        $role = $alert->role_filter;
        $users = $alert->user_id;
        foreach ($users as $user) {
            if ($alert->type == 'Without AM Visits') {
                $data = (new AlertReportsService())->withoutVisits($alert_period, 'AM', $alert_time, 2, $alert_id, $search, $widget_id, $user, $role);
            } elseif ($alert->type == 'Without PM Visits') {
                $data = (new AlertReportsService())->withoutVisits($alert_period, 'PM', $alert_time, 2, $alert_id, $search, $widget_id, $user, $role);
            }
        }


        return $data;

    }

}
