<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Http\Controllers\Controller;
use App\Line;
use App\LineDivision;
use App\Models\Attachment;
use App\Models\Expenses\Expense;
use App\Models\Expenses\ExpenseDetails;
use App\Models\Expenses\Types\ExpenseType;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ExpenseStatisticsReportController extends ApiController
{
    private function getExpenses($expense, $object, $table, $from, $to, $type = null)
    {
        $data = DB::table('expenses')->select(
            'expenses.id as id',
            'lines.name as line',
            'users.fullname as user',
            'expenses.amount as amount',
            DB::raw('IFNULL(group_concat(distinct crm_products.name),"") as product'),
            DB::raw('IFNULL(group_concat(distinct crm_expense_details.date),"") as date'),
            DB::raw('IFNULL(group_concat(distinct crm_expense_details.type_id),"") as type_id'),
            DB::raw('IFNULL(group_concat(distinct crm_expense_types.name),"") as type'),
            // DB::raw('IFNULL(sum(crm_expense_details.amount),"") as amount'),
            DB::raw('IFNULL(group_concat(distinct crm_line_divisions.name),"") as division'),
            DB::raw('IFNULL(group_concat(distinct crm_expense_details.description),"") as description'),

        )
            ->leftJoin('users', 'expenses.user_id', 'users.id')
            ->leftJoin('lines', 'expenses.line_id', 'lines.id')
            ->leftJoin('expense_details', 'expenses.id', 'expense_details.expense_id')
            ->leftJoin('expense_types', 'expense_details.type_id', 'expense_types.id')
            ->leftJoin('expense_divisions', 'expenses.id', 'expense_divisions.expense_id')
            ->leftJoin('expense_products', 'expenses.id', 'expense_products.expense_id')
            ->leftJoin('products', 'expense_products.product_id', 'products.id')
            ->leftJoin('line_divisions', 'expense_divisions.div_id', 'line_divisions.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('expenses.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', Expense::class);
                }
            )
            ->leftJoin(
                'paid_requests',
                function ($join) {
                    $join->on('expenses.id', '=', 'paid_requests.paidable_id');
                    $join->where('paid_requests.paidable_type', Expense::class);
                }
            )
            ->where('expenses.deleted_at', '=', null)
            ->whereBetween('expense_details.date', [$from, $to])
            // ->where('expenses.line_id', $expense['line'])
            ->where($table, $object->id);
        if ($type != null) {
            $data = $data->whereRaw("find_in_set($type,crm_expense_details.type_id)");
        }
        if (!empty($expense['products'])) {
            $data = $data->whereIn('expense_products.product_id', $expense['products']);
        }
        $data = match ($expense['approval']) {
            1 => $data->whereNull('plan_visit_details.approval'),
            2 => $data->where('plan_visit_details.approval', 1),
            3 => $data->where('plan_visit_details.approval', 0),
            4 => $data->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null)),
            5 => $data,
        };
        $data = match ($expense['payment']) {
            1 => $data->where('paid_requests.type', 'No'),
            2 => $data->where('paid_requests.type', 'Yes'),
            3 => $data->where('paid_requests.type', 'Partial'),
            4 => $data
        };

        $data = $data->groupBy("id")->get();

        return $data;
    }
    public function getLineData(Line $line)
    {
        /**@var User $user */
        $user = Auth::user();
        $divisions = $user->userDivisions($line)->values();
        $users = $user->belowUsersWithPositions($line);
        $specialities = $line->specialities()->get();
        $products = $line->products()->get();
        $giveaways = $line->giveaways()->get();

        return response()->json([
            'role' => $user,
            'divisions' => $divisions,
            'users' => $users,
            'specialities' => $specialities,
            'products' => $products,
            'giveaways' => $giveaways,
            // 'doctors' => $doctors,
        ]);
    }
    public function filter(Request $request)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $expense = $request->expenseFilter;
        $from = Carbon::parse($expense['fromDate'])->startOfDay();
        $to = Carbon::parse($expense['toDate'])->endOfDay();
        $types = ExpenseType::select('expense_types.id', 'expense_types.name')
            ->when(!empty($expense['types']), fn($q) => $q->whereIn("expense_types.id", $expense['types']))->get();
        $fields = collect(['line', 'division', 'employee', 'emp_code']);
        $clickable_fields = collect([]);
        foreach ($types as $type) {
            $fields = $fields->push($type->name . ' ' . '(number)');
            $fields = $fields->push($type->name . ' ' . '(amount)');
            $clickable_fields = $clickable_fields->push($type->name . ' ' . '(number)');
            $clickable_fields = $clickable_fields->push($type->name . ' ' . '(amount)');
        }
        $fields = $fields->merge(['total_number', 'total_amount']);
        $clickable_fields = $clickable_fields->merge(['total_number', 'total_amount']);

        $filtered = new Collection([]);
        $data = new Collection([]);
        $lines = Line::when(!empty($expense['lines']), fn($q) => $q->whereIntegerInRaw("lines.id", $expense['lines']))->get();
        foreach ($lines as $line) {
            if ($expense['filter'] == 1) {
                $divisions = $line->divisions()->where("deleted_at", null)
                    ->when(!empty($expense['divisions']), fn($q) => $q->whereIn("line_divisions.id", $expense['divisions']))->get();
                $filtered = $filtered->merge($authUser->filterDivisions($line, $divisions, $expense));
            }
            if ($expense['filter'] == 2) {
                $users = $line->users()->wherePivot("deleted_at", null)
                    ->when(!empty($expense['users']), fn($q) => $q->whereIn("line_users.user_id", $expense['users']))->get();
                $filtered = $filtered->merge($authUser->filterUsers($line, $users, $expense));
            }
        }

        // $filtered->unique('id')->values()->each(function ($object) use ($expense, $data, $from, $to, $types) {
        // });
        $data = $this->statistics($filtered->unique('id')->values(), $types, $expense, $from, $to);
        return response()->json([
            'data' => $data->unique("id")->values(),
            'fields' => $fields,
            'clickable_fields' => $clickable_fields
        ]);
    }
    private function statistics($objects, $types, $expense, $from, $to)
    {
        $lines = [];
        $totalData = collect([]);
        $results = collect([]);
        foreach ($objects as $object) {
            $totalNumbers = 0;
            $totalAmounts = 0;
            if ($expense['filter'] == 2) {
                $lines = $object->lines->pluck('id')->toArray();
            } else {
                $lines = $object->line()->get()->pluck('id')->toArray();
            }

            $data = collect([
                'id' => $object->id,
                'line' => $expense['filter'] == 1 ? $object?->line?->name : $object->lines()->whereIntegerInRaw('line_users.line_id', $lines)->pluck('name')->implode(','),
                'division' => $expense['filter'] == 1 ? $object?->name : $object->divisions()->whereIntegerInRaw('line_divisions.line_id', $lines)->pluck('name')->implode(','),
                'employee' => $expense['filter'] == 1 ? $object->users()?->whereIntegerInRaw('line_users_divisions.line_id', $lines)->pluck('fullname')->implode(',') : $object?->fullname,
                'emp_code' => $object?->emp_code ?? '',
                'color' => $expense['filter'] == 1 ? $object?->DivisionType->color : $object->divisions()?->first()?->DivisionType->color,
            ]);
            $types->each(function ($type) use (&$data, $expense, $object, &$totalAmounts, &$totalNumbers, $from, $to, $totalData) {
                $expenses = $this->getExpenses($expense, $object, $expense['filter'] == 1 ? 'line_divisions.id' : 'users.id', $from, $to, $type->id);
                $countType = $expenses->count();
                $sumAmount = $expenses->sum('amount');
                $totalNumbers += $countType;
                $totalAmounts += $sumAmount;
                $data->put($type->name . ' ' . '(number)', $countType);
                $data->put($type->name . ' ' . '(amount)', round($sumAmount, 2));

                //////////////////////////////////////////////////////////////////
                if ($totalData->has($type->name . ' ' . '(number)')) {
                    $totalData->transform(function ($item, $key) use ($type, $countType) {
                        if ($key == $type->name . ' ' . '(number)') {
                            return $item + $countType;
                        } else {
                            return round($item, 2);
                        }
                    });
                } else {
                    $totalData->put($type->name . ' ' . '(number)', round($countType, 2) ?? 0);
                }
                if ($totalData->has($type->name . ' ' . '(amount)')) {
                    $totalData->transform(function ($item, $key) use ($type, $sumAmount) {
                        if ($key == $type->name . ' ' . '(amount)') {
                            return $item + $sumAmount;
                        } else {
                            return round($item, 2);
                        }
                    });
                } else {
                    $totalData->put($type->name . ' ' . '(amount)', round($sumAmount, 2) ?? 0);
                }
                $data->put($type->name . ' ' . '(number)', $countType);
                $data->put($type->name . ' ' . '(amount)', round($sumAmount, 2));
                $data->put('total_number', $totalNumbers);
                $data->put('total_amount', round($totalAmounts, 2));
            });
            if ($totalData->has('total_numbers')) {
                $totalData->transform(function ($item, $key) use ($totalNumbers) {
                    if ($key == 'total_numbers') {
                        return round(($item + $totalNumbers), 2);
                    } else {
                        return round($item, 2);
                    }
                });
            } else {
                $totalData->put('total_numbers', round($totalNumbers, 2) ?? 0);
            }

            if ($totalData->has('total_amount')) {
                $totalData->transform(function ($item, $key) use ($totalAmounts) {
                    if ($key == 'total_amount') {
                        return round(($item + $totalAmounts), 2);
                    } else {
                        return round($item, 2);
                    }
                });
            } else {
                $totalData->put('total_amount', round($totalAmounts, 2) ?? 0);
            }
            $results =  $results->push($data);
        }
        $results = $results->push(
            collect([
                'id' => '',
                'line' => '',
                'division' => '',
                'employee' => 'Total',
                'emp_code' => '',
                'total_number' => $totalData->get('total_numbers'),
                'total_amount' => $totalData->get('total_amount'),

            ])->merge(
                $types->mapWithKeys(fn($item) => [$item->name . ' ' . '(number)' => $totalData->get($item->name . ' ' . '(number)')]),
            )
                ->merge(
                    $types->mapWithKeys(fn($item) => [$item->name . ' ' . '(amount)' => $totalData->get($item->name . ' ' . '(amount)')])
                )
        );
        // throw new CrmException($results);
        return $results;
    }
    public function showData(Request $request)
    {
        $expense = $request->listFilter;
        // $line = Line::find($expense['line']);
        $column = $request->column;
        $from = Carbon::parse($expense['fromDate'])->startOfDay();
        $to = Carbon::parse($expense['toDate'])->endOfDay();

        $types = ExpenseType::select('expense_types.id', 'expense_types.name')
            ->when(!empty($expense['types']), fn($q) => $q->whereIn("expense_types.id", $expense['types']))->get();
        $fieldCalls = $types->map(function ($type) {
            return [
                'type_id' => $type->id,
                'number' => $type->name . ' ' . '(number)',
                'amount' => $type->name . ' ' . '(amount)',
            ];
        });
        $object = '';
        $expenses = collect([]);
        if ($request->div != null) {
            $object = LineDivision::find($request->div);
        } else {
            $object = User::find($request->user);
        }

        $data = null;


        foreach ($fieldCalls as $field) {
            $expenses = $this->getExpenses($expense, $object, $expense['filter'] == 1 ? 'line_divisions.id' : 'users.id', $from, $to, $field['type_id']);
            if ($field['number'] == $column) {
                $data = $expenses->values()->map(function ($expense) {
                    return [
                        'id' => $expense->id,
                        'line' => $expense->line,
                        'division' => $expense->division ?? '',
                        'employee' => $expense->user,
                        'product' => $expense->product ?? '',
                        'type' => $expense->type,
                        'description' => $expense->description ?? '',
                        'status' => $this->getStatus($expense->id),
                        'feedback' => $this->getFeedbacks($expense->id),
                    ];
                });
                return $this->respond($data);
            }
            if ($field['amount'] == $column) {
                $data = $expenses->values()->map(function ($expense) {
                    return [
                        'id' => $expense->id,
                        'line' => $expense->line,
                        'division' => $expense->division,
                        'employee' => $expense->user,
                        'product' => $expense->product ?? '',
                        'type' => $expense->type,
                        'distance' => $expense->distance ?? '',
                        'amount' => $expense->amount,
                        'status' => $this->getStatus($expense->id),
                        'feedback' => $this->getFeedbacks($expense->id),
                    ];
                });
                return $this->respond($data);
            }
            if ($column == 'total_number') {
                $expenses = $this->getExpenses($expense, $object, $expense['filter'] == 1 ? 'line_divisions.id' : 'users.id', $from, $to);
                $data = $expenses->values()->map(function ($expense) {
                    // $expense = Expense::find($expense->id);
                    // $data = $expense->details->map(function ($detail) use ($expense) {
                    return [
                        'id' => $expense->id,
                        'line' => $expense->line,
                        'division' => $expense->division,
                        'employee' => $expense->user,
                        'product' => $expense->product ?? '',
                        'distance' => $expense->distance ?? '',
                        'amount' => $expense->amount,
                        'status' => $this->getStatus($expense->id),
                        'feedback' => $this->getFeedbacks($expense->id),
                    ];
                });
                // });
                return $this->respond($data);
            }
            if ($column == 'total_amount') {
                $data = collect([]);
                $expenses = $this->getExpenses($expense, $object, $expense['filter'] == 1 ? 'line_divisions.id' : 'users.id', $from, $to);
                $expenses->values()->each(function ($expense) use (&$data) {
                    $expense = Expense::find($expense->id);
                    $data = $data->merge($expense->details->map(function ($detail) use ($expense) {
                        return [
                            'id' => $expense->id,
                            'type' => $detail->type?->name ?? '',
                            'from' => $detail->location?->source?->name ?? '',
                            'to' => $detail->location?->destination?->name ?? '',
                            'distance' => $detail->distance ?? '',
                            'amount' => $detail->amount,
                            'meal' => $detail->meal->name ?? '',
                            'meal_price' => $detail->meal->price ?? '',
                            'desc' => $detail->description ?? '',
                            'file' => Attachment::where('attachable_id', $detail->id)
                                ->where('attachable_type', ExpenseDetails::class)->get()->pluck('url') ?? '',
                            'status' => $detail->expense->approvals?->approval,

                        ];
                    }));
                });
                return $this->respond($data);
            }
        }
    }

    public function deleteExpenseDetail(ExpenseDetails $expenseDetail)
    {
        // throw new CrmException(count($expenseDetail->expense->details) == 0);
        $expense = $expenseDetail->expense;
        $expenseDetail->forceDelete();
        if (count($expense->details) == 0) $expense->forceDelete();
        return $this->respondSuccess();
    }
    private function getStatus($id)
    {
        $expense = Expense::find($id);
        if ($expense->approvals?->approval === null) return null;
        if ($expense->approvals?->approval === 1) return 1;
        if ($expense->approvals?->approval === 0) return 0;
    }
    private function getFeedbacks($id)
    {
        $expense = Expense::find($id);
        if (count($expense->feedbacks) > 0)  return $expense->feedbacks->pluck('feedback');
        else return '';
    }
}
