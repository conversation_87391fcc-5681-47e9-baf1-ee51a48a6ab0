<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Http\Requests\ImportRequest;
use App\Http\Requests\TargetDetailsRequest;
use App\Line;
use App\SalesSetting;
use App\TargetDetails;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class TargetDetailsController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {

        $target_details = TargetDetails::select([
            'target_details.id',
            'lines.name as line',
            'line_divisions.name as division',
            'products.name as product',
            'target_details.target',
            'target_details.date',
            DB::raw('IFNULL(crm_bricks.name,"") AS brick'),
            DB::raw('IFNULL(crm_target_types.name,"") AS type'),
        ])
            ->leftJoin('lines', 'target_details.line_id', 'lines.id')
            ->leftJoin('target_types', 'target_details.type_id', 'target_types.id')
            ->leftJoin('line_divisions', 'target_details.div_id', 'line_divisions.id')
            ->leftJoin('products', 'target_details.product_id', 'products.id')
            ->leftJoin('bricks', 'target_details.brick_id', 'bricks.id')
            ->where(
                fn ($q) => $q->where('line_divisions.name', 'Like', '%' . request('query') . '%')
                    ->orWhere('lines.name', 'Like', '%' . request('query') . '%')
                    ->orWhere('products.name', 'Like', '%' . request('query') . '%')
                    ->orWhere('target_types.name', 'Like', '%' . request('query') . '%')
                    ->orWhere('target_details.date', 'Like', '%' . request('query') . '%')
            )
            ->paginate(300);
        LogActivity::addLog();

        return $this->respond($target_details);
    }

    public function linesAndTypes()
    {
        $lines = DB::table('lines')->select('lines.id', 'lines.name')
            ->where('deleted_at', null)
            ->where(fn ($q) => $q->where('to_date', '=', null)->orWhere('to_date', '>=', (string)Carbon::now()))
            ->orderBy('id', 'ASC')->get();
        $types = DB::table('target_types')->select('target_types.id', 'target_types.name')
            ->where('deleted_at', null)
            ->orderBy('id', 'ASC')->get();

        return response()->json(['lines' => $lines, 'types' => $types]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(TargetDetailsRequest $request)
    {
        // foreach ($request->validated()['percent'] as $target_details) {
            //     TargetDetails::insert($target_details);
            // }
            foreach ($request->validated()['percent'] as $target_details) {
                foreach ($target_details as $target_detail) {
                // throw new CrmException($target_detail);
                if ($target_detail['id'] != null) {
                    $object = TargetDetails::find($target_detail['id']);
                    $object->update($target_detail);
                    continue;
                }
                TargetDetails::create($target_detail);
            }
        }
        LogActivity::addLog();
        return $this->respond();
    }

    public function getLineData(Line $line)
    {

        $setting_target_details = DB::table('sales_settings')->select('sales_settings.key', 'sales_settings.value')->where('key', '=', 'target_level')->get();
        $setting = DB::table('sales_settings')->select('sales_settings.key', 'sales_settings.value')->where('key', '=', 'contribution_per_product_or_line')->get();
        $data_rows = null;
        $data_cols = null;
        if ($setting_target_details->first()->value == 'Brick') {
            $data_rows = $line->bricks;
        }
        if ($setting_target_details->first()->value == 'Division') {
            $division_type = DB::table('division_types')->select('division_types.id')->where('last_level', '=', 1)->get();
            $data_rows = DB::table('line_divisions')->select('line_divisions.id', 'line_divisions.name')
                ->where('division_type_id', '=', $division_type->first()->id)
                ->where('line_id', '=', $line->id)
                ->orderBy('line_divisions.id', 'ASC')
                ->get();
        }
        if ($setting->first()->value == 'Product') {
            $data_cols = DB::table('products')->select('products.id', 'products.name')->orderBy('id', 'ASC')->get();
        }
        if ($setting->first()->value == 'Line') {
            $data_cols = DB::table('lines')->select('lines.id', 'lines.name')->where('id', '=', $line->id)
                ->orderBy('lines.id', 'ASC')->get();
        }
        return response()->json(['data_rows' => $data_rows, 'data_cols' => $data_cols, 'setting_target_details' => $setting_target_details, 'setting' => $setting]);
    }

    public function show(Request $request)
    {
        $target_level = SalesSetting::select('sales_settings.key', 'sales_settings.value')->where('key', '=', 'target_level')->value('value');
        $contribution_per_product_or_line = SalesSetting::select('sales_settings.key', 'sales_settings.value')
            ->where('key', '=', 'contribution_per_product_or_line')->value('value');
        $month = Carbon::parse($request->date)->format('m');
        $year = Carbon::parse($request->date)->format('Y');
        $targets = TargetDetails::where('line_id', $request->line)->where('type_id', $request->type)->whereMonth('date', $month)->whereYear('date', $year);
        if ($target_level == 'Brick' && $contribution_per_product_or_line == 'Product') {
            $targets = $targets->where('div_id', null);
        }
        if ($target_level == 'Brick' && $contribution_per_product_or_line == 'Line') {
            $targets = $targets->whereNull('div_id')->whereNull('product_id');
        }
        if ($target_level == 'Division' && $contribution_per_product_or_line == 'Product') {
            $targets = $targets->whereNull('brick_id');
        }
        if ($target_level == 'Division' && $contribution_per_product_or_line == 'Line') {
            $targets = $targets->whereNull('brick_id')->whereNull('product_id');
        }

        return response()->json(['targets' => $targets->get()]);
    }

    public function import(ImportRequest $import)
    {
        TargetDetails::import($import);
        return $this->respondSuccess();
    }

    public function getTargetDetailsToDelete(Request $request)
    {

        // throw new CrmException($request->all());

        $target_details = TargetDetails::select([
            'target_details.id',
            'lines.name as line',
            'line_divisions.name as division',
            'products.name as product',
            'target_details.target',
            'target_details.date',
            DB::raw('IFNULL(crm_target_types.name,"") AS type'),
        ])
            ->leftJoin('lines', 'target_details.line_id', 'lines.id')
            ->leftJoin('target_types', 'target_details.type_id', 'target_types.id')
            ->leftJoin('line_divisions', 'target_details.div_id', 'line_divisions.id')
            ->leftJoin('products', 'target_details.product_id', 'products.id')
            ->whereNull('target_details.deleted_at')
            ->where(
                fn ($q) =>
                $q->whereIn('lines.id', $request['line'])
            );

        if ($request['divisions']) {
            $target_details = $target_details->where('line_divisions.id', '=', $request['divisions']);
        }

        if ($request['products']) {
            $target_details = $target_details->where('products.id', '=', $request['products']);
        }

        if ($request['fromDate'] && $request['toDate']) {
            $target_details = $target_details->whereBetween('target_details.date', [$request['fromDate'], $request['toDate']]);
        }

        $target_details = $target_details->get();
        // ->unique('id')->values()->pluck('id');

        // throw new CrmException($target_details);

        foreach ($target_details as $target) {
            $target->delete();
        }
        // throw new CrmException($target_details);

        LogActivity::addLog();

        return $this->respond($target_details);
    }
}
