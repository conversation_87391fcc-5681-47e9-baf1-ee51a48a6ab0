<?php

namespace App\Http\Controllers;

use App\Distributor;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Http\Controllers\Controller;
use App\Line;
use App\LineBricks;
use App\LineDivision;
use App\LineProduct;
use App\Mapping;
use App\Models\Distributors\DistributorLine;
use App\Product;
use App\Productprice;
use App\Sale;
use App\SalesSetting;
use App\SalesTypes;
use App\Services\Enums\Ceiling;
use App\Setting;
use App\User;
use Carbon\CarbonPeriod;
use Illuminate\Support\Carbon;
use DateInterval;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SalesSummeryReportController extends ApiController
{
    public function getLines()
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = $user->userLines();
        $types = SalesTypes::get();
        return $this->respond(compact('lines', 'types'));
    }
    public function getLineData(Request $request)
    {
        $user = Auth::user();
        $divisionTypeId = DivisionType::where('last_level', 1)->value('id');
        $divisions = $this->getDivisions($user, $request->lines, $divisionTypeId);
        $distributors = $this->getDistributors($request->lines);
        $products = $this->getProducts($user, $request->lines, $divisions);

        return $this->respond(compact('distributors', 'divisions', 'products'));
    }
    public function filter(Request $request)
    {
        $user = Auth::user();
        $saleFilter = $request->saleFilter;
        $period = $this->getPeriod($saleFilter);
        $distributors = $this->getFilteredDistributors($saleFilter);
        $fields = $this->getFields($distributors);
        $divisions = $this->getFilteredDivisions($user, $saleFilter, $period);
        $products = $this->getFilteredProducts($user, $saleFilter, $period);
        $salesPerDistributor = $this->isSalesPerDistributor();

        $sales = $this->productSale($products, $saleFilter, $distributors, $period, $divisions, $salesPerDistributor);
        foreach ($period as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $year = Carbon::parse($saleFilter['fromDate'])->format('Y');
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $year,
        ];
        LogActivity::addLog();
        return $this->respond(compact('sales', 'fields', 'dates'));
    }

    private function getDivisions(User $user, array $lineIds, int $divisionTypeId): Collection
    {
        return collect($lineIds)
            ->map(function ($lineId) use ($user, $divisionTypeId) {
                $line = Line::find($lineId);
                return $user->userDivisions($line)->where('division_type_id', $divisionTypeId);
            })
            ->collapse()
            ->unique('id')
            ->values();
    }

    private function getDistributors(array $lineIds): Collection
    {
        return DistributorLine::whereIntegerInRaw('line_id', $lineIds)
            ->where('from_date', '<=', now())
            ->where(fn($q) => $q->whereNull('to_date')->orWhere('to_date', '>=', now()))
            ->with('distributor')
            ->get()
            ->pluck('distributor')
            ->filter()
            ->unique('id')
            ->values();
    }

    private function getProducts(User $user, array $lineIds, Collection $divisions): Collection
    {
        $setting = Setting::where('key', 'product_with_line_or_division')->value('value');
        return collect($lineIds)
            ->map(function ($lineId) use ($user, $setting, $divisions) {
                $line = Line::find($lineId);
                return $setting == 'Division'
                    ? $user->userDivisionProducts($line, $divisions->pluck('id')->toArray())
                    : $user->userProducts($line);
            })
            ->collapse()
            ->unique('id')
            ->values();
    }

    private function getPeriod(array $saleFilter): CarbonPeriod
    {
        $from = Carbon::parse($saleFilter['fromDate'])->startOfDay();
        $to = Carbon::parse($saleFilter['toDate'])->endOfDay();
        return CarbonPeriod::create($from, '1 month', $to);
    }

    private function getFilteredDistributors(array $saleFilter): Collection
    {
        $query = Distributor::whereNull('deleted_at');
        if (!empty($saleFilter['distributors'])) {
            $query->whereIntegerInRaw('id', $saleFilter['distributors']);
        }
        return $query->get();
    }

    private function getFields(Collection $distributors): Collection
    {
        $baseFields = collect(['line', 'pro_id', 'pro_code', 'name']);
        $distributorFields = $distributors->flatMap(fn($distributor) => [
            "{$distributor->name} Unit",
            "{$distributor->name} Value"
        ]);
        return $baseFields->merge($distributorFields)->merge(['total_units', 'total_values']);
    }

    private function getFilteredDivisions(User $user, array $saleFilter, CarbonPeriod $period): array
    {
        $divisions = LineDivision::whereIntegerInRaw('line_id', $saleFilter['lines'])
            ->where('from_date', '<=', $period->getStartDate())
            ->whereNull('deleted_at')
            ->where(fn($q) => $q->whereNull('to_date')->orWhere('to_date', '>=', $period->getEndDate()))
            ->when(!empty($saleFilter['divisions']), fn($q) => $q->whereIn("line_divisions.id", $saleFilter['divisions']))
            ->get();

        return collect($saleFilter['lines'])
            ->flatMap(fn($lineId) => $user->filterDivisions(Line::find($lineId), $divisions, $saleFilter, $period->getStartDate(), $period->getEndDate()))
            ->where('division_type_id', DivisionType::where('last_level', 1)->value('id'))
            ->unique('id')
            ->pluck('id')
            ->toArray();
    }

    private function getFilteredProducts(User $user, array $saleFilter, $period): Collection
    {
        $query = Product::select(['products.id', 'products.ucode as pro_code', 'products.name']);

        if (!$user->hasPosition() || ($user->hasPosition() && !$this->userHasProductsInLines($user, $saleFilter['lines']))) {
            $query = $this->addLineProductJoins($query, $saleFilter, $period);
        } elseif (!$user->hasRole('admin') && $user->hasPosition() && $this->userHasProductsInLines($user, $saleFilter['lines'])) {
            $query = $this->addPositionProductJoins($query, $user, $saleFilter);
        }

        if (!empty($saleFilter['products'])) {
            $query->whereIntegerInRaw('products.id', $saleFilter['products']);
        }

        return $query->get()->values();
    }

    private function userHasProductsInLines(User $user, array $lineIds): bool
    {
        return $user->userPosition->first()->products()
            ->whereHas('lines', fn($q) => $q->whereIntegerInRaw("lines.id", $lineIds))
            ->exists();
    }

    private function addLineProductJoins($query, array $saleFilter, $period)
    {
        return $query->addSelect(
            'line_products.from_date',
            'lines.name as line',
            'lines.id as line_id',
            'line_products.to_date'
        )->leftJoin('line_products', 'products.id', 'line_products.product_id')
            ->leftJoin('lines', 'line_products.line_id', 'lines.id')
            ->whereNull('line_products.deleted_at')
            ->where(function ($query) use ($period) {
                $query->where(function ($subQuery) use ($period) {
                    $subQuery->whereNull('line_products.to_date') // Active records
                        ->orWhereBetween('line_products.to_date', [$period->getStartDate()->toDateString(), $period->getEndDate()->toDateString()]) // Ends within range
                        ->orWhere('line_products.to_date', '>=', $period->getEndDate()->toDateString()); // Ends within range
                })
                    ->where(function ($subQuery) use ($period) {
                        $subQuery->where('line_products.from_date', '<=', $period->getStartDate()->toDateString()) // Starts before range
                            ->orWhereBetween('line_products.from_date', [$period->getStartDate()->toDateString(), $period->getEndDate()->toDateString()]); // Starts within range
                    });
            })
            // ->where('line_products.from_date', '<=', now())
            // ->where(fn($q) => $q->whereNull('line_products.to_date')->orWhere('line_products.to_date', '>=', now()))
            ->when(!empty($saleFilter['lines']), fn($q) => $q->whereIntegerInRaw('lines.id', $saleFilter['lines']));
    }

    private function addPositionProductJoins($query, User $user, array $saleFilter)
    {
        return $query->leftJoin('line_products', 'products.id', 'line_products.product_id')
            ->leftJoin('line_product_positions', function ($join) {
                $join->on('line_product_positions.product_id', '=', 'line_products.product_id');
                $join->on('products.id', '=', 'line_product_positions.product_id');
            })
            ->leftJoin('user_positions', function ($join) use ($user) {
                $join->on('line_product_positions.user_position_id', '=', 'user_positions.id');
                $join->where('user_positions.user_id', $user->id);
            })
            ->leftJoin('line_user_positions', 'user_positions.id', 'line_user_positions.user_position_id')
            ->leftJoin('lines', function ($join) {
                $join->on('line_user_positions.line_id', '=', 'lines.id');
                $join->on('line_products.line_id', '=', 'lines.id');
            })
            ->whereNull('line_products.deleted_at')
            ->when(!empty($saleFilter['lines']), fn($q) => $q->whereIntegerInRaw('lines.id', $saleFilter['lines']));
    }

    private function isSalesPerDistributor(): bool
    {
        return SalesSetting::where('key', 'mapping_with_distributor')->value('value') == 'Yes';
    }

    private function productSale($products, $saleFilter, $distributors, $period, $divisions, $salesPerDistributor)
    {
        $sales = collect();
        $distTotalSale = collect();
        $products = $products->unique('id')->values();

        foreach ($products as $product) {
            $mySale = $this->initializeSale($product);
            $totalUnits = 0;
            $totalValues = 0;

            foreach ($distributors as $distributor) {
                [$quantity, $value] = $this->calculateSaleForDistributor($product, $distributor, $period, $divisions, $saleFilter, $salesPerDistributor);
                $totalUnits += $quantity;
                $totalValues += $value;

                $this->updateDistributorTotals($distTotalSale, $distributor, $quantity, $value);
                $this->updateMySale($mySale, $distributor, $quantity, $value, $totalUnits, $totalValues);
            }

            $this->updateTotalSales($distTotalSale, $totalUnits, $totalValues);
            $sales->push($mySale);
        }

        $sales->push($this->createTotalRow($distTotalSale, $distributors));

        return $sales;
    }

    private function initializeSale($product): Collection
    {
        return collect([
            'pro_id' => $product->id,
            'line' => $product->line,
            'pro_code' => $product->pro_code,
            'name' => $product->name,
        ]);
    }

    private function calculateSaleForDistributor($product, $distributor, $period, $divisions, $saleFilter, $salesPerDistributor): array
    {
        $quantity = 0;
        $value = 0.0;

        $productPrices = ProductPrice::where('product_id', $product->id)
            ->where(fn($q) => $q->whereNull('product_prices.distributor_id')
                ->orWhere('product_prices.distributor_id', $distributor->id))->get();

        foreach ($period as $month) {
            $validPrice = $this->getValidPrice($productPrices, $month);
            $salesData = $this->getSalesData($product, $distributor, $month, $divisions, $saleFilter, $salesPerDistributor, $validPrice);

            $quantity += $salesData->sum('quantity');
            $value += $salesData->sum('value') ?: $salesData->sum('sales_value');
        }

        return [$quantity, $value];
    }

    private function getValidPrice($productPrices, $month)
    {
        return $productPrices->first(fn($price) => $month->between($price->from_date, $price->to_date));
    }

    private function getSalesData($product, $distributor, $month, $divisions, $saleFilter, $salesPerDistributor, $validPrice)
    {
        $query = Sale::select([
            'sales.id as sale_id',
            'sales_details.id as detail_id',
            'sales_details.div_id as div_id',
            'sales_details.brick_id as brick_id',
            'sales.product_id',
            'sales_details.date',
            'products.name as product',
            'sales_details.quantity as quantity',
            'sales_details.value as value',
            'product_prices.avg_price as avg_price'
        ])
            ->where('sales.product_id', $product->id)
            ->whereMonth('sales_details.date', $month->format("m"))
            ->whereYear('sales_details.date', $month->year)
            ->whereIn("ceiling", [Ceiling::DISTRIBUTED, Ceiling::BELOW]);

        $query = $salesPerDistributor
            ? $this->addSalesPerDistributorJoins($query, $distributor)
            : $this->addRegularSalesJoins($query, $distributor);

        $query = $this->addCommonJoins($query, $validPrice)
            ->selectRaw('crm_product_prices.avg_price * crm_sales_details.quantity as sales_value')
            ->when(!empty($saleFilter['lines']), fn($q) => $q->where(fn($q) => $q->whereIntegerInRaw('mappings.line_id', $saleFilter['lines'])->orWhereNull('mappings.line_id')))
            ->when($saleFilter['type'], fn($q) => $q->where('mappings.mapping_type_id', $saleFilter['type']))
            ->when(!empty($saleFilter['divisions']), fn($q) => $q->whereIntegerInRaw('sales_details.div_id', $saleFilter['divisions']))
            ->when(empty($saleFilter['divisions']), fn($q) => $q->whereIntegerInRaw('sales_details.div_id', $divisions));

        return $query->get()->unique('detail_id')->values();
    }

    private function addSalesPerDistributorJoins($query, $distributor)
    {
        return $query->selectRaw('crm_mappings.distributor_id')
            ->leftJoin('mapping_sale', 'sales.id', 'mapping_sale.sale_id')
            ->leftJoin('mappings', 'mapping_sale.mapping_id', 'mappings.id')
            ->where(fn($q) => $q->where('mappings.distributor_id', $distributor->id)->orWhereNull('mappings.distributor_id'));
    }

    private function addRegularSalesJoins($query, $distributor)
    {
        return $query->selectRaw('crm_sales.distributor_id')
            ->leftJoin('mapping_sale', 'sales.id', 'mapping_sale.sale_id')
            ->leftJoin('mappings', 'mapping_sale.mapping_id', 'mappings.id')
            ->where('sales.distributor_id', $distributor->id);
    }

    private function addCommonJoins($query, $validPrice)
    {
        return $query->leftJoin('sales_details', 'sales.id', 'sales_details.sale_id')
            ->leftJoin('products', 'sales.product_id', 'products.id')
            ->leftJoin(
                'product_prices',
                function ($join) use ($validPrice) {
                    $join->on('products.id', '=', 'product_prices.product_id')
                        ->where(fn($q) => $q->where('product_prices.distributor_id', $validPrice?->distributor_id)
                            ->orWhereNull('product_prices.distributor_id'))
                        ->where('product_prices.avg_price', $validPrice?->avg_price);
                }
            );
    }

    private function updateDistributorTotals(&$distTotalSale, $distributor, $quantity, $value)
    {
        $unitKey = "{$distributor->name} Unit";
        $valueKey = "{$distributor->name} Value";

        $distTotalSale[$unitKey] = ($distTotalSale[$unitKey] ?? 0) + round($quantity, 2);
        $distTotalSale[$valueKey] = ($distTotalSale[$valueKey] ?? 0) + round($value, 2);
    }

    private function updateMySale(&$mySale, $distributor, $quantity, $value, $totalUnits, $totalValues)
    {
        $mySale["{$distributor->name} Unit"] = round($quantity, 2) ?: 0;
        $mySale["{$distributor->name} Value"] = number_format(round($value, 2)) ?: 0;
        $mySale['total_units'] = round($totalUnits, 2) ?: 0;
        $mySale['total_values'] = number_format(round($totalValues, 2)) ?: 0;
    }

    private function updateTotalSales(&$distTotalSale, $totalUnits, $totalValues)
    {
        $distTotalSale['total_units'] = ($distTotalSale['total_units'] ?? 0) + round($totalUnits, 2);
        $distTotalSale['total_values'] = ($distTotalSale['total_values'] ?? 0) + round($totalValues, 2);
    }

    private function createTotalRow($distTotalSale, $distributors): Collection
    {
        $totalRow = collect([
            'id' => '',
            'line' => '',
            'pro_id' => '',
            'pro_code' => '',
            'name' => 'Total',
            'total_units' => $distTotalSale['total_units'],
            'total_values' => number_format($distTotalSale['total_values']),
        ]);

        foreach ($distributors as $distributor) {
            $totalRow["{$distributor->name} Unit"] = $distTotalSale["{$distributor->name} Unit"];
            $totalRow["{$distributor->name} Value"] = number_format($distTotalSale["{$distributor->name} Value"]);
        }

        return $totalRow;
    }
}
