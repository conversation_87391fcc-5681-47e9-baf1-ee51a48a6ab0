<?php

namespace App\Http\Controllers;

use App\CallRate;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Line;
use App\LineDivision;
use App\Models\Kpi;
use App\Models\OtherSetting;
use App\Position;
use App\Services\ActualService;
use App\Services\Kpis\AccountCoverageService;
use App\Services\Kpis\AreaManagement;
use App\Services\Kpis\CallRateService;
use App\Services\Kpis\DoctorCoverageService;
use App\Services\Kpis\FieldDays;
use App\Services\Kpis\FrequencyKpiService;
use App\Services\Kpis\PlanAchievementService;
use App\Services\Kpis\TeamAccountCoverageService;
use App\Services\Kpis\TeamDoctorCoverageService;
use App\Setting;
use App\User;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class KpisReportController extends ApiController
{
    public function getUsersUsingPositions(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = Line::whereIntegerInRaw('id', $request->lines)->get();
        $users = $user->belowUsersOfAllLinesWithPositions($lines);
        if ($request->position) {
            $users = $users->where('menuroles', $request->position['name']);
        }
        return response()->json([
            'users' => $users->unique()->values(),
        ]);
    }
    public function filter(Request $request)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $kpis = $request->kpisFilter;
        $from = Carbon::parse($kpis['fromDate'])->startOfDay();
        $to = Carbon::parse($kpis['toDate'])->endOfDay();
        $month = [Carbon::parse($from)->format('m'), Carbon::parse($to)->format('m')];
        $year = Carbon::parse($from)->format('Y');
        $period = CarbonPeriod::create($from, $to);
        $lines = Line::when(!empty($kpis['lines']), fn($q) => $q->whereIntegerInRaw("lines.id", $kpis['lines']))->get();
        $lineIds = $lines->pluck('id')->toArray();

        $division_type = DivisionType::where('last_level', '=', 1)->first()->id;
        $freqType = OtherSetting::where('key', 'type_of_frequency')->value('value');
        $fields = collect(['employee']);
        $fieldResults = collect([]);

        $filtered = new Collection([]);
        $data = new Collection([]);
        // foreach ($lines as $line) {
        //     $users = $line->users($from, $to)
        //         ->when(!empty($kpis['users']), fn ($q) => $q->whereIntegerInRaw("line_users.user_id", $kpis['users']))->get();
        //     $filtered = $filtered->merge($authUser->filterUsers($line, $users, $kpis));
        // }
        $filtered = User::whereIntegerInRaw("id", $kpis['users'])->get();
        $KpisNames = Kpi::select(
            'kpis.id',
            'kpis.short_name',
            'kpis.name',
            'kpi_ratios.ratio',
            'kpi_ratios.line_id',
            'kpi_ratios.roleable_id',
            'kpi_ratios.roleable_type',
            'kpi_ratios.minimum'
        )
            ->leftJoin('kpi_ratios', 'kpis.id', 'kpi_ratios.kpi_id')
            ->where('roleable_id', $filtered->first()->divisions()?->first()?->division_type_id)
            ->orderBy('sort', 'ASC')
            ->whereIntegerInRaw('kpi_ratios.line_id', $lineIds)->get()->unique();
        foreach ($KpisNames as $KpiName) {
            $fields = $fields->push($KpiName->short_name . ' ' . round($KpiName->ratio, 0) . '%' . ' (min)');
            $fields = $fields->push($KpiName->short_name . ' ' . ' (act)');
            $fields = $fields->push($KpiName->short_name . ' ' . ' (res)');
            $fieldResults = $fieldResults->push($KpiName->short_name . ' ' . ' (res)');
        }
        $fields = $fields->merge(['tot']);
        $days = Setting::where('key', 'fixed_working_days')->value('value');
        $period = CarbonPeriod::create($from, '1 month', $to);
        foreach ($period as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $year,
        ];
        $filtered->each(function ($user) use ($lineIds, $from, $to, $month, $year,  &$data, $division_type, $freqType, $days) {
            $data = $data->push($this->statistics($lineIds, $user, $from, $to, $month, $year,  $division_type, $freqType, $days));
        });
        return response()->json([
            'data' => $data,
            'fields' => $fields,
            'dates' => $dates,
            'fieldResults' => $fieldResults,
        ]);
    }

    private function statistics($lineIds, $user, $from, $to, $month, $year,  $division_type, $freqType, $days)
    {
        $type = $user->hasPosition() ? $user->positionDivision()->first()?->DivisionType : $user->divisions($from, $to)?->first()?->DivisionType;
        $lines = $user->lines($from, $to)->pluck('lines.id')->toArray();
        $direct = $user->divisions($from, $to)->whereIn('line_divisions.line_id', $lines);
        $isManager = $direct->where('division_type_id', '<>', $division_type)->exists();
        $directDivisions = $user->divisions($from, $to)->whereIn('line_divisions.line_id', $lines)->where('division_type_id', $division_type)->get()->pluck('id')->toArray();
        $filteredDivisions = collect([]);
        $divisions = $user->allBelowDivisions(from: $from, to: $to)->whereIn('line_id', $lines)->where('is_kol', 0)
            ->where('division_type_id', $division_type)->unique('id')->pluck('id')->toArray();
        $belowUsers = collect([]);
        collect($divisions)->each(function ($division) use ($belowUsers, $lines, $from, $to, $filteredDivisions) {
            $div = LineDivision::find($division);
            $divWithUser = $div->users($from, $to)->whereIntegerInRaw('line_id', $lines)->get();
            if (count($divWithUser) != 0) {
                $belowUsers = $belowUsers->push($divWithUser);
                $filteredDivisions = $filteredDivisions->push($div);
            }
        });
        $filteredDivisions = $filteredDivisions->pluck('id')->unique()->values()->toArray();
        $belowUsers = $belowUsers->collapse()->pluck('id')->unique()->values()->toArray();
        // $actuals = (new ActualService)->getActuals($user, 'users.id', $from, $to, [], $lines);
        $data = collect([
            'id' => $user->id,
            'employee' => $user->fullname,
            'title' => $user->menuroles,
            'color' => $type?->color,
        ]);
        $KpisSetting = collect([]);
        if ($user->hasPosition()) {
            $positionId = $user->position()?->id;
            $KpisSetting = Kpi::select(
                'kpis.id',
                'kpis.name',
                'kpis.short_name',
                'kpi_ratios.ratio',
                'kpi_ratios.line_id',
                'kpi_ratios.roleable_id',
                'kpi_ratios.roleable_type',
                'kpi_ratios.minimum'
            )
                ->leftJoin('kpi_ratios', 'kpis.id', 'kpi_ratios.kpi_id')
                ->whereIntegerInRaw('kpi_ratios.line_id', $lineIds)
                ->where('roleable_id', $positionId)
                ->orderBy('sort', 'ASC')
                ->where('roleable_type', Position::class)
                ->get();
        } else {
            $KpisSetting = Kpi::select(
                'kpis.id',
                'kpis.name',
                'kpis.short_name',
                'kpi_ratios.ratio',
                'kpi_ratios.line_id',
                'kpi_ratios.roleable_id',
                'kpi_ratios.roleable_type',
                'kpi_ratios.minimum'
            )
                ->leftJoin('kpi_ratios', 'kpis.id', 'kpi_ratios.kpi_id')
                ->whereIntegerInRaw('kpi_ratios.line_id', $lineIds)
                ->where('roleable_id', $type?->id)
                ->orderBy('sort', 'ASC')
                ->where('roleable_type', DivisionType::class)
                ->get();
        }
        $tot = 0;
        $KpisSetting->each(function ($kpi)
        use ($isManager, $directDivisions, $filteredDivisions, &$data, $divisions, $user, $from, $to, $month, $year, $freqType, $lines, &$tot, $belowUsers, $division_type, $days) {
            $result = 0;
            $average = 0;
            if ($kpi->name == 'Coverage') {
                $result = (new DoctorCoverageService)->getCoveredDoctors($user, 'users.id', $from, $to, [], $lines, $isManager ? $directDivisions : $divisions);
            }
            if ($kpi->name == 'TEAM Coverage') {
                $result = (new TeamDoctorCoverageService)->getTeamCoveredDoctors($from, $to, $belowUsers, 'users.id', [], $lines, $filteredDivisions);
            }
            if ($kpi->name == 'AM Coverage') {
                $result = (new AccountCoverageService)->getCoveredAccountsPerShift($user, 'users.id', $from, $to, [1], $lines, $isManager ? $directDivisions : $divisions);
            }
            if ($kpi->name == 'TEAM AM Coverage') {
                $result = (new TeamAccountCoverageService)->getTeamCoveredAccountsPerShift($from, $to, $belowUsers, 'users.id', [1], $lines, $filteredDivisions);
            }
            if ($kpi->name == 'PM Coverage') {
                $result = (new AccountCoverageService)->getCoveredAccountsPerShift($user, 'users.id', $from, $to, [2], $lines, $isManager ? $directDivisions : $divisions);
            }
            if ($kpi->name == 'TEAM PM Coverage') {
                $result = (new TeamAccountCoverageService)->getTeamCoveredAccountsPerShift($from, $to, $belowUsers, 'users.id', [2], $lines, $filteredDivisions);
            }
            if ($kpi->name == 'PH Coverage') {
                $result = (new AccountCoverageService)->getCoveredAccountsPerShift($user, 'users.id', $from, $to, [3], $lines, $isManager ? $directDivisions : $divisions);
            }
            if ($kpi->name == 'TEAM PH Coverage') {
                $result = (new TeamAccountCoverageService)->getTeamCoveredAccountsPerShift($from, $to, $belowUsers, 'users.id', [3], $lines, $filteredDivisions);
            }
            if ($kpi->name == 'Plan Achievement') {
                $result = (new PlanAchievementService)->getPlanAchievement($user, 'users.id', $from, $to, [1, 2, 3], $lines);
            }
            if ($kpi->name == 'Frequency') {
                $result = (new FrequencyKpiService)->getFrequencyKpis($user, 'users.id', $from, $to, $month, $year, $freqType, $lines, $isManager ? $directDivisions : $divisions);
            }
            if ($kpi->name == 'AM Frequency') {
                $result = (new FrequencyKpiService)->getShiftFrequencyKpis($user, 'users.id', $from, $to, $month, $year, $freqType, $lines, $isManager ? $directDivisions : $divisions, [], [], 1);
            }
            if ($kpi->name == 'PM Frequency') {
                $result = (new FrequencyKpiService)->getShiftFrequencyKpis($user, 'users.id', $from, $to, $month, $year, $freqType, $lines, $isManager ? $directDivisions : $divisions, [], [], 2);
            }
            if ($kpi->name == 'PH Frequency') {
                $result = (new FrequencyKpiService)->getShiftFrequencyKpis($user, 'users.id', $from, $to, $month, $year, $freqType, $lines, $isManager ? $directDivisions : $divisions, [], [], 3);
            }
            if ($kpi->name == 'TEAM Frequency') {
                $result = (new FrequencyKpiService)->getTeamFrequencyKpis($belowUsers, 'users.id', $from, $to, $month, $year, $freqType, $lines, $filteredDivisions, $division_type);
            }
            if ($kpi->name == 'Call Rate') {
                $result = (new CallRateService)->getCallRates($user, 'users.id', $from, $to, $month, $year, [], $lines, $isManager ? $directDivisions : $divisions, [], [], $days);
            }
            // if ($kpi->name == 'TEAM Call Rate') {
            //     $result = (new CallRateService)->getTeamCallRates($user,$belowUsers, 'users.id', $from, $to, $month, $year, [], $lines, $divisions);
            // }
            if ($kpi->name == 'AM Call Rate') {
                $result = (new CallRateService)->getCallRates($user, 'users.id', $from, $to, $month, $year, [1], $lines, $isManager ? $directDivisions : $divisions, [], [], $days);
            }
            if ($kpi->name == 'TEAM AM Call Rate') {
                $result = (new CallRateService)->getTeamCallRates($user, $belowUsers, 'users.id', $from, $to, $month, $year, [1], $lines, $divisions, $division_type, [], [], $days);
            }
            if ($kpi->name == 'PM Call Rate') {
                $result = (new CallRateService)->getCallRates($user, 'users.id', $from, $to, $month, $year, [2], $lines, $isManager ? $directDivisions : $divisions, [], [], $days);
            }
            if ($kpi->name == 'TEAM PM Call Rate') {
                $result = (new CallRateService)->getTeamCallRates($user, $belowUsers, 'users.id', $from, $to, $month, $year, [2], $lines, $divisions, $division_type, [], [], $days);
            }
            if ($kpi->name == 'PH Call Rate') {
                $result = (new CallRateService)->getCallRates($user, 'users.id', $from, $to, $month, $year, [3], $lines, $isManager ? $directDivisions : $divisions, [], [], $days);
            }
            if ($kpi->name == 'TEAM PH Call Rate') {
                $result = (new CallRateService)->getTeamCallRates($user, $belowUsers, 'users.id', $from, $to, $month, $year, [3], $lines, $divisions, $division_type, [], [], $days);
            }
            if ($kpi->name == 'Field Days') {
                $result = (new FieldDays)->getFieldDays($kpi, $user, 'users.id', $from, $to, [], $lines, $divisions);
            }
            if ($kpi->name == 'Team Field Days') {
                $managerDivisions = $user->divisions;
                $directUsers = collect([]);
                $managerDivisions->each(function ($division) use ($user, $directUsers) {
                    $directUsers = $directUsers->push($user->getDirectBelowUsers($division));
                });
                $directUsers = $directUsers->collapse()->pluck('id')->unique()->toArray();
                $result = (new FieldDays)->getTeamFieldDays($directUsers, 'users.id', $from, $to, [], $lines, $divisions);
            }
            if ($kpi->name == 'Area Management') {
                $result = (new AreaManagement)->getAreaManagement($belowUsers, 'users.id', $from, $to, [], $lines);
            }
            if ($result >= $kpi->minimum) {
                $average = $result != 0 ? round(($result * $kpi->ratio) / 100, 2) : 0;
            }
            $tot += $average;
            $data = $data->put($kpi->short_name . ' ' . round($kpi->ratio, 0) . '%' . ' (min)', round($kpi->minimum, 0) . '%');
            $data = $data->put($kpi->short_name . ' ' . ' (act)', $result);
            $data = $data->put($kpi->short_name . ' ' . ' (res)', $average . '%');
        });
        $data = $data->put('tot', round($tot, 2) . '%');
        return $data;
    }
}
