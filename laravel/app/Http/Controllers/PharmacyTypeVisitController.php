<?php

namespace App\Http\Controllers;

use App\Models\PharmacyTypeVisit;
use Illuminate\Http\Request;

class PharmacyTypeVisitController extends ApiController
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return $this->respond(PharmacyTypeVisit::get());
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $max_sort = PharmacyTypeVisit::max('sort');
        if (!$max_sort) {
            $max_sort = 100;
        } else {
            $max_sort += 100;
        }
        PharmacyTypeVisit::create([
            'name' => $request->name,
            'sort' => $max_sort
        ]);
        return $this->respondCreated();
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        return $this->respond(PharmacyTypeVisit::find($id));
    }


    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        PharmacyTypeVisit::find($id)->update([
            'name' => $request->name,
            'sort' => $request->sort,
        ]);
        return $this->respondSuccess();
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        PharmacyTypeVisit::find($id)?->delete();
        return $this->respondSuccess();
    }
}
