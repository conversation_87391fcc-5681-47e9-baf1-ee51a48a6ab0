<?php

namespace App\Http\Controllers;

use App\AccountType;
use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Http\Controllers\Controller;
use App\Models\CommercialPharmacySetting;
use Illuminate\Http\Request;

class CommercialPharmacySettingController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $commercials = CommercialPharmacySetting::get()->map(function ($commercial) {
            return [
                'id' => $commercial->id,
                'type' => $commercial->name,
                'account_type' => $commercial->type ? $commercial->type->name : '',
            ];
        });
        $accountTypes = AccountType::get();
        LogActivity::addLog();
        return $this->respond(compact('commercials','accountTypes'));
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $commercial = CommercialPharmacySetting::create([
            'name' => $request->type,
            'acc_type_id' => $request->acc_type_id,
            'appearance' => $request->appearance,
            'show_units' => $request->showUnits,
        ]);
        $model_id = $commercial->id;
        $model_type = CommercialPharmacySetting::class;
        LogActivity::addLog($model_id, $model_type);
        return $this->respondCreated();
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $commercial = CommercialPharmacySetting::find($id);
        $model_id = $commercial->id;
        $model_type = CommercialPharmacySetting::class;
        LogActivity::addLog($model_id, $model_type);
        return $this->respond($commercial);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $commercial = CommercialPharmacySetting::find($id);
        $commercial->name = $request->type;
        $commercial->acc_type_id = $request->acc_type_id;
        $commercial->appearance = $request->appearance;
        $commercial->show_units = $request->showUnits;
        $commercial->save();
        $model_id = $commercial->id;
        $model_type = CommercialPharmacySetting::class;
        LogActivity::addLog($model_id, $model_type);
        return $this->respondSuccess();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        CommercialPharmacySetting::find($id)->delete();
        $model_id = $id;
        $model_type = CommercialPharmacySetting::class;
        LogActivity::addLog($model_id, $model_type);
        return $this->respondSuccess();
    }
}
