<?php

namespace App\Http\Controllers;

use App\Action;
use App\Classes;
use App\Form;
use App\Helpers\ExcelImporter;
use App\Helpers\LogActivity;
use App\Http\Controllers\Controller;
use App\Http\Requests\ImportRequest;
use App\Http\Requests\LineClassesRequest;
use App\Line;
use App\LineClasses;
use App\Permission;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use App\Imports\Updates\LineClassesImport as UpdatesLineClassesImport;

class LineClassesController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $classes = Classes::orderBy('sort', 'ASC')->select('classes.id', 'classes.name')->get();
        return response()->json(compact('classes'));
    }

    public function getLineClasses(Line $line)
    {
        $line_classes = DB::table('line_classes')
            ->select('line_classes.id', 'lines.name as line_name', 'classes.name as class_name', 'line_classes.from_date',  'line_classes.to_date')
            ->leftJoin('lines', 'line_classes.line_id', '=', 'lines.id')
            ->leftJoin('classes', 'line_classes.class_id', '=', 'classes.id')
            ->where('line_classes.deleted_at', '=', null)
            ->where('line_classes.line_id', '=', $line->id)
            ->get();

        
        
        
        
        
        
        LogActivity::addLog();

        return response()->json(['line_classes' => $line_classes]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(LineClassesRequest $request, Line $line)
    {
        $line_class_ids = $request->input('class_id');
        if (!$line->isAvailable($request->from_date, $request->to_date))
            throw new Exception('Line Class is Not in Range Date of Line');
        foreach ($line_class_ids as $class_id) {
            $line_classe = LineClasses::create([
                'line_id' => $request->line_id,
                'class_id' => $class_id,
                'from_date' => $request->from_date,
                'to_date' => $request->to_date,
            ]);
        }
        
        
        
        
        
        
        $model_id = $line_classe->id;
        $model_type = LineClasses::class;

        LogActivity::addLog( $model_id, $model_type);

        return response()->json(['status' => 'success']);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $line_classe = DB::table('line_classes')
            ->select(
                'line_classes.id',
                'line_classes.class_id',
                'line_classes.line_id',
                'line_classes.from_date',
                'line_classes.to_date'
            )
            ->where('line_classes.id', '=', $id)
            ->first();
        
        
        
        
        
        
        $model_id = $id;
        $model_type = LineClasses::class;
        LogActivity::addLog( $model_id, $model_type);

        return response()->json(['line_classe' => $line_classe]);
    }


    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(LineClassesRequest $request,Line $line, $id)
    {
        $line_classe = LineClasses::find($id);
        if (!$line->isAvailable($request->from_date, $request->to_date))
            throw new Exception('Line Class is Not in Range Date of Line');
        $line_classe->class_id = $request->input('class_id');
        $line_classe->line_id = $request->input('line_id');
        $line_classe->from_date = $request->input('from_date');
        $line_classe->to_date = $request->input('to_date');
        $line_classe->save();

        
        
        
        
        
        
        $model_id = $id;
        $model_type = LineClasses::class;

        LogActivity::addLog( $model_id, $model_type);

        return response()->json(['status' => 'success']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $line_classe = LineClasses::find($id);
        if ($line_classe) {
            $line_classe->delete();
        }

        
        
        
        
        
        
        $model_id = $id;
        $model_type = LineClasses::class;

        LogActivity::addLog( $model_id, $model_type);

        return response()->json(['status' => 'success']);
    }
    public function importLineClasses(ImportRequest $request)
    {
        LineClasses::import($request);
        return $this->respondSuccess();
    }

    public function updateByImport(ImportRequest $request)
    {
        LineClasses::import(request: $request, update: true);

        return $this->respondSuccess();
    }

    public function export( $filename = '' )
    {
        // Check if file exists in app/storage/file folder
        $file_path = storage_path() . "/app/downloads/" . $filename;
        $headers = array(
            'Content-Type: xlsx',
            'Content-Disposition: attachment; filename='.$filename,
        );
        
        
        
        
        
        
        LogActivity::addLog();
        if ( file_exists( $file_path ) ) {
            // Send Download
            return Response::download( $file_path, $filename, $headers );
        } else {
            // Error
            return response()->json(['statusText'=>'failed'],422);
        }
    }
}
