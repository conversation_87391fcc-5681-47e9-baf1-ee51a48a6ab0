<?php

namespace App\Http\Controllers;

use App\Models\Coaching\CoachingDetail;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class CoachingDetailController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Coaching\CoachingDetail  $coachingDetail
     * @return \Illuminate\Http\Response
     */
    public function show(CoachingDetail $coachingDetail)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Coaching\CoachingDetail  $coachingDetail
     * @return \Illuminate\Http\Response
     */
    public function edit(CoachingDetail $coachingDetail)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Coaching\CoachingDetail  $coachingDetail
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, CoachingDetail $coachingDetail)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Coaching\CoachingDetail  $coachingDetail
     * @return \Illuminate\Http\Response
     */
    public function destroy(CoachingDetail $coachingDetail)
    {
        //
    }
}
