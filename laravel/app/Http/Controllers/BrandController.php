<?php

namespace App\Http\Controllers;


use App\Form;
use App\Action;
use App\Brand;
use App\Helpers\ExcelImporter;
use App\Http\Requests\BrandRequest;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Excel as ExcelType;
use App\Helpers\LogActivity;
use App\Http\Requests\ImportRequest;
use App\Http\Requests\MailRequest;
use App\Imports\BrandsImport;
use Spatie\Permission\Models\Permission;
use App\Imports\Updates\BrandsImport as UpdatesBrandsImport;

class BrandController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $brands = DB::table('brands')
            ->select('brands.id', 'brands.name', 'brands.notes', 'brands.sort')
            ->whereNull('deleted_at')
            ->orderBy('brands.sort', 'asc')
            ->get();
        LogActivity::addLog();
        return response()->json(compact('brands'));
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\BrandRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(BrandRequest $request)
    {
        $max_sort = Brand::withTrashed()->max('sort');
        if (!$max_sort) {
            $max_sort = 100;
        } else {
            $max_sort += 100;
        }
        $brand = new Brand();
        $brand->name = $request->input('name');
        $brand->notes = $request->input('notes');
        $brand->sort = $max_sort;
        $brand->save();
        $model_id = $brand->id;
        $model_type = Brand::class;
        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Brand  $brand
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $brand = DB::table('brands')
            ->select('brands.id', 'brands.name', 'brands.notes', 'brands.sort')
            ->where('brands.id', '=', $id)
            ->first();
        $model_id = $id;
        $model_type = Brand::class;
        LogActivity::addLog($model_id, $model_type);
        return response()->json($brand);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Brand  $brand
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $brand = DB::table('brands')
            ->select('brands.id', 'brands.name', 'brands.notes', 'brands.sort')
            ->where('brands.id', '=', $id)
            ->first();
        $model_id = $id;
        $model_type = Brand::class;
        LogActivity::addLog($model_id, $model_type);
        return response()->json($brand);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Brand  $brand
     * @return \Illuminate\Http\Response
     */
    public function update(BrandRequest $request, $id)
    {
        $brand = Brand::find($id);
        $brand->name       = $request->input('name');
        $brand->notes      = $request->input('notes');
        $brand->sort       = $request->input('sort');
        $brand->save();
        $model_id = $id;
        $model_type = Brand::class;

        LogActivity::addLog($model_id, $model_type);

        return response()->json(['status' => 'success']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Brand  $brand
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $model_id = $id;
        $brand = Brand::find($id);
        $productBrand = DB::table("product_brands")->where("brand_id", $id)->count();
        if ($brand) {
            if ($productBrand > 0) {
                return response()->json(['statusText' => 'failed'], 422);
            }
            $brand->delete();
        }
        $model_type = Brand::class;

        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    public function import(ImportRequest $request)
    {
        Brand::import($request);
        return $this->respondSuccess();
    }

    public function updateByImport(ImportRequest $request)
    {
        Brand::import(request: $request, update: true);
        return $this->respondSuccess();
    }

    public function exportbrands()
    {
        return Brand::export(ExcelType::XLSX);
    }

    public function exportcsv()
    {
        return Brand::export(ExcelType::CSV);
    }

    public function exportpdf()
    {
        $brands = Brand::where('deleted_at', null)->get();
        return Brand::exportPdf($brands);
    }

    public function sendmail(MailRequest $request)
    {
        $brands = Brand::where('deleted_at', null)->get();
        return Brand::sendmail($request, $brands);
    }
}
