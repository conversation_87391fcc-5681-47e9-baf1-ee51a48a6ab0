<?php

namespace App\Http\Controllers;

use App\Http\Requests\IncentiveMappingRequest;
use App\Models\IncentiveMapping;

class IncentiveMappingController extends ApiController
{
    public function index()
    {
        $data = IncentiveMapping::select([
            'incentive_mappings.id',
            'roles.name as title',
            'roles.id as role_id',
            'incentive_mappings.achievement_rule as achievement',
            'incentive_mappings.year',
            'incentive_mappings.quarter_value as quarterly',
            'incentive_mappings.annual_value as annual',
            'incentive_mappings.total_value as totalIncome'
        ])
            ->leftJoin('roles', 'roles.id', '=', 'incentive_mappings.role_id')
            ->get();

        return $this->respond($data, 200);
    }

    public function store(IncentiveMappingRequest $request)
    {
        IncentiveMapping::create([
            'role_id' => $request->role_id,
            'achievement_rule' => $request->achievement,
            'year' => $request->year,
            'quarter_value' => $request->quarterly,
            'annual_value' => $request->annual,
            'total_value' => $request->totalIncome,
        ]);

        return $this->respondCreated();
    }

    public function update(IncentiveMappingRequest $request, IncentiveMapping $incentiveMapping)
    {
        $incentiveMapping->update([
            'role_id' => $request->role_id,
            'achievement_rule' => $request->achievement,
            'year' => $request->year,
            'quarter_value' => $request->quarterly,
            'annual_value' => $request->annual,
            'total_value' => $request->totalIncome,
        ]);

        return $this->respondSuccess();
    }

    public function destroy(IncentiveMapping $incentiveMapping)
    {
        $incentiveMapping->delete();

        $this->respondSuccess();
    }
}
