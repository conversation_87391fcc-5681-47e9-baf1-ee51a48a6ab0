<?php

namespace App\Http\Controllers;

use App\Action;
use App\Form;
use App\Helpers\LogActivity;
use App\Line;
use App\LineBricks;
use App\LineDivision;
use App\Permission;

class BrickAccountController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Line $line, LineDivision $division)
    {
        $LineBrick = LineBricks::where('line_id', $line->id)
            ->where('line_division_id', $division->id)
            ->where(function ($q) {
                $q->where('to_date', '=', null)
                    ->OrWhere('to_date', '>', now()->toDateString());
            })->orderBy('created_at', 'desc')->first();

        $bricks = $LineBrick->line_division->bricks()->with('accounts')->get();
        
        
        
        
        
        
        LogActivity::addLog();
        return $this->respond($bricks);
    }
}
