<?php

namespace App\Http\Controllers;

use App\Models\Trace;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;

class TraceController extends Controller
{
    public function index(Request $request)
    {
        $userIds = $request->users;
        $active = User::where('status','active')->select('id')->pluck('id')->toArray();
        $inactive = User::where('status','<>','active')->select('id')->pluck('id')->toArray();
        $traces = User::select(["id", "name"])
            ->with([
                "latestTrace" => function ($q) {
                    $q->select(["lat", "lng", "user_id", "created_at"]);
                }
            ])
            ->whereIn('id', $active)
            ->get()
            ->map(fn(User $user) => [
                "id" => $user->id,
                "name" => $user->name,
                "lat" => $user->latestTrace?->lat,
                "lng" => $user->latestTrace?->lng,
                "last_online" => $user->latestTrace?->created_at->lte(now()->addMinute()) ? 'now' : $user->latestTrace?->created_at
                    ->diffForHumans([
                        "parts" => 1
                    ])
            ]);

        $traces = collect($traces)->merge(User::select(["id", "name"])
            ->with([
                "latestTrace" => function ($q) {
                    $q->select(["lat", "lng", "user_id", "created_at"]);
                }
            ])
            ->whereIn('id', $inactive)
            ->get()
            ->map(fn(User $user) => [
                "id" => $user->id,
                "name" => $user->name,
                "lat" => $user->latestTrace?->lat,
                "lng" => $user->latestTrace?->lng,
                "last_online" => 'not'
            ]));


        return response()->json($traces);
    }
}
