<?php

namespace App\Http\Controllers;

use App\Line;
use App\Models\CommercialRequest\CommercialRequest;
use App\Models\CommercialRequest\PartialPayment;
use App\Models\CommercialRequest\ServiceComplete;
use App\User;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class FinanceSummaryReportConroller extends ApiController
{
    public function getCommercials($table, $objects, $commercial, $from, $to, $startDueDate, $endDueDate)
    {
        $commercials = DB::table('commercial_requests')->select(
            'commercial_requests.id as id',
            DB::raw('DATE_FORMAT(crm_commercial_requests.created_at, "%Y-%m-%d") as insertion'),
            'users.fullname as employee',
            'users.id as user_id',
            DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
            DB::raw('IFNULL(group_concat(distinct crm_division_types.color),"") as color'),
            DB::raw('IFNULL(group_concat(distinct crm_commercial_requests.description),"") as description'),
            DB::raw('IFNULL(crm_commercial_requests.amount,"") as total'),
            // DB::raw('IFNULL(crm_partial_payments.finance_desc,"") as partial_comment'),
            // DB::raw("
            //     CASE
            //         WHEN crm_partial_payments.finance_view = 1 THEN 'Yes'
            //         WHEN crm_partial_payments.finance_view = 0 THEN 'No'
            //         ELSE 'No'
            //     END as recieved_partial
            // "),
            DB::raw("
                CASE
                    WHEN crm_partial_payments.finance_paid = 1 THEN 'Yes'
                    WHEN crm_partial_payments.finance_paid = 0 THEN 'No'
                    ELSE 'Partial'
                END as partial_paid
            "),
            DB::raw('IFNULL(crm_partial_payments.partial_amount,"") as partial_amount'),
            // DB::raw('IFNULL(crm_partial_payments.date,"") as partial_date'),
            // DB::raw('IFNULL(crm_partial_attachments.path,"") as partial_attach'),
            // DB::raw("
            //     CASE
            //         WHEN crm_service_completes.finance_view = 1 THEN 'Yes'
            //         WHEN crm_service_completes.finance_view = 0 THEN 'No'
            //         ELSE 'No'
            //     END as recieved_end_service
            // "),
            DB::raw("
                CASE
                    WHEN crm_service_completes.end_service = 1 THEN 'Yes'
                    WHEN crm_service_completes.end_service = 0 THEN 'No'
                    ELSE 'No'
                END as end_service
            "),
            // DB::raw('IFNULL(crm_service_completes.finance_desc,"") as end_service_comment'),
            DB::raw('IFNULL(crm_service_completes.invoice_amount,"") as end_service_amount'),
            DB::raw('IFNULL(crm_service_completes.date,"") as end_service_date'),
            DB::raw('IFNULL(crm_service_done_attachments.path,"") as end_service_attach'),
            DB::raw("
                CASE
                    WHEN crm_end_commercial_payments.paid = 1 THEN 1
                    WHEN crm_end_commercial_payments.paid = 0 THEN 0
                    ELSE 0
                END as paid
            "),
            DB::raw('IFNULL(DATE_FORMAT(crm_service_completes.due_date, "%Y-%m-%d"),"") as due_date'),
        )
            ->leftJoin('users', 'commercial_requests.user_id', 'users.id')
            ->leftJoin('service_completes', 'commercial_requests.id', 'service_completes.request_id')
            ->leftJoin('end_commercial_payments', 'commercial_requests.id', 'end_commercial_payments.request_id')
            ->leftJoin('commercial_lines', 'commercial_requests.id', 'commercial_lines.request_id')
            ->leftJoin('lines', 'commercial_lines.line_id', 'lines.id')
            ->leftJoin('commercial_divisions', 'commercial_requests.id', 'commercial_divisions.request_id')
            ->leftJoin('line_divisions', 'commercial_divisions.div_id', 'line_divisions.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin(
                'partial_payments',
                function ($join) {
                    $join->on('commercial_requests.id', '=', 'partial_payments.request_id');
                }
            )
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('commercial_requests.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', CommercialRequest::class);
                }
            )
            ->leftJoin(
                'attachments as partial_attachments',
                function ($join) {
                    $join->on('partial_payments.id', '=', 'partial_attachments.attachable_id');
                    $join->where('partial_attachments.attachable_type', PartialPayment::class);
                }
            )
            ->leftJoin(
                'attachments as service_done_attachments',
                function ($join) {
                    $join->on('service_completes.id', '=', 'service_done_attachments.attachable_id');
                    $join->where('service_done_attachments.attachable_type', ServiceComplete::class);
                }
            )
            ->orderBy('id', 'DESC')
            ->whereNull('commercial_requests.deleted_at')
            ->whereNull('partial_attachments.deleted_at')
            ->whereNull('service_done_attachments.deleted_at')
            ->whereIntegerInRaw($table, $objects)
            ->whereBetween('commercial_requests.created_at', [$from, $to]);
        if (!empty($commercial['types'])) {
            $commercials = $commercials->whereIntegerInRaw('commercial_requests.request_type_id', $commercial['types']);
        }
        if (!empty($commercial['lines'])) {
            $commercials = $commercials->whereIntegerInRaw('commercial_lines.line_id', $commercial['lines']);
        }
        if (!empty($commercial['due_date'])) {

            $commercials = $commercials->whereBetween('service_completes.due_date', [$startDueDate, $endDueDate]);
        }

        $commercials = match ($commercial['approval']) {
            1 => $commercials->whereNull('plan_visit_details.approval'),
            2 => $commercials->where('plan_visit_details.approval', 1),
            3 => $commercials->where('plan_visit_details.approval', 0),
            4 => $commercials->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null)),
            5 => $commercials,
        };

        $commercials = match ($commercial['partial_payment']) {
            1 => $commercials->where('partial_payments.finance_paid', 0),
            2 => $commercials->where('partial_payments.finance_paid', 1),
            3 => $commercials
        };
        $commercials = match ($commercial['serviceDone']) {
            1 => $commercials->where('service_completes.end_service', 0),
            2 => $commercials->where('service_completes.end_service', 1),
            3 => $commercials
        };
        $commercials = match ($commercial['paid']) {
            1 => $commercials->whereNull('end_commercial_payments.paid'),
            2 => $commercials->where('end_commercial_payments.paid', 1),
            3 => $commercials
        };
        $commercials = $commercials->groupBy("id", "partial_payments.id", "partial_attachments.id", "service_completes.id", "service_done_attachments.id", "end_commercial_payments.paid")->get();

        return $commercials;
    }

    public function filter(Request $request)
    {
        /**@var User */
        $authUser = Auth::user();
        $commercial = $request->commercialFilter;
        $from = Carbon::parse($commercial['fromDate'])->startOfDay();
        $to = Carbon::parse($commercial['toDate'])->endOfDay();
        $startDueDate = Carbon::parse($commercial['due_date'])->startOfMonth();
        $endDueDate = Carbon::parse($commercial['due_date'])->endOfMonth();
        $lines = Line::select('id', 'name')->when(!empty($commercial['lines']), fn($q) => $q->whereIntegerInRaw("lines.id", $commercial['lines']))->get();
        $filtered = new Collection([]);
        $data = new Collection([]);
        $table = $commercial['filter'] == 1 ? 'line_divisions.id' : 'users.id';
        foreach ($lines as $line) {
            if ($commercial['filter'] == 1) {
                $divisions = $line->divisions($from, $to)
                    ->when(!empty($commercial['divisions']), fn($q) => $q->whereIn("line_divisions.id", $commercial['divisions']))->get();
                $filtered = $filtered->merge($authUser->filterDivisions($line, $divisions, $commercial));
            }
            if ($commercial['filter'] == 2) {
                $users = $line->users($from, $to)
                    ->when(!empty($commercial['users']), fn($q) => $q->whereIn("line_users.user_id", $commercial['users']))->get();
                $filtered = $filtered->merge($authUser->filterUsers($line, $users, $commercial));
            }
            if ($commercial['filter'] == 2) {
                $users = $line->users($from, $to)
                    ->when(!empty($commercial['users']), fn($q) => $q->whereIn("line_users.user_id", $commercial['users']))->get();
                $filtered = $filtered->merge($authUser->filterUsers($line, $users, $commercial));
            }
        }
        if ($commercial['filter'] == 3) {
            $filtered = User::whereIntegerInRaw('id', $commercial['users'])->get();
        }
        $objects = $filtered->unique('id')->pluck('id');
        $data = $this->getCommercials($table, $objects, $commercial, $from, $to, $startDueDate, $endDueDate);

        $fields = [
            "id",
            "line",
            "employee",
            "insertion",
            "description",
            "partial_paid",
            "partial_amount",
            "end_service",
            "total",
            "end_service_date",
            "end_service_attach",
            "due_date",
            "paid"
        ];
        $months = [];
        $period = CarbonPeriod::create($from, '1 month', $to);
        foreach ($period as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $from->format('Y'),
        ];
        return response()->json([
            'data' => $data,
            'fields' => $fields,
            'dates' => $dates,
            'clickable_fields' => ['id'],
        ]);
    }
}
