<?php

namespace App\Http\Controllers;

use App\LineUser;
use App\Position;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class UnLinkedUserPositionController extends ApiController
{
    public function getUnlinkedPositions()
    {

        $positions = Position::whereHas('users', function (Builder $query) {
            $query->where('to_date', '<>', null)
                ->where('to_date', '<=', Carbon::now());
        })->orDoesntHave('users')->get()->unique('id');

        return $this->respond($positions);
    }
    public function getUnLinkedUsers()
    {
        $user_line_position = LineUser::with('user:id,fullname')->whereHas('user.positions', function (Builder $query) {
            $query->where('to_date', '<>', null)
                ->where('to_date', '<=', Carbon::now());
        })->orDoesntHave('user.positions')->get()->pluck('user');
        return $this->respond($user_line_position->filter(fn ($user) => $user != null)->unique('id')->values());
    }
}
