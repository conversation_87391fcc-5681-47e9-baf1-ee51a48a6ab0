<?php

namespace App\Http\Controllers;

use App\Helpers\LogActivity;
use App\Http\Controllers\Controller;
use App\Models\Expenses\KilometersAverage;
use Illuminate\Http\Request;

class KilometersAverageController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return $this->respond(KilometersAverage::get());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        KilometersAverage::create([
            'kilo_meter' => $request->kilo_meter,
            'calculation' => $request->calculation,
        ]);
        // }
        return $this->respondCreated();
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        return $this->respond(KilometersAverage::find($id));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $price = KilometersAverage::find($id);
        $price->kilo_meter = $request->kilo_meter;
        $price->calculation = $request->calculation;
        $price->save();
        return $this->respondSuccess();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        KilometersAverage::find($id)->delete();
        return $this->respondSuccess();
    }
}
