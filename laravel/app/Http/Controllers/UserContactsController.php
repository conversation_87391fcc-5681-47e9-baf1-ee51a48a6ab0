<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Line;
use App\LineDivision;
use App\Role;
use App\User;
use App\UserPosition;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Mpdf\Tag\P;

class UserContactsController extends ApiController
{
    // contacts and direct below users
    public function getContacts()
    {
        $user = auth()->user();
        /**@var User $user */
        if ($user->hasRole('admin') || $user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) {
            return $this->respond(
                User::with([
                    'traces' => fn($q) => $q->whereYear('created_at', now()->year)
                        ->whereMonth('created_at', now()->month)
                        ->whereDay('created_at', now()->day)
                        ->orderBy("id","desc")
                        ->limit(100),
                ])
                    ->where('status', 'active')
                    ->where("id", "<>", auth()->id())->get()
            );
        }

        $belowUsers = new Collection();
        if ($user->hasPosition()) {
            $userIds = $user->indexPerUser($user);
            $belowUsers = User::with([
                'traces' => fn($q) => $q->whereYear('created_at', now()->year)
                    ->whereMonth('created_at', now()->month)
                    ->whereDay('created_at', now()->day)
                    ->orderBy("id","desc")
                    ->limit(100)
            ])->whereIn('id', $userIds)->get();
            // throw new CrmException($belowUsers);
        } else {
            $user->lines->map(function ($line) use ($user) {
                return $user->division($line);
            })->each(function ($division) use ($belowUsers) {
                if ($division) {
                    $division->getBelowDivisions()->each(function (LineDivision $division) use ($belowUsers) {
                        $user = $division->user()?->load([
                            'traces' => fn($q) => $q->whereYear('created_at', now()->year)
                                ->whereMonth('created_at', now()->month)
                                ->whereDay('created_at', now()->day)
                                ->orderBy("id","desc")
                                ->limit(100)
                        ]);
                        if (!$belowUsers->contains("id", $user?->id)) {
                            $belowUsers->push($user);
                        }
                    });
                }
            });
        }

        return $this->respond($belowUsers->filter(fn($user)=> $user != null)->values());
    }

    // contacts and all below users
    public function getAllContacts(Line $line, ?string $id = null)
    {
        $user = User::find($id) ?? auth()->user();


        if ($user->hasRole('admin') || $user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) {
            return $this->respond(User::where('status', 'active')->get());
        }

        return $this->respond($user->getBelowUsers($user->division($line)));
    }

    public function getContactAdmins()
    {
        $adminRole = Role::whereIn("name", ["admin", "sub admin"])->first();

        return $this->respond($adminRole->users);
    }
}
