<?php

namespace App\Http\Controllers\Coaching;

use App\Exceptions\CrmException;
use App\Helpers\ExcelImporter;
use App\Http\Controllers\ApiController;
use App\Http\Requests\Coaching\AnswerRequest;
use App\Http\Requests\Coaching\QuestionRequest;
use App\Http\Resources\Coaching\QuestionResource;
use App\Models\Coaching\Question;
use App\Models\Coaching\Answer;

use App\Http\Requests\ImportRequest;
use App\Http\Requests\MailRequest;
use App\Models\Coaching\CategoryQuestion;
use Maatwebsite\Excel\Excel as ExcelType;
use App\Imports\Updates\Coaching\QuestionsImport as UpdatesQuestionsImport;
use App\Models\Coaching\CoachingDetail;
use Illuminate\Support\Facades\DB;

class QuestionController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $questions = Question::with(['answers', 'categories'])->orderBy('sort', 'ASC');

        return response()->json([
            'questions' => QuestionResource::collection($questions->paginate(50)),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Requests\Coaching\QuestionRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(QuestionRequest $request)
    {
        // throw new CrmException($request->all());
        DB::transaction(function () use ($request) {
            $question = new Question();
            $question->name = $request->name;
            $question->notes = $request->notes;
            $question->sort = $request->sort;
            $question->save();

            $question->categories()->sync($request->category_id);

            foreach ($request->answers as $answer) {

                Answer::create([
                    'question_id' => $question->id,
                    'name' => $answer['name'],
                    'notes' => $answer['notes'],
                    'weight' => $answer['weight'],
                ]);
            }
        });

        return $this->respondCreated();
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Coaching\Question  $question
     * @return \Illuminate\Http\Response
     */
    public function show(Question $question)
    {

        return (new QuestionResource($question->loadMissing(['answers', 'categories'])))->response();
    }

    /** 
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Requests\Coaching\QuestionRequest  $request
     * @param  \App\Models\Coaching\Question  $question
     * @return \Illuminate\Http\Response
     */
    public function update(QuestionRequest $request, Question $question)
    {

        $question->update([
            'name' => $request->name,
            'notes' => $request->notes,
            'sort' => $request->sort
        ]);
        foreach ($request->answers as $answer) {
            // throw new CrmException($answer['answer'], 1);
            Answer::findOrFail($answer['answer']['id'])->update($answer['answer']);
        }
        $question->categories()->sync($request->category_ids);

        return $this->respondSuccess();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Coaching\Question  $question
     * @return \Illuminate\Http\Response
     */
    public function destroy(Question $question)
    {

        DB::transaction(function () use ($question) {
            $details = CoachingDetail::where('question_id', $question->id)->get();
            foreach ($details as $detail) {
                $detail->delete();
            }
            $question->answers()->delete();
            $question->categories()->detach();

            $question->delete();
            return $this->respondSuccess();
        });
    }

    // import question
    public function import(ImportRequest $request)
    {
        Question::import($request);
        return $this->respondSuccess();
    }

    // bulk edit questions
    public function updateByImport(ImportRequest $request)
    {
        Question::import(request: $request, update: true);

        return $this->respondSuccess();
    }
    public function exportquestions()
    {
        return Question::export(ExcelType::XLSX);
    }

    public function exportcsv()
    {
        return Question::export(ExcelType::CSV);
    }

    public function exportpdf()
    {
        $questions = Question::where('deleted_at', null)->get();
        return Question::exportPdf($questions);
    }

    public function sendmail(MailRequest $request)
    {
        $questions = Question::where('deleted_at', null)->get();
        return Question::sendmail($request, $questions);
    }
}
