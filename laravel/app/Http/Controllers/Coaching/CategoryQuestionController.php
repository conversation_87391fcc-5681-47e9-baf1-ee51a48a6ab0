<?php

namespace App\Http\Controllers\Coaching;

use App\Http\Controllers\ApiController;
use App\Http\Requests\Coaching\CategoryQuestionRequest;
use App\Models\Coaching\CategoryQuestion;
use App\Models\Coaching\Category;
use App\Models\Coaching\Question;

use App\Http\Requests\ImportRequest;
use App\Http\Requests\MailRequest;
use Maatwebsite\Excel\Excel as ExcelType;

class CategoryQuestionController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $categoryquestions = CategoryQuestion::with(['category','question'])->get();

        return compact('categoryquestions');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Requests\Coaching\CategoryQuestionRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(CategoryQuestionRequest $request)
    {
        $data = $request->validated();
        $category = Category::findOrFail($data['category_id']);
        $question = Question::findOrFail($data['question_id']);

        $category->questions()->sync($question);

        return $this->respondCreated();
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Coaching\CategoryQuestion  $categoryQuestion
     * @return \Illuminate\Http\Response
     */
    public function show(CategoryQuestion $categoryQuestion)
    {
        return $categoryQuestion;
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Requests\Coaching\CategoryQuestionRequest  $request
     * @param  \App\Models\Coaching\CategoryQuestion  $categoryQuestion
     * @return \Illuminate\Http\Response
     */
    public function update(CategoryQuestionRequest $request, $id)
    {
        $categoryQuestion = CategoryQuestion::findOrFail($id);
        $categoryQuestion->category_id = $request->category_id;
        $categoryQuestion->question_id = $request->question_id;
        $categoryQuestion->save(); 
        return $this->respondSuccess();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Coaching\CategoryQuestion  $categoryQuestion
     * @return \Illuminate\Http\Response
     */
    public function destroy(CategoryQuestion $categoryQuestion)
    {
        $categoryQuestion->delete();
        return $this->respondSuccess();
    }

        // import type
        public function import(ImportRequest $request)
        {
            CategoryQuestion::import($request);
            return $this->respondSuccess();
        }
    
        public function exportcategoryquestions()
        {
            return CategoryQuestion::export(ExcelType::XLSX);
        }
    
        public function exportcsv()
        {
            return CategoryQuestion::export(ExcelType::CSV);
        }
    
        public function exportpdf()
        {
            $categoryquestions = CategoryQuestion::where('deleted_at',null)->get();
            return CategoryQuestion::exportPdf($categoryquestions);
        }
    
        public function sendmail(MailRequest $request)
        {
            $categoryquestions = CategoryQuestion::where('deleted_at',null)->get();
            return CategoryQuestion::sendmail($request,$categoryquestions);
        }
    
}
