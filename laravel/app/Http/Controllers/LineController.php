<?php

namespace App\Http\Controllers;

use App\Country;
use App\Currency;
use App\Line;
use App\LineDivision;
use App\LineDivisionUser;
use App\LineDivParent;
use App\Http\Requests\LineRequest;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Laravel\Octane\Facades\Octane;
use \Maatwebsite\Excel\Excel as ExcelType;
use Illuminate\Support\Carbon;
use App\Helpers\LogActivity;
use Spatie\Permission\Models\Permission;
use App\Form;
use App\Action;
use App\ActualVisitSetting;
use App\Exceptions\CrmException;
use App\Helpers\ExcelImporter;
use App\Http\Requests\ImportRequest;
use App\Http\Requests\MailRequest;
use App\Models\LineStructure;
use App\LineBricks;
use App\LineClasses;
use App\LineDivisionType;
use App\LineProduct;
use App\LineSpecialities;
use App\LineUser;
use App\User;
use Illuminate\Support\Facades\Auth;

use App\Imports\Updates\LinesImport as UpdatesLinesImport;
use App\Product;
use App\Setting;
use DateTimeZone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class LineController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {


//        [$lines, $user] = Octane::concurrently([
//            fn () =>
        $lines = Cache::remember('all-lines', now()->addHour(), fn() => DB::table('lines')
            ->select([
                'lines.id',
                'lines.name',
                'lines.sort',
                'lines.from_date',
                DB::raw('IFNULL(crm_lines.to_date,"") as to_date'),
                'currencies.name as currency name',
                'countries.name as country name'
            ])
            ->leftJoin('currencies', 'lines.currency_id', '=', 'currencies.id')
            ->leftJoin('countries', 'lines.country_id', '=', 'countries.id')
            ->where('lines.deleted_at', '=', null)
            ->orderBy('lines.sort')
            ->get());
//            ,
//            fn () => User::find(1),
//        ]);

        $total = count($lines);
        LogActivity::addLog();
        return response()->json(compact('lines', 'total'));
    }

    public function getProductName($product, $level)
    {
        if ($level == 'Product')
            return $product->name;
        elseif ($level == 'Brief')
            return $product->short_name;
        else
            return count($product->brands) > 0 ? $product->brands->first()?->name : $product->name;
    }

    public function getLineProducts($line_id)
    {
        $product_brand_level = Setting::where('key', 'reports_level')->value('value');

        $products = Line::where('id', $line_id)->with('products:id,name')->get()->pluck('products')->collapse();
        $products = $products->map(function ($product) use ($product_brand_level) {
            return [
                'id' => $product->id,
                'name' => $this->getProductName($product, $product_brand_level),
            ];
        });
        return response()->json($products->unique('name')->filter(fn($product) => $product['name'] != null)->values());
    }

    public function setting()
    {
        $setting = Setting::where('key', 'product_with_line_or_division')->value('value');
        return response()->json(['setting' => $setting]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function create()
    {
        $currencies = Currency::orderBy('sort', 'ASC')->select(['currencies.id', 'currencies.name'])->get();
        $countries = Country::orderBy('sort', 'ASC')->select(['countries.id', 'countries.name'])->get();
        return response()->json(['status' => 'success', 'currencies' => $currencies, 'countries' => $countries]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(LineRequest $request)
    {

        $max_sort = Line::withTrashed()->max('sort');
        if (!$max_sort) {
            $max_sort = 100;
        } else {
            $max_sort += 100;
        }

        $line = Line::create([
            'name' => $request->name,
            'notes' => $request->notes,
            'sort' => $max_sort,
            'currency_id' => $request->currency_id,
            'country_id' => $request->country_id,
            'from_date' => $request->from_date,
            'to_date' => $request->to_date,
        ]);
        $model_id = $line->id;
        $model_type = Line::class;

        LogActivity::addLog($model_id, $model_type);
        $max_sort = Line::withTrashed()->max('sort');
        if (!$max_sort) {
            $max_sort = 100;
        } else {
            $max_sort += 100;
        }

        return response()->json(['status' => 'success', 'line' => $line]);
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Line $line
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $line = DB::table('lines')
            ->select('lines.id', 'lines.name', 'lines.notes', 'lines.from_date', 'lines.to_date', 'lines.sort', 'currencies.name as currency', 'countries.name as country')
            ->leftJoin('currencies', 'lines.currency_id', '=', 'currencies.id')
            ->leftJoin('countries', 'lines.country_id', '=', 'countries.id')
            ->where('lines.id', '=', $id)
            ->first();

        $division_types = DB::table('line_division_types')
            ->select(
                'line_division_types.id',
                'lines.name as line name',
                'division_types.name as division type name',
                'line_division_types.from_date',
                'line_division_types.to_date'
            )
            ->leftJoin('lines', 'line_division_types.line_id', '=', 'lines.id')
            ->leftJoin('division_types', 'line_division_types.divisiontype_id', '=', 'division_types.id')
            ->where('line_division_types.line_id', '=', $id)
            ->where('line_division_types.deleted_at', '=', null)
            ->get();

        $divisions = DB::table('line_divisions')
            ->select(
                'line_divisions.id',
                'lines.name as line name',
                'division_types.name as division type name',
                'line_divisions.name',
                'line_divisions.from_date',
                'line_divisions.to_date'
            )
            ->leftJoin('lines', 'line_divisions.line_id', '=', 'lines.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', '=', 'division_types.id')
            ->where('line_divisions.line_id', '=', $id)
            ->where('line_divisions.deleted_at', '=', null)
            ->get();

        $div_parents = DB::table('line_div_parents')
            ->select(
                'line_div_parents.id',
                'line_divisions.name as line_division_name',
                'line_div_parents.parent_id',
                'line_div_parents.from_date',
                'line_div_parents.to_date',
                'c.name as parent_name'
            )
            ->leftJoin('line_divisions', 'line_div_parents.line_div_id', '=', 'line_divisions.id')
            ->leftJoin('line_divisions AS c', function ($join) {
                $join->on('line_div_parents.parent_id', '=', 'c.id');
            })
            ->where('line_div_parents.deleted_at', '=', null)
            ->where('line_divisions.line_id', '=', $id)
            ->get();

        $products = DB::table('line_products')
            ->select(
                'line_products.id',
                'lines.name as line name',
                'products.name as product name',
                'line_products.from_date',
                'line_products.to_date'
            )
            ->leftJoin('lines', 'line_products.line_id', '=', 'lines.id')
            ->leftJoin('products', 'line_products.product_id', '=', 'products.id')
            ->where('line_products.line_id', '=', $id)
            ->where('line_products.deleted_at', '=', null)
            ->get();

        $users = DB::table('line_users')
            ->select(
                'line_users.id',
                'lines.name as line name',
                'users.name as user name',
                'line_users.from_date',
                'line_users.to_date'
            )
            ->leftJoin('lines', 'line_users.line_id', '=', 'lines.id')
            ->leftJoin('users', 'line_users.user_id', '=', 'users.id')
            ->where('line_users.line_id', '=', $id)
            ->where('line_users.deleted_at', '=', null)
            ->get();

        $division_users = DB::table('line_users_divisions')
            ->select(
                'line_users_divisions.id',
                'users.name as user_name',
                'line_divisions.name as line_division_name',
                'line_users_divisions.from_date',
                'line_users_divisions.to_date'
            )
            ->leftJoin('users', 'line_users_divisions.user_id', '=', 'users.id')
            ->leftJoin('line_divisions', 'line_users_divisions.line_division_id', '=', 'line_divisions.id')
            ->where('line_users_divisions.deleted_at', '=', null)
            ->where('line_divisions.line_id', '=', $id)
            ->get();

        $bricks = DB::table('line_bricks')
            ->select(
                'line_bricks.id',
                'lines.name as line_name',
                'line_divisions.name as line_division_name',
                'bricks.name as brick_name',
                'line_bricks.from_date',
                'line_bricks.to_date'
            )
            ->leftJoin('lines', 'line_bricks.line_id', '=', 'lines.id')
            ->leftJoin('bricks', 'line_bricks.brick_id', '=', 'bricks.id')
            ->leftJoin('line_divisions', 'line_bricks.line_division_id', '=', 'line_divisions.id')
            ->where('line_bricks.line_id', '=', $id)
            ->where('line_bricks.deleted_at', '=', null)
            ->get();

        $classes = DB::table('line_classes')
            ->select(
                'line_classes.id',
                'lines.name as line_name',
                'classes.name as class_name',
                'line_classes.from_date',
                'line_classes.to_date'
            )
            ->leftJoin('lines', 'line_classes.line_id', '=', 'lines.id')
            ->leftJoin('classes', 'line_classes.class_id', '=', 'classes.id')
            ->where('line_classes.line_id', '=', $id)
            ->where('line_classes.deleted_at', '=', null)
            ->get();

        $specialities = DB::table('line_specialities')
            ->select(
                'line_specialities.id',
                'lines.name as line_name',
                'specialities.name as speciality_name',
                'line_specialities.from_date',
                'line_specialities.to_date'
            )
            ->leftJoin('lines', 'line_specialities.line_id', '=', 'lines.id')
            ->leftJoin('specialities', 'line_specialities.speciality_id', '=', 'specialities.id')
            ->where('line_specialities.line_id', '=', $id)
            ->where('line_specialities.deleted_at', '=', null)
            ->get();
        $model_id = $id;
        $model_type = Line::class;

        LogActivity::addLog($model_id, $model_type);

        return response()->json([
            'bricks' => $bricks,
            'specialities' => $specialities,
            'classes' => $classes,
            'line' => $line,
            'division_types' => $division_types,
            'divisions' => $divisions,
            'div_parents' => $div_parents,
            'products' => $products,
            'users' => $users,
            'division_users' => $division_users
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Line $line
     * @return \Illuminate\Http\JsonResponse
     */
    public function edit($id)
    {
        $currencies = Currency::orderBy('sort', 'ASC')->select('currencies.id', 'currencies.name')->get();
        $timezones = DateTimeZone::listIdentifiers();
        $countries = Country::orderBy('sort', 'ASC')->select('countries.id', 'countries.name')->get();
        $line = DB::table('lines')
            ->select('lines.id', 'lines.name', 'lines.timezone', 'lines.notes', 'lines.sort', 'lines.currency_id', 'lines.country_id', 'lines.from_date', 'lines.to_date')
            ->where('lines.id', '=', $id)
            ->first();
        $model_id = $id;
        $model_type = Line::class;
        LogActivity::addLog($model_id, $model_type);

        return response()->json([
            'line' => $line,
            'countries' => $countries,
            'timezones' => $timezones,
            'currencies' => $currencies,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Line $line
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(LineRequest $request, $id)
    {
        $line = Line::find($id);
        $line->name = $request->input('name');
        $line->timezone = $request->input('timezone');
        $line->notes = $request->input('notes');
        $line->sort = $request->input('sort');
        $line->currency_id = $request->input('currency_id');
        $line->country_id = $request->input('country_id');
        $line->from_date = $request->input('from_date');
        $line->to_date = $request->input('to_date');
        $line->save();
        $model_id = $id;
        $model_type = Line::class;

        LogActivity::addLog($model_id, $model_type);

        return response()->json(['status' => 'success']);
    }

    public function delete($id)
    {
        $line = Line::find($id);
        $lineDivisions = $line->divisions()->where('deleted_at', null)->count();
        $lineDivisionTypes = $line->linedivisiontypes()->where('deleted_at', null)->count();
        $lineUsers = $line->lineusers()->where('deleted_at', null)->count();
        if ($lineDivisions > 0 || $lineDivisionTypes > 0 || $lineUsers > 0)
            return response()->json(['message' => 'This Line has Data if you delete it, you will delete all data']);
        else
            return response()->json(['message' => 'Line has no Data, you Can Delete it..']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Line $line
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $line = Line::find($id);
        if ($line) {
            $line->delete();
        }
        $lineDivisions = LineDivision::where("line_id", $id)->pluck('id');
        LineDivisionType::where("line_id", $id)->update([
            'deleted_at' => Carbon::now()->format('Y-m-d H:i:s')
        ]);
        foreach ($lineDivisions as $lineDivision) {
            LineDivParent::where("parent_id", $lineDivision)->update([
                'deleted_at' => Carbon::now()->format('Y-m-d H:i:s')
            ]);
        }
        LineDivision::where("line_id", $id)->update([
            'deleted_at' => Carbon::now()->format('Y-m-d H:i:s')
        ]);
        LineProduct::where("line_id", $id)->update([
            'deleted_at' => Carbon::now()->format('Y-m-d H:i:s')
        ]);
        foreach ($lineDivisions as $lineDivision) {
            LineDivisionUser::where("line_division_id", $lineDivision)->update([
                'deleted_at' => Carbon::now()->format('Y-m-d H:i:s')
            ]);
        }
        LineUser::where("line_id", $id)->update([
            'deleted_at' => Carbon::now()->format('Y-m-d H:i:s')
        ]);
        LineBricks::where("line_id", $id)->update([
            'deleted_at' => Carbon::now()->format('Y-m-d H:i:s')
        ]);
        LineClasses::where("line_id", $id)->update([
            'deleted_at' => Carbon::now()->format('Y-m-d H:i:s')
        ]);
        LineSpecialities::where("line_id", $id)->update([
            'deleted_at' => Carbon::now()->format('Y-m-d H:i:s')
        ]);
        $model_id = $id;
        $model_type = Line::class;

        LogActivity::addLog($model_id, $model_type);

        return response()->json(['status' => 'success']);
    }

    public function exportlines()
    {
        return Line::export(ExcelType::XLSX);
    }

    public function import(ImportRequest $request)
    {
        Line::import($request);
        return $this->respondSuccess();
    }

    public function updateByImport(ImportRequest $request)
    {
        Line::import(request: $request, update: true);

        return $this->respondSuccess();
    }

    public function importLineStructure(ImportRequest $request)
    {
        LineStructure::import($request);
        return $this->respondSuccess();
    }

    public function exportcsv()
    {
        return Line::export(ExcelType::CSV);
    }

    public function exportpdf()
    {
        $lines = Line::where('deleted_at', null)->with(['country', 'currency', 'linedivisiontypes', 'divisions', 'lineproducts', 'lineusers', 'linedivusers'])->get();
        return Line::exportPdf($lines);
    }

    public function sendmail(MailRequest $request)
    {
        $lines = Line::where('deleted_at', null)->with(['country', 'currency', 'linedivisiontypes', 'divisions', 'lineproducts', 'lineusers', 'linedivusers', 'lineClasses'])->get();
        return Line::sendMail($request, $lines);
    }

    public function LineDetailsPositions()
    {
        $lines = Line::with(['divisions:id,name,line_id', 'products:id,name'])->get()
            ->map(function ($line) {
                return [
                    'id' => $line->id,
                    'name' => $line->name,
                    'divisions' => $line->divisions,
                    'products' => $line->products,
                ];
            });
        return $this->respond($lines);
    }

    public function replicate(Request $request)
    {
        $max_sort = Line::withTrashed()->max('sort');
        if (!$max_sort) {
            $max_sort = 100;
        } else {
            $max_sort += 100;
        }
        $line = Line::find($request->line);
        DB::transaction(function () use ($request, $line, $max_sort) {
            $newLine = Line::create([
                'currency_id' => 1,
                'country_id' => 1,
                'name' => $request->line_name,
                'sort' => $max_sort,
                'from_date' => $line->from_date,
                'to_date' => $line->to_date,
                'file_id' => $line->file_id,

            ]);
            if (!empty($request->options)) {
                $lineDivisions = LineDivision::where('line_id', $line->id)->where('from_date', '<=', now())
                    ->where(fn($q) => $q->where('to_date', '>', (string)Carbon::now())
                        ->orWhere('to_date', null))->get();
                foreach ($request->options as $option) {
                    if ($option == 1) {
                        $lineDivisionTypes = LineDivisionType::where('line_id', $line->id)
                            ->where('from_date', '<=', now())->where(fn($q) => $q->where('to_date', '>', (string)Carbon::now())
                                ->orWhere('to_date', null))->get();
                        foreach ($lineDivisionTypes as $lineDivisionType) {
                            LineDivisionType::create([
                                'line_id' => $newLine->id,
                                'divisiontype_id' => $lineDivisionType->divisiontype_id,
                                'from_date' => $lineDivisionType->from_date,
                                'to_date' => $lineDivisionType->to_date,
                                'file_id' => $lineDivisionType->file_id,
                            ]);
                        }
                    }
                    if ($option == 2) {
                        foreach ($lineDivisions as $lineDivision) {
                            $newLineDivision = LineDivision::create([
                                'line_id' => $newLine->id,
                                'name' => $lineDivision->name,
                                'division_type_id' => $lineDivision->division_type_id,
                                'is_kol' => $lineDivision->is_kol,
                                'from_date' => $lineDivision->from_date,
                                'to_date' => $lineDivision->to_date,
                                'file_id' => $lineDivision->file_id,
                            ]);
                            if (array_search(4, $request->options)) {
                                $lineBricks = LineBricks::where('line_id', $line->id)->where('line_division_id', $lineDivision->id)
                                    ->where('from_date', '<=', now())->where(fn($q) => $q->where('to_date', '>', (string)Carbon::now())
                                        ->orWhere('to_date', null))->get();
                                if (count($lineBricks) > 0) {
                                    foreach ($lineBricks as $lineBrick) {
                                        LineBricks::create([
                                            'line_id' => $newLine->id,
                                            'brick_id' => $lineBrick->brick_id,
                                            'line_division_id' => $newLineDivision->id,
                                            'ratio' => $lineBrick->ratio,
                                            'from_date' => $lineBrick->from_date,
                                            'to_date' => $lineBrick->to_date,
                                            'file_id' => $lineBrick->file_id,
                                        ]);
                                    }
                                }
                            }
                        }
                    }
                    if ($option == 3) {
                        foreach ($lineDivisions as $lineDivision) {
                            $parent = LineDivParent::where('line_div_id', $lineDivision->id)
                                ->where('from_date', '<=', now())->where(fn($q) => $q->where('to_date', '>', (string)Carbon::now())
                                    ->orWhere('to_date', null))->first()?->parent;
                            if ($parent) {
                                $parent_id = LineDivision::where('name', $parent->name)->where('line_id', $newLine->id)->first()?->id ?? null;
                                $newLineDivision = LineDivision::where('name', $lineDivision->name)->where('line_id', $newLine->id)->first();
                                if ($parent_id)
                                    LineDivParent::create([
                                        'line_div_id' => $newLineDivision->id,
                                        'parent_id' => $parent_id,
                                        'from_date' => $parent->from_date,
                                        'to_date' => $parent->to_date,
                                        'file_id' => $parent->file_id,
                                    ]);
                            }
                        }
                    }
                }
            }
        });
        return $this->respondSuccess();
    }
}
