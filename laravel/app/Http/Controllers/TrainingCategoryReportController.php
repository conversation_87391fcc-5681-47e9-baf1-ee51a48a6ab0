<?php

namespace App\Http\Controllers;

use App\Action;
use App\ActualVisit;
use App\Exceptions\CrmException;
use App\Form;
use App\Helpers\LogActivity;
use App\Http\Controllers\Controller;
use App\Line;
use App\LineDivision;
use App\LineDivisionType;
use App\LineProduct;
use App\Models\QuizCategory;
use App\Permission;
use App\Product;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TrainingCategoryReportController extends ApiController
{


    public function getLineData(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        // $lines = Line::whereIntegerInRaw('id', $request->lines)->get();
        $products = Product::whereHas(
            'lineproducts',
            fn($q) =>
            $q->whereIntegerInRaw('line_products.line_id', $request->lines)->where('line_products.from_date', '<=', now())
                ->where(fn($q) => $q->where('line_products.to_date', '>', (string)Carbon::now())
                    ->orWhere('line_products.to_date', null))
        )->get();
        $categories =  QuizCategory::get();

        return response()->json([
            'role' => $user,
            'products' => $products,
            'categories' => $categories
        ]);
    }
    public function filter(Request $request)
    {
        $quiz = $request->trainingCategoryFilter;
        $lines = Line::when(!empty($quiz['line']), fn($q) => $q->whereIn("lines.id", $quiz['line']))->get();;
        $categories = QuizCategory::select('quiz_categories.id', 'quiz_categories.name')
            ->when(!empty($quiz['categories']), fn($q) => $q->whereIn("quiz_categories.id", $quiz['categories']))->get();

        $data = new Collection([]);
        $fields = new Collection([]);
        $clickable_fields = new Collection([]);

        $products = Product::select('products.id', 'products.name', 'lines.name as line', 'lines.id as line_id')
            ->leftJoin('line_products', 'products.id', 'line_products.product_id')
            ->leftJoin('lines', 'line_products.line_id', 'lines.id')
            ->where('line_products.from_date', '<=', Carbon::now())
            ->whereNull('line_products.deleted_at')
            ->whereIntegerInRaw('line_products.line_id', $lines->pluck('id')->toArray())
            ->where(fn($q) => $q->where('line_products.to_date', '>', (string) Carbon::now())
                ->orWhere('line_products.to_date', null))
            ->when(!empty($quiz['products']), fn($q) => $q->whereIn("products.id", $quiz['products']))->get();

        if ($quiz['view'] == 'Details') {
            $fields = collect(['line', 'product', 'question', 'answers', 'correct', 'category']);
            $data = $this->details($lines, $products, $categories);
        } else {
            $fields = collect(['line', 'product']);
            foreach ($categories as $category) {
                $fields = $fields->push($category['name']);
                $clickable_fields = $clickable_fields->push($category['name']);
            }
            $fields = $fields->merge(['total']);
            $products->each(function ($product) use ($categories, $data) {
                $data = $data->push($this->statistics($product, $categories));
            });
        }



        return response()->json([
            'data' => $data->unique("id")->values(),
            'fields' => $fields,
            'clickable_fields' => $clickable_fields
        ]);
    }
    private function statistics($product, $categories)
    {
        $data = collect([
            'id' => $product->id,
            'line_id' => $product->line_id,
            'line' => $product->line,
            'product' => $product->name
        ]);
        $total = 0;

        $categories->each(function ($category) use ($data, &$total, &$total_per_category, $product) {
            $total_per_category = 0;
            $questions = DB::table('question_product')->select(
                'products.id as product_id',
                'products.name as product',
                'quiz_questions.name as quiz_question',
                'quiz_questions.id as question_id',
                'quiz_questions.quiz_category_id as quiz_category_id',
                'quiz_categories.name as quiz_category',
            )
                ->leftJoin('products', 'question_product.product_id', 'products.id')
                ->leftJoin('quiz_questions', 'question_product.quiz_question_id', 'quiz_questions.id')
                ->leftJoin('quiz_categories', 'quiz_questions.quiz_category_id', 'quiz_categories.id')
                ->where('products.id', '=', $product->id)
                ->whereNull('quiz_questions.deleted_at')
                ->where('quiz_questions.quiz_category_id', '=', $category->id)
                ->get();
            $category_count = $questions->count();
            $data->put($category->name, $category_count);

            $total_per_category += $category_count;
            $total += $total_per_category;
            $data->put('total', $total);
        });
        return $data;
    }

    private function details($lines, $products, $categories)
    {
        $questions = DB::table('quiz_questions')->select(
            'quiz_questions.id as id',
            'quiz_questions.name as question',
            DB::raw('IFNULL(group_concat(distinct crm_products.name),"") as product'),
            DB::raw('IFNULL(group_concat(distinct crm_products.id),"") as product_id'),
            DB::raw('IFNULL(group_concat(distinct crm_quiz_questions.quiz_category_id),"") as category_id'),
            DB::raw('IFNULL(group_concat(distinct crm_quiz_categories.name),"") as category'),
            DB::raw('IFNULL(group_concat(distinct crm_lines.id),"") as line_id'),
            DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
            DB::raw('IFNULL(group_concat(distinct crm_answers.name),"") as answers'),
            DB::raw('IFNULL(group_concat(distinct crm_correct_answers.name),"") as correct'),
        )
            ->leftJoin('quiz_answers as answers', 'quiz_questions.id', 'answers.quiz_question_id')
            ->leftJoin('quiz_answers as correct_answers', function ($join) {
                $join->on('quiz_questions.id', 'correct_answers.quiz_question_id')->where('correct_answers.correct', 1);
            })
            ->leftJoin('question_product', 'quiz_questions.id', 'question_product.quiz_question_id')
            ->leftJoin('products', 'question_product.product_id', 'products.id')
            ->leftJoin('line_products', function ($join) use ($lines) {
                $join->on('products.id', 'line_products.product_id')
                    ->whereIntegerInRaw('line_products.line_id', $lines->pluck('id')->toArray())
                    ->where('line_products.from_date', '<=', Carbon::now())
                    ->where(fn($q) => $q->where('line_products.to_date', '>=', (string) Carbon::now())
                        ->orWhere('line_products.to_date', null));
            })
            ->leftJoin('lines', 'line_products.line_id', 'lines.id')
            ->leftJoin('quiz_categories', 'quiz_questions.quiz_category_id', 'quiz_categories.id')
            ->whereIntegerInRaw('products.id', $products->pluck('id')->toArray())
            ->whereIntegerInRaw('quiz_questions.quiz_category_id', $categories->pluck('id')->toArray())
            ->whereNull('quiz_questions.deleted_at')
            ->groupBy('quiz_questions.id')
            ->get();
        // throw new CrmException($questions);
        return $questions;
    }

    public function showData(Request $request)
    {
        $quiz = $request->quizFilter;
        $line = Line::find($request->item['line_id']);
        $product = Product::find($request->item['id']);
        $category = QuizCategory::where('name', $request->column)->first();
        // throw new CrmException($category);
        // $column = $request->column;

        $data = DB::table('question_product')->select(
            'products.id as product_id',
            'products.name as product',
            'quiz_questions.id as quiz_question_id',
            'quiz_questions.name as quiz_question',
            'quiz_questions.quiz_category_id as quiz_category_id',
            'quiz_categories.name as quiz_category',
            'lines.name as line'
        )
            ->leftJoin('products', 'question_product.product_id', 'products.id')
            ->leftJoin('quiz_questions', 'question_product.quiz_question_id', 'quiz_questions.id')
            ->leftJoin('quiz_categories', 'quiz_questions.quiz_category_id', 'quiz_categories.id')
            ->leftJoin('line_products', 'products.id', 'line_products.product_id')
            ->leftJoin('lines', 'line_products.line_id', 'lines.id')
            ->where('products.id', '=', $product->id)
            ->where('lines.id', '=', $line->id)
            ->where('quiz_questions.quiz_category_id', '=', $category->id)
            ->get()->unique('quiz_question_id')->values();
        return $this->respond($data);
    }
}
