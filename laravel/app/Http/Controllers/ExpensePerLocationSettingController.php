<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Helpers\ExcelImporter;
use App\Http\Controllers\Controller;
use App\Http\Requests\ImportRequest;
use App\Http\Requests\MailRequest;
use App\Models\Expenses\Expense;
use App\Models\Expenses\PerLocation\ExpenseLocationSetting;
use App\Imports\Updates\Expenses\PerLocation\ExpenseLocationSettingsImport as UpdatesExpenseLocationSettingsImport;
use App\Models\Expenses\Types\ExpenseType;
use Maatwebsite\Excel\Excel as ExcelType;
use Illuminate\Http\Request;

class ExpensePerLocationSettingController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $divisions = ExpenseLocationSetting::get();
        $locationExpenseId = ExpenseType::where('is_location',1)->first()?->id;
        return $this->respond(['divisions' => $divisions,'type_id'=>$locationExpenseId]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $max_sort = ExpenseLocationSetting::withTrashed()->max('sort');
        if (!$max_sort)
            $max_sort = 100;
        else
            $max_sort += 100;
        ExpenseLocationSetting::create([
            'name'=>$request->name,
            'notes'=>$request->notes,
            'sort'=>$max_sort,
        ]);
        return $this->respondCreated();
    }


    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        return $this->respond(ExpenseLocationSetting::find($id));
    }


    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $setting = ExpenseLocationSetting::find($id);
        $setting->name = $request->name;
        $setting->notes = $request->notes;
        $setting->sort = $request->sort;
        $setting->save();
        return $this->respondSuccess();
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        ExpenseLocationSetting::find($id)->delete();
        return $this->respondSuccess();
    }
    public function import(ImportRequest $request)
    {
        ExpenseLocationSetting::import($request);
        return $this->respondSuccess();
    }

    public function updateByImport(ImportRequest $request)
    {
        ExpenseLocationSetting::import(request: $request, update: true);
        return $this->respondSuccess();
    }

    public function export()
    {
        return ExpenseLocationSetting::export(ExcelType::XLSX);
    }

    public function exportcsv()
    {
        return ExpenseLocationSetting::export(ExcelType::CSV);
    }

    public function exportpdf()
    {
        $expenselocationsettings = ExpenseLocationSetting::where('deleted_at', null)->get();
        return ExpenseLocationSetting::exportPdf($expenselocationsettings);
    }

    public function sendmail(MailRequest $request)
    {
        $expenselocationsettings = ExpenseLocationSetting::where('deleted_at', null)->get();
        return ExpenseLocationSetting::sendmail($request, $expenselocationsettings);
    }
}
