<?php

namespace App\Http\Controllers;

use App\Action;
use App\Form;
use App\Services\AuthService;
use App\Setting;
use App\ErrorMessages;
use App\Exceptions\CrmException;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use App\Helpers\LogActivity;
use App\Models\AppLog;
use Exception;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Permission;

class AuthController extends Controller
{

    protected $client;
    public function username()
    {
        return 'name';
    }

    /**
     * Create a new AuthController instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:api', ['except' => ['login', 'register']]);
        $this->client = Auth::guard('api');
    }

    /**
     * Register new user.
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    public function register(Request $request)
    {
        $data = $request->validate([
            'name' => 'required|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|confirmed',
            'fullname' => 'required',
            'status' => 'required'
        ]);

        $data['password'] = bcrypt($request->password);

        $user = User::create($data);

        $token = $user->createToken('API Token')->accessToken;

        return response(['user' => $user, 'token' => $token]);
    }

    /**
     * Get a JWT via given credentials.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()->all()], 422);
        }

        $user = User::where('name', $request->name)->first();
        // Log::info($request);
        if (!empty($request->appVersion)) {
            AppLog::create([
                'user_id' => $user->id,
                'app_version' => $request->appVersion,
                'os_version' => $request->osVersion,
                'device_brand' => $request->deviceBrand,
                'os_id' => $request->OsID,
                'is_real_login' => $request->isRealLogin == 'true' || $request->isRealLogin == true ? 1 : 0,
                // 'os_type' => $request->osType,
            ]);
        }
        if (empty($user)) {
            throw new Exception('User does not exist');
        }
        if ($user->status == 'inactive' || $user->status == 'inActive' || $user->status == 'Inactive') {
            throw new Exception('This User is inactive on Gemstone.');
        }

        if (!Hash::check($request->password, $user->password)) {
            throw new Exception('Password mismatch');
        }

        $token = AuthService::login($user);


        return $this->respondWithToken($token, $user);
    }

    /**
     * Log the user out (Invalidate the token).
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout()
    {
        $user = Auth::user();

        AuthService::logout($user);

        return response()->json(['status' => 'success']);
    }

    /**
     * Refresh a token.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function refresh()
    {
        return $this->respondWithToken(auth()->refresh());
    }

    /**
     * Get the token array structure.
     *
     * @param  string $token
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function respondWithToken($token, $user)
    {
        $logged_user = $user;
        // $user_roles = $logged_user->roles;
        $font_size = Setting::where('key', 'gemstone_font_size')->value('value');
        $userDetails = $logged_user->details->first();
        $date_format = Setting::where('key', 'date_format')->first();
        $confirm_delete = ErrorMessages::where('slug', 'confirm_delete')->first();
        $confirm_restore = ErrorMessages::where('slug', 'confirm_restore')->first();
        $successfully_created = ErrorMessages::where('slug', 'successfully_created')->first();
        $successfully_updated = ErrorMessages::where('slug', 'successfully_updated')->first();
        $successfully_deleted = ErrorMessages::where('slug', 'successfully_deleted')->first();
        $successfully_uploaded = ErrorMessages::where('slug', 'successfully_uploaded')->first();
        $failed_delete = ErrorMessages::where('slug', 'failed_delete')->first();
        $failed_login = ErrorMessages::where('slug', 'failed_login')->first();
        $failed_change_password = ErrorMessages::where('slug', 'failed_change_password')->first();
        $failed_export_template = ErrorMessages::where('slug', 'failed_export_template')->first();
        return response()->json([
            'user' => $logged_user,
            'userDetails' => $userDetails,
            'date_format' => $date_format->value,
            'confirm_delete' => $confirm_delete->message,
            'confirm_restore' => $confirm_restore->message,
            'successfully_created' => $successfully_created->message,
            'successfully_updated' => $successfully_updated->message,
            'successfully_deleted' => $successfully_deleted->message,
            'successfully_uploaded' => $successfully_uploaded->message,
            'failed_delete' => $failed_delete->message,
            'failed_delete_code' => $failed_delete->code,
            'failed_login' => $failed_login->message,
            'failed_change_password' => $failed_change_password->message,
            'failed_export_template_code' => $failed_export_template->code,
            'failed_export_template' => $failed_export_template->message,
            'access_token' => $token,
            'font_size' => $font_size,
            'token_type' => 'bearer',
            // 'expires_in' => auth()->factory()->getTTL() * 60 * 24 * 7
        ]);
    }

    public function user(Request $request)
    {
        return response()->json($request->user());
    }
}
