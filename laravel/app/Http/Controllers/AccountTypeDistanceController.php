<?php

namespace App\Http\Controllers;

use App\AccountType;
use App\Helpers\LogActivity;
use App\Models\AccountTypeDistance;
use Illuminate\Http\Request;

class AccountTypeDistanceController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $account_types = AccountType::get();
        $distances = AccountTypeDistance::get()->map(function ($distance) {
            return [
                'id' => $distance->id,
                'type' => $distance->type->name,
                'distance' => $distance->distance
            ];
        });
        LogActivity::addLog();
        return response()->json(['account_types' => $account_types, 'distances' => $distances]);
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $distance = AccountTypeDistance::create([
            'type_id' => $request->type_id,
            'distance' => $request->distance,
        ]);
        $model_id = $distance->id;
        $model_type = AccountTypeDistance::class;

        LogActivity::addLog($model_id, $model_type);
        return $this->respondCreated();
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $accountTypeDistance = AccountTypeDistance::find($id);
        $model_id = $id;
        $model_type = AccountType::class;
        LogActivity::addLog($model_id, $model_type);
        return $this->respond($accountTypeDistance);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $distance = AccountTypeDistance::find($id);
        $distance->type_id = $request->type_id;
        $distance->distance = $request->distance;
        $distance->save();
        $model_id = $id;
        $model_type = AccountTypeDistance::class;
        LogActivity::addLog($model_id, $model_type);
        return $this->respondSuccess();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $model_id = $id;
        AccountTypeDistance::find($id)->delete();
        $model_type = AccountTypeDistance::class;
        LogActivity::addLog($model_id, $model_type);
        return $this->respondSuccess();
    }
}
