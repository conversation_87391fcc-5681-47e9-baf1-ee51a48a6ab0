<?php

namespace App\Http\Controllers;

use App\Account;
use App\AccountLines;
use App\AccountType;
use App\Brick;
use App\Classes;
use App\DivisionType;
use App\Doctor;
use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Helpers\Notifications\NotificationHelper;
use App\Http\Requests\AccountRequestApprovalRequest;
use App\Http\Requests\AddAccountRequest;
use App\Http\Requests\ApprovalEmployeesRequest;
use App\Http\Requests\ExpenseDisApprovalRequest;
use App\Line;
use App\LineBricks;
use App\LineDivision;
use App\Models\AccountRequest;
use App\Models\ApprovalFlowUser;
use App\Models\ApprovalSetting;
use App\Models\ListSetting;
use App\Models\ListType;
use App\Models\NewAccountDoctor;
use App\Notifications\AccountRequestApprovedNotification;
use App\Notifications\AccountRequestCreatedNotification;
use App\Notifications\ExpenseApprovedNotification;
use App\Notifications\PlanCreatedNotification;
use App\PlanVisitDetails;
use App\Position;
use App\Reason;
use App\Speciality;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AccountRequestController extends ApiController
{
    public function getSettings()
    {
        return $this->respond(ListSetting::where('key', 'account_request_type')->value('value'));
    }
    public function getBricks()
    {
        $status = null;
        $date = null;
        /**@var User $user */
        $user = Auth::user();
        $lines = $user->userLines();
        $divisions = $user->divisions();
        $divisionIds = $divisions->pluck('line_divisions.id');
        $status = 'Inactive';
        $date = '2035-01-01';
        $specialities = Speciality::whereHas('lineSpecialities', fn($q) => $q->whereIn('line_id', $lines->pluck('id')))->get()->unique('id')->values();
        $classes = Classes::whereHas('lineClasses', fn($q) => $q->whereIn('line_id', $lines->pluck('id')))->get()->unique('id')->values();
        $bricks = Brick::whereHas('lineBricks', fn($q) => $q->whereIn('line_division_id', $divisionIds))
            ->get()->unique('id')->values();
        $accountTypes = AccountType::select('id', 'name')->orderBy('sort', 'ASC')->get();
        return $this->respond([
            'bricks' => $bricks,
            'status' => $status,
            'date' => $date,
            'accountTypes' => $accountTypes,
            'specialities' => $specialities,
            'classes' => $classes,
        ]);
    }
    public function lines()
    {
        $status = null;
        $date = null;
        /**@var User $user */
        $user = Auth::user();
        $lines = $user->userLines();
        $lastLevel = $user->divisions()->first()?->DivisionType?->last_level;
        if ($lastLevel == 1) {
            $status = 'Active';
            $date = Carbon::now()->firstOfMonth()->toDateString();
        } else {
            $status = 'Inactive';
            $date = '2035-01-01';
        }
        $accountTypes = AccountType::select('id', 'name')->orderBy('sort', 'ASC')->get();
        return $this->respond(compact('lines', 'accountTypes', 'lastLevel', 'status', 'date'));
    }
    public function divisions(Line $line)
    {
        $division_type = DivisionType::where('last_level', '=', 1)->get('id');
        /**@var User $user */
        $user = Auth::user();
        $divisions = $user->userDivisions($line)->where('division_type_id', $division_type->first()->id)->values();
        $specialities = $line->specialities;
        $classes = $line->classes;
        return $this->respond(compact('divisions', 'specialities', 'classes'));
    }
    public function bricks(Line $line, LineDivision $division)
    {
        $bricks = LineBricks::where('line_division_id', $division->id)->where('line_id', $line->id)
            ->where(fn($q) => $q->where('to_date', '=', null)->orWhere('to_date', '>=', (string)Carbon::now()))
            ->with('brick')->get()->pluck('brick');
        return $this->respond($bricks);
    }
    public function store(AddAccountRequest $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $accounts = $request->accounts;
        $line = $accounts[0]["line_id"] ? Line::find($accounts[0]["line_id"]) : Line::find($user->userLines()->first()?->id);
        // throw new CrmException(Line::find($user->userLines()->first()?->id));
        $hasApprovable = $user->division($line)?->divisionType?->planable?->where('line_id', $line->id)->first()
            ->approvables()->wherePivot('request_type', AccountRequest::class)->count() > 0;
        // throw new CrmException($hasApprovable);
        DB::transaction(function () use ($line, $request, $accounts, $user, $hasApprovable) {
            $approval = 0;
            foreach ($accounts as $account) {
                $codes = $this->getCodes($account, $user);
                $accountCode = $codes['accountCode'];
                $doctorCode = $codes['doctorCode'];
                $accountRequest = AccountRequest::create([
                    'user_id' => $user->id,
                    'line_id' => $account['line_id'],
                    'div_id' => $account['div_id'],
                    'brick_id' => $account['brick_id'],
                    'type_id' => $account['type_id'],
                    'account' => $account['account'],
                    'address' => $account['address'],
                    'account_code' => $accountCode,
                    'acc_class_id' => $account['acc_class_id'],
                    'doctor' => $account['doctor'],
                    'speciality_id' => $account['speciality_id'],
                    'doctor' => $account['doctor'],
                    'ucode' => $doctorCode,
                    'doc_class_id' => $account['doc_class_id'],
                    'active_date' => $request->active_date,
                ]);
                PlanVisitDetails::firstOrCreate([
                    'visitable_id' => $accountRequest->id,
                    'visitable_type' => AccountRequest::class,
                ], [
                    'approval' => $hasApprovable ? null : 1,
                    'created_by' => $user->id,
                    'date' => $request->active_date,
                ]);
                NotificationHelper::send(
                    collect($user->approvableUsers($line->id)),
                    new AccountRequestCreatedNotification('Account Request Created', auth()->user())
                );
                if (!$hasApprovable) {
                    $this->createAccount($accountRequest, $approval);
                }
            }
        });
        // LogActivity::addLog();
        return $this->respondCreated();
    }
    public function getCodes($account, $user)
    {
        $type = AccountType::select('name')->where('id', $account['type_id'])->first()->name;
        $speciality = Speciality::select('name')->where('id', $account['speciality_id'])->first()->name;
        $account_code = $type . '-' . $user->id . '-' . rand();
        $doctor_code = $speciality . '-' . $user->id . '-' . rand();
        $accountExists = Account::where('code', $account_code)->exists();
        $doctorExists = Doctor::where('ucode', $doctor_code)->exists();
        if ($accountExists || $doctorExists) {
            $this->getCodes($account, $user);
        } else {
            return array('accountCode' => $account_code, 'doctorCode' => $doctor_code);
        }
    }


    public function getAccounts(Request $request)
    {

        $reasons = Reason::where('request_type', AccountRequest::class)->get();
        $accounts = collect([]);
        $from_date = Carbon::parse($request->from_date)->startOfDay();
        $to_date = Carbon::parse($request->to_date)->endOfDay();
        $accounts = AccountRequest::whereIn('user_id', $request->users_id)->whereBetween('created_at', [$from_date, $to_date])
            ->get();
        $approvalSetting = ApprovalSetting::where('key', 'account_request_approval_center_flow')->value('value');
        $line = Line::find($request->line_id);
        /**@var User $authUser */
        $authUser = Auth::user();
        $approvalData = $authUser->userApprovals($from_date, $to_date);
        $lines = $approvalData['lines'];
        $linesAapprovables = $approvalData['linesAapprovables'];
        $required = 0;
        $dataFlow = '';
        $scanLevel = 1;
        $accounts = $accounts->filter(
            function (AccountRequest $account) use ($scanLevel, $from_date, $to_date, $lines, $linesAapprovables, $approvalSetting, $line, $authUser, &$required, $dataFlow) {
                if ($approvalSetting == 'No') {
                    return is_null($account->details?->approval);
                } else {
                    if (!isNullable($account->details)) {
                        $flows = $account->details;
                        $disapproved = $flows->approvalFlows()->where('approval', 0)->first();
                        if ($disapproved) return;
                        $approvablesCountOnThisShit = $flows->approvalFlows()->count();
                        $data = $authUser->approvalWidget($account, $authUser, AccountRequest::class, $from_date, $to_date, $lines, $linesAapprovables);
                        $dataFlow = $data['linesDataFlow'];
                        $currentFlow = $dataFlow?->flow;
                        $required = $dataFlow?->required;
                        $vacantCount = $data['vacantCount'];
                        $haveToApprove = $approvablesCountOnThisShit === $currentFlow - ($vacantCount + $scanLevel);
                        return $haveToApprove;
                    }
                }
            }
        )
            ->map(function ($account) use ($reasons, $required) {
                return [
                    'id' => $account->id,
                    'emp' => $account->user->fullname,
                    'line' => $account->line?->name ?? '',
                    'line_id' => $account->line_id ?? '',
                    'div_id' => $account->div_id ?? '',
                    'brick_id' => $account->brick_id,
                    'acc_class_id' => $account->acc_class_id,
                    'division' => $account->division?->name ?? '',
                    'brick' => $account->brick->name,
                    'date' => $account->active_date,
                    'account' => $account->account,
                    'acc_code' => $account->account_code,
                    'type' => $account->accountType->name,
                    'doctor' => $account->doctor,
                    'doc_code' => $account->ucode,
                    'speciality' => $account->speciality->name,
                    'required' => $required,
                    'visitable_type' => AccountRequest::class,
                    'reason_id' => null
                ];
            });
        $fields = collect(["s", "id", "emp", "brick", "account", "acc_code", "type", "doctor", "doc_code", "date", "speciality", "actions"]);
        return $this->respond(['accounts' => $accounts->values(), 'fields' => $fields, 'reasons' => $reasons]);
    }

    public function accept(Request $request)
    {
        // throw new CrmException($request->all());
        $approvalSetting = ApprovalSetting::where('key', 'account_request_approval_center_flow')->value('value');
        $lines = Line::select('id', 'name')->get();
        $requestSetting = ListSetting::where('key', 'account_request_type')->value('value');
        $accounts = $request->accounts;
        /**@var User $authUser */
        $authUser = Auth::user();
        $users = collect([]);
        $approval = 1;
        DB::transaction(function () use ($lines, $requestSetting, $approvalSetting, $accounts, $users, $authUser, $approval) {
            foreach ($accounts as $account) {
                $objAccount = resolve($account['visitable_type'])->find($account['visitable_id']);
                // throw new CrmException($objAccount);
                $users->push($objAccount->user);
                $detail = $objAccount->details;
                if ($approvalSetting == 'Yes') {
                    ApprovalFlowUser::firstOrCreate([
                        'detail_id' => $detail->id,
                        'user_id' => $authUser->id,
                        'approval' => 1,
                    ]);
                    if ($account['required'] == 1) {
                        $detail->user_id = $authUser->id;
                        $detail->approval = 1;
                        $detail->save();
                        if ($requestSetting == 'Line') $this->createAccount($objAccount, $approval);
                        else $this->createAccountPerBrick($objAccount, $lines);
                    }
                } else {
                    $detail->user_id = $authUser->id;
                    $detail->approval = 1;
                    $detail->save();
                    if ($requestSetting == 'Line') $this->createAccount($objAccount, $approval);
                    else $this->createAccountPerBrick($objAccount, $lines);
                }
            }
            NotificationHelper::send(
                $users->unique('id'),
                new AccountRequestApprovedNotification('Account Request Get Approved', auth()->user())
            );
        });

        // LogActivity::addLog();
        return response()->json(['status' => 'success']);
    }
    public function reject(AccountRequestApprovalRequest $request)
    {
        $approvalSetting = ApprovalSetting::where('key', 'account_request_approval_center_flow')->value('value');
        $accounts = $request->accounts;
        /**@var User $authUser */
        $authUser = Auth::user();
        $users = collect([]);
        DB::transaction(function () use ($approvalSetting, $accounts, $users, $authUser) {
            foreach ($accounts as $account) {
                $objAccount = resolve($account['visitable_type'])->find($account['visitable_id']);
                $users->push($objAccount->user);
                $detail = $objAccount->details;
                if ($approvalSetting == 'Yes') {
                    ApprovalFlowUser::firstOrCreate([
                        'detail_id' => $detail->id,
                        'user_id' => $authUser->id,
                        'approval' => 0,
                    ]);
                    if ($account['required'] == 1) {
                        $detail->user_id = $authUser->id;
                        $detail->approval = 0;
                        $detail->save();
                    }
                } else {
                    $detail->user_id = $authUser->id;
                    $detail->approval = 0;
                    $detail->save();
                }
                if ($account['reason_id'] != null) {
                    $reason = Reason::find($account['reason_id']);
                    $objAccount->reasons()->attach($reason, [
                        'user_id' => $authUser->id,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
            }
            NotificationHelper::send(
                $users->unique('id'),
                new AccountRequestApprovedNotification('Account Request Get Disapproved', auth()->user())
            );
        });
        return response()->json(['status' => 'reject']);
    }

    public function createAccount($account, $approval)
    {

        $newAccount = Account::create([
            'name' => $account['account'],
            'code' => $account['account_code'],
            'type_id' => $account['type_id'],
            'address' => $account['address'],
            'active_date' => $account['active_date'] >= Carbon::now()->startOfMonth()->toDateString()
                ? Carbon::now()->startOfMonth()->toDateString() : $account['active_date'],
        ]);
        $newDoctor = Doctor::create([
            'name' => $account['doctor'],
            'speciality_id' => $account['speciality_id'],
            'ucode' => $account['ucode'],
            'class_id' => $account['doc_class_id'],
            'active_date' => $account['active_date'] >= Carbon::now()->startOfMonth()->toDateString()
                ? Carbon::now()->startOfMonth()->toDateString() : $account['active_date'],
        ]);
        $settingFavourite = ListType::first()->favourite_type == 'Doctor' ? true : false;
        if ($settingFavourite) {
            $accountLine = AccountLines::create([
                'line_id' => $account['line_id'],
                'line_division_id' => $account['div_id'],
                'brick_id' => $account['brick_id'],
                'class_id' => $account['acc_class_id'],
                'account_id' => $newAccount->id,
                'from_date' => $account['active_date'] >= Carbon::now()->startOfMonth()->toDateString()
                    ? Carbon::now()->startOfMonth()->toDateString() : $account['active_date'],
            ]);
            NewAccountDoctor::create([
                'account_lines_id' => $accountLine->id,
                'line_id' => $account['line_id'],
                'account_id' => $newAccount->id,
                'doctor_id' => $newDoctor->id,
                'from_date' => $account['active_date'],
            ]);
        } else {
            $accountLine = AccountLines::create([
                'line_id' => $account['line_id'],
                'line_division_id' => $account['div_id'],
                'brick_id' => $account['brick_id'],
                'class_id' => $account['acc_class_id'],
                'account_id' => $newAccount->id,
                'from_date' => $account['active_date'],
            ]);
            NewAccountDoctor::create([
                'account_lines_id' => $accountLine->id,
                'line_id' => $account['line_id'],
                'account_id' => $newAccount->id,
                'doctor_id' => $newDoctor->id,
                'from_date' => $account['active_date'],
            ]);
        }
    }
    public function createAccountPerBrick($account, $lines)
    {
        $newAccount = Account::create([
            'name' => $account['account'],
            'code' => $account['account_code'],
            'type_id' => $account['type_id'],
            'address' => $account['address'],
            'active_date' => $account['active_date'] >= Carbon::now()->startOfMonth()->toDateString()
                ? Carbon::now()->startOfMonth()->toDateString() : $account['active_date'],
        ]);
        $newDoctor = Doctor::create([
            'name' => $account['doctor'],
            'speciality_id' => $account['speciality_id'],
            'ucode' => $account['ucode'],
            'class_id' => $account['doc_class_id'],
            'active_date' => $account['active_date'] >= Carbon::now()->startOfMonth()->toDateString()
                ? Carbon::now()->startOfMonth()->toDateString() : $account['active_date'],
        ]);
        $settingFavourite = ListType::first()->favourite_type == 'Doctor' ? true : false;
        foreach ($lines as $line) {
            $divisionId = LineBricks::where('line_id', $line->id)->where('brick_id', $account['brick_id'])->first()?->line_division_id;
            if ($divisionId) {
                if ($settingFavourite) {
                    $accountLine = AccountLines::create([
                        'line_id' => $line->id,
                        'line_division_id' => $divisionId,
                        'brick_id' => $account['brick_id'],
                        'class_id' => $account['acc_class_id'],
                        'account_id' => $newAccount->id,
                        'from_date' => $account['active_date'] >= Carbon::now()->startOfMonth()->toDateString()
                            ? Carbon::now()->startOfMonth()->toDateString() : $account['active_date'],
                    ]);
                    NewAccountDoctor::create([
                        'account_lines_id' => $accountLine->id,
                        'line_id' => $line->id,
                        'account_id' => $newAccount->id,
                        'doctor_id' => $newDoctor->id,
                        'from_date' => $account['active_date'],
                    ]);
                } else {
                    $accountLine = AccountLines::create([
                        'line_id' => $account['line_id'],
                        'line_division_id' => $divisionId,
                        'brick_id' => $account['brick_id'],
                        'class_id' => $account['acc_class_id'],
                        'account_id' => $newAccount->id,
                        'from_date' => $account['active_date'],
                    ]);
                    NewAccountDoctor::create([
                        'account_lines_id' => $accountLine->id,
                        'line_id' => $account['line_id'],
                        'account_id' => $newAccount->id,
                        'doctor_id' => $newDoctor->id,
                        'from_date' => $account['active_date'],
                    ]);
                }
            }
        }
    }
    public function filterOfEmployees(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $users = collect([]);
        $types = [DivisionType::class, Position::class];
        foreach ($request->line_id as $line) {
            foreach ($types as $type) {
                $users = $users->merge($user->planableUsers($line, AccountRequest::class, $type));
            }
        }
        return response()->json(['filtered_users' => $users->unique('id')->values()]);
    }
}
