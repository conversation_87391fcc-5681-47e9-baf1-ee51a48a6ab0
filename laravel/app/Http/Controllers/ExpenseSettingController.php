<?php

namespace App\Http\Controllers;

use App\Helpers\LogActivity;
use App\Models\ExpenseSetting;
use Illuminate\Http\Request;

class ExpenseSettingController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $settings = ExpenseSetting::select('id', 'name', 'value')->get();
        LogActivity::addLog();
        return response()->json(['settings' => $settings]);
    }

    public function show($id)
    {
        $model_id = $id;
        $model_type = ExpenseSetting::class;
        LogActivity::addLog($model_id, $model_type);
        return ExpenseSetting::find($id);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $setting = ExpenseSetting::find($id);
        $setting->name = $request->name;
        $setting->key = $request->key;
        $setting->value = $request->value;
        $setting->type = $request->type;
        $setting->save();
        $model_id = $id;
        $model_type = ExpenseSetting::class;

        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
