<?php

namespace App\Http\Controllers;

use App\Distributor;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Http\Controllers\Controller;
use App\Line;
use App\LineDivision;
use App\LineProduct;
use App\LineUser;
use App\Mapping;
use App\Models\Distributors\DistributorLine;
use App\Product;
use App\Productprice;
use App\Sale;
use App\SalesSetting;
use App\SalesTypes;
use App\Services\Enums\Ceiling;
use App\Services\SalesPerDistributorService;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SalesPerDistributorReportController extends ApiController
{

    public function __construct(private SalesPerDistributorService $salesPerDistributorService)
    {
    }

    public function getLines()
    {
        $user = Auth::user();
        [$lines, $types] = $this->salesPerDistributorService->getLines($user);
        return $this->respond(compact('lines', 'types'));
    }

    public function getLineData(Request $request)
    {

        $user = Auth::user();

        return $this->respond(
            $this->salesPerDistributorService->getLineData(
                $user,
                $request->lines
            )
        );
    }

    public function filter(Request $request)
    {
        $user = Auth::user();
        $saleFilter = $request->saleFilter;


        return $this->respond(
            $this->salesPerDistributorService->getSalesAndFields(
                $saleFilter,
                $user
            )
        );
    }

}
