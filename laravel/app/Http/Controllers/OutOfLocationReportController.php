<?php

namespace App\Http\Controllers;

use App\ActualVisit;
use App\Exceptions\CrmException;
use App\Models\AccountTypeDistance;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class OutOfLocationReportController extends ApiController
{
    public function filter(Request $request)
    {
        $visit = $request->visitFilter;
        $from = Carbon::parse($visit['fromDate'])->startOfDay();
        $to = Carbon::parse($visit['toDate'])->endOfDay();
        $visits = [];
        /**@var User $user */
        $user = Auth::user();
        $data = ActualVisit::select(
            'actual_visits.id as id',
            'actual_visits.line_id',
            'actual_visits.plan_id',
            'actual_visits.is_automatic',
            'actual_visits.div_id',
            'actual_visits.user_id',
            'actual_visits.ll',
            'actual_visits.lg',
            'actual_visits.acc_type_id',
            DB::raw("
            CASE
                WHEN crm_actual_visits.is_web_visit = 1 THEN 'Web'
                WHEN crm_actual_visits.is_web_visit = 0 THEN 'App'
                ELSE 'Unknown Status'
            END as visit_from
        "),
            'actual_visits.account_id',
            'actual_visits.created_at as insertion',
            DB::raw('IFNULL(crm_actual_visits.visit_duration,"") as duration'),
            'actual_visits.visit_date as start',
            DB::raw('IFNULL(crm_actual_visits.end_visit_date,"") as end'),
            DB::raw('IFNULL(crm_bricks.name,"") as brick'),
            'employees.fullname as employee',
            DB::raw('IFNULL(crm_employees.emp_code,"") as emp_code'),
            'lines.name as line',
            'line_divisions.name as division',
            'accounts.name as account_name',
            DB::raw('IFNULL(crm_accounts.address,"") as address'),
            'accounts.id as account_id',
            DB::raw('IFNULL(crm_accounts.code,"") as acc_code'),
            'account_types.name as acc_type',
            'account_types.shift_id as acc_shift_id',
            'shifts.name as shift',
            DB::raw("
            CASE
                WHEN crm_actual_visits.visit_status = 1 THEN 'False Time'
                WHEN crm_actual_visits.visit_status = 0 THEN 'Accurate Duration'
                ELSE 'Unknown Status'
            END as duration_status
        "),
            DB::raw('IFNULL(group_concat(distinct crm_managers.fullname),"") as manager'),
            DB::raw('IFNULL(group_concat(distinct crm_products.name),"") as product'),
            DB::raw('IFNULL(group_concat(distinct crm_products.short_name),"") as brief'),
            DB::raw('IFNULL(group_concat(distinct crm_brands.name),"") as brand'),
            DB::raw('IFNULL(group_concat(distinct crm_actual_visit_products.notes),"") as comment'),
            DB::raw('IFNULL(group_concat(distinct crm_actual_visit_products.follow_up),"") as follow'),
            DB::raw('IFNULL(group_concat(distinct crm_actual_visit_products.market_feedback),"") as mf'),
            DB::raw('IFNULL(group_concat(crm_visit_feedbacks.notes),"") as v_feedback'),
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            DB::raw('IFNULL(crm_specialities.name,"") as speciality'),
            'visit_types.name as type',
            DB::raw('IFNULL(crm_double_visit_types.name,"") as sec_type'),
            'specialities.id as speciality_id',
        )
            ->leftJoin('lines', 'actual_visits.line_id', 'lines.id')
            ->leftJoin('users as employees', 'actual_visits.user_id', 'employees.id')
            ->leftJoin('line_divisions', 'actual_visits.div_id', 'line_divisions.id')
            ->leftJoin('accounts', 'actual_visits.account_id', 'accounts.id')
            ->leftJoin('bricks', 'actual_visits.brick_id', 'bricks.id')
            ->leftJoin('account_types', 'actual_visits.acc_type_id', 'account_types.id')
            ->leftJoin('shifts', 'account_types.shift_id', 'shifts.id')
            ->leftJoin('doctors', 'actual_visits.account_dr_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('visit_types', 'actual_visits.visit_type_id', 'visit_types.id')
            ->leftJoin('double_visit_types', 'actual_visits.double_visit_type_id', 'double_visit_types.id')
            ->leftJoin(
                'actual_visits_managers',
                function ($join) {
                    $join->on('actual_visits.id', '=', 'actual_visits_managers.visit_id');
                }
            )
            ->leftJoin('users as managers', 'actual_visits_managers.user_id', 'managers.id')
            ->leftJoin('actual_visit_products', 'actual_visits.id', 'actual_visit_products.visit_id')
            ->leftJoin('visit_feedbacks', 'actual_visit_products.vfeedback_id', 'visit_feedbacks.id')
            ->leftJoin('products', 'actual_visit_products.product_id', 'products.id')
            ->leftJoin('brands', 'actual_visit_products.brand_id', 'brands.id')
            ->where('actual_visits.deleted_at', '=', null)
            ->whereBetween('visit_date', [$from, $to]);
        $data = match ($visit['status']) {
            1 => $data,
            2 => $data->where('plan_visit_details.approval', 1),
            3 => $data->whereNull('plan_visit_details.approval'),
        };
        $data = match ($visit['actualProductData']) {
            1 => $data,
            2 => $data->whereNotNull('actual_visit_products.follow_up'),
            3 => $data->whereNotNull('actual_visit_products.market_feedback'),
        };
        $data = match ($visit['actualData']) {
            1 => $data,
            2 => $data->whereNull('actual_visits.plan_id'),
            3 => $data->whereNotNull('actual_visits.plan_id'),
        };
        if (!empty($visit['divisions'])) {
            $data = $data->whereIntegerInRaw('actual_visits.div_id', $visit['divisions']);
        }
        if (!empty($visit['bricks'])) {
            $data = $data->whereIntegerInRaw('actual_visits.brick_id', $visit['bricks']);
        }
        if (!empty($visit['users'])) {
            $data = $data->whereIntegerInRaw('actual_visits.user_id', $visit['users']);
        }
        if (!empty($visit['lines'])) {
            $data = $data->whereIntegerInRaw('actual_visits.line_id', $visit['lines']);
        }
        if (!empty($visit['types'])) {
            $data = $data->whereIntegerInRaw('actual_visits.acc_type_id', $visit['types']);
        }
        if (!empty($visit['specialities'])) {
            $data = $data->whereIntegerInRaw('specialities.id', $visit['specialities']);
        }
        if (!empty($visit['visit_types'])) {
            $data = $data->whereIntegerInRaw('visit_types.id', $visit['visit_types']);
        }
        if (!empty($visit['another_types'])) {
            $data = $data->whereIntegerInRaw('actual_visits.double_visit_type_id', $visit['another_types']);
        }
        $data = $data->with('account.accountlines')
            ->groupBy(
                "actual_visits.id"
            )
            ->get();
        $accountTypeDistance = AccountTypeDistance::select('type_id', 'distance')->get();
        $unit = "M";
        $visits = collect([]);
        foreach ($data as $visit) {
            $account_ll = $visit->account?->accountlines()
                ->where('line_division_id', $visit->div_id)
                ->first()?->ll;
            $account_lg = $visit->account?->accountlines()
                ->where('line_division_id', $visit->div_id)
                ->first()?->lg;
            $distance = 0;
            $distance = (int)$this->distanceBetweenTwoCoordinates(
                (float)$visit->ll,
                (float)$visit->lg,
                (float)$account_ll,
                (float)$account_lg,
                $unit
            );
            $validDistance = $accountTypeDistance->where('type_id', $visit->acc_type_id)->first()->distance;
            if ($account_ll && $account_lg && $visit->ll != 0 && $visit->lg != 0 && $distance != 0 && $distance > $validDistance) {
                $visits = $visits->push([
                    'id' => $visit->id,
                    'date' => $visit->start,
                    'line' => $visit->line,
                    'division' => $visit->division ?? '',
                    'div_id' => $visit->div_id ?? '',
                    'employee' => $visit->employee ?? '',
                    'emp_code' => $visit->emp_code ?? '',
                    'account' => $visit->account_name,
                    'account_id' => $visit->account_id,
                    'brick' => $visit->brick,
                    'doctor' => $visit->doctor,
                    'doctor_id' => $visit->doctor_id,
                    'speciality' => $visit->speciality,
                    'acc_type' => $visit->acc_type,
                    'comment' => $visit->comment ?? '',
                    'type' => $visit->type,
                    'manager' => $visit->manager,
                    'll' => (float)$visit->ll,
                    'lg' => (float)$visit->lg,
                    'out_distance' =>  $distance . ' m'
                ]);
            }
        }
        if (empty($visits)) {
            throw new Exception('There is no actual visits');
        }
        // throw new CrmException($visits);
        // LogActivity::addLog();
        return response()->json(['visits' => $visits]);
    }

    public function distanceBetweenTwoCoordinates($lat1, $lon1, $lat2, $lon2, $unit)
    {
        if (($lat1 == $lat2) && ($lon1 == $lon2)) {
            return 0;
        } else {
            $theta = $lon1 - $lon2;
            $dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) +  cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
            $dist = acos($dist);
            $dist = rad2deg($dist);
            $miles = $dist * 60 * 1.1515;
            $unit = strtoupper($unit);

            if ($unit == "K") {
                return ($miles * 1.609344);
            } else if ($unit == "N") {
                return ($miles * 0.8684);
            } else if ($unit == "M") {
                return ($miles * 1609.34);
            } else {
                return $miles;
            }
        }
    }
}
