<?php

namespace App\Http\Controllers;

use App\SalesSetting;
use App\Services\CeilingService;
use App\Services\QueueLoadBalancingService;
use Illuminate\Http\Request;

class CeilingReportController extends Controller
{


    public function index(Request $request)
    {
        $productIds = $request->visitFilter["products"];
        $distributorIds = $request->visitFilter["distributors"];
        $from = $request->visitFilter["fromDate"];
        $to = $request->visitFilter["toDate"];

        $data = CeilingService::query($from, $to,
            productIds: $productIds,
            distributorIds: $distributorIds
        );

        $fields = ["id", "name", "pharmacy", "date", "distributor", "number_of_units"];

        return response()->json(["data" => $data, "fields" => $fields]);
    }

    public function recalc(Request $request)
    {
        $productIds = $request->visitFilter["products"];
        $distributorIds = $request->visitFilter["distributors"];
        $from = $request->visitFilter["fromDate"];
        $to = $request->visitFilter["toDate"];

        $setting = SalesSetting::where("key", "sales_ceiling")->value("value");

        if (!$setting) {
            throw new \Exception("Sales Ceiling Setting must be Exist.");
        }

        $balancingService = new QueueLoadBalancingService();

        $data = CeilingService::query($from, $to,
            productIds: $productIds,
            distributorIds: $distributorIds
        );

        match ($setting) {
            "All" => CeilingService::recalcDistributeAll($data, $balancingService),
            "Difference" => CeilingService::recalcDistributeDiff($data, $balancingService),
        };


    }

}
