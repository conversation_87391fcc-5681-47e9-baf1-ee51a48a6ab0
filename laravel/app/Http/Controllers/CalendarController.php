<?php

namespace App\Http\Controllers;

use App\Action;
use App\ActualVisit;
use App\Exceptions\CrmException;
use App\Form;
use App\Helpers\LogActivity;
use App\Line;
use App\LineDivision;
use App\OwActualVisit;
use App\OwPlanVisit;
use App\Permission;
use App\PlanSetting;
use App\PlanVisit;
use App\Position;
use App\PublicHoliday;
use App\Services\ActualService;
use App\Services\PlanService;
use App\User;
use App\UserPosition;
use App\Vacation;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

class CalendarController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(User $user)
    {
        $visits = (new ActualService)->getActuals($user, 'users.id', Carbon::now()->firstOfYear(), Carbon::now()->endOfYear());
        $plans = (new PlanService)->getCalendarPlans($user, 'users.id', Carbon::now()->firstOfYear(), Carbon::now()->addYear(1)->endOfYear());
        // throw new CrmException($plans->where('plan_date','2023-10-02'));
        // $visits = ActualVisit::whereYear('visit_date',Carbon::now()->firstOfYear())->where('user_id', $user->id);
        $calendar = PlanVisit::calendar($plans);
        $calendar = $calendar->toBase()->merge(PublicHoliday::calendar()->toBase());
        $calendar = $calendar->toBase()->merge(Vacation::calendar($user)->toBase());
        $calendar = $calendar->toBase()->merge(ActualVisit::unplannedVisits($user, $visits)->toBase());
        $calendar = $calendar->toBase()->merge(ActualVisit::doubleVisitsCalendar($user, $visits)->toBase());
        $calendar = $calendar->toBase()->merge(OwActualVisit::calendar($user)->toBase());
        $calendar = $calendar->toBase()->merge(OwPlanVisit::calendar($user)->toBase());
        LogActivity::addLog();
        return $this->respond($calendar);
    }

    public function getLines()
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = $user->userLines();
        $withShift = PlanSetting::where('key', 'plan_shift')->value('value') == 'yes' ? 'month' : 'day';
        $users = $user->belowUsersOfAllLinesWithPositions($lines);
        return $this->respond(['lines' => $lines, 'users' => $users, 'withShift' => $withShift]);
    }
    public function getPositions(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $line = Line::find($request->line_id);
        $roles = collect([]);
        if ($user->hasRole('admin') || $user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) {
            $roles = collect([...$line->divisionTypes, ...$line->getLinePositions()])
                ->map(fn($role) => ['id' => $role->id . '_' . get_class($role), 'name' => $role->name]);
            // throw new CrmException($roles);
        } else {
            $divisionTypes = collect([]);
            $divisions = collect([]);
            if ($user->hasDivision($line)) {
                $user->allBelowDivisions($line)->each(function ($division) use ($divisionTypes) {
                    $divisionTypes = $divisionTypes->push($division->divisionType);
                });
                $divisionTypes = $divisionTypes->prepend($user->divisionType($line));
            } else {
                $userPosition = $user->userPosition->first();
                $divisions = $userPosition->divisions->where('line_id', $line->id);
                if (count($divisions) == 0) {
                    $divisions = LineDivision::where('line_id', $line->id)
                        ->where('division_type_id', 1)->whereNull('deleted_at')->get();
                }
                $divisions->values()->each(function ($division) use (&$divisions) {
                    $divisions = $divisions->merge($division?->getBelowDivisions());
                });
                $divisions->unique('id')->values()->each(function ($division) use ($divisionTypes) {
                    $divisionTypes = $divisionTypes->push($division->divisionType);
                });
                $divisionTypes = $divisionTypes->prepend($user->position());
            }
            $roles = collect([...$divisionTypes->unique('name')->values()])
                ->map(fn($role) => ['id' => $role->id . '_' . get_class($role), 'name' => $role->name]);
        }
        return $this->respond($roles);
    }
    public function getUsers(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $line = Line::find($request->line_id);
        $from = $request->from ? Carbon::parse($request->from)->startOfDay() : Carbon::now();
        $to = $request->to ? Carbon::parse($request->to)->startOfDay() : Carbon::now();
        $users = collect([]);
        if ($request->role_type == Position::class) {
            $users = UserPosition::where('position_id', $request->role_id)
                ->where('user_positions.from_date', '<=', $from?->toDateString())
                ->where(
                    fn($q) => $q->where('user_positions.to_date', '>=', $to?->toDateString())
                        ->orWhere('user_positions.to_date', null)
                )
                ->with('user')->get()->pluck('user');
        } else {
            $divisions = $user->userDivisions($line, $from, $to)->prepend($user->divisions($from, $to)->where('line_divisions.line_id', $line->id)->where('is_kol', 0)->first())
                ->where('division_type_id', $request->role_id)->where('is_kol', 0);
            $divisions->each(function ($division) use ($users, $from, $to) {
                $users = $users->push($division->user($from, $to));
            });
        }
        return response()->json(['users' => $users->where('status', '!=', 'Inactive')->filter(fn($user) => $user != null)->unique('id')->values()]);
    }
}
