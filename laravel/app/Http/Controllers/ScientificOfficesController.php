<?php

namespace App\Http\Controllers;

use App\Helpers\LogActivity;
use App\Models\ScientificOffice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ScientificOfficesController extends ApiController
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $offices = ScientificOffice::get();
        LogActivity::addLog();
        return $this->respond($offices);
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $max_sort = ScientificOffice::max('sort');

        if (!$max_sort) {
            $max_sort = 100;
        } else {
            $max_sort += 100;
        }
        ScientificOffice::create([
            'name' => $request->name,
            'sort' => $max_sort,
        ]);
        return $this->respondCreated();
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $type = ScientificOffice::find($id);
        return $this->respond(['type' => $type]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $type = ScientificOffice::findOrFail($id);
        $type->name = $request->name;
        $type->sort = $request->sort;
        $type->save();

        return $this->respondSuccess();
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        ScientificOffice::find($id)->delete();
        return $this->respondSuccess();
    }
}
