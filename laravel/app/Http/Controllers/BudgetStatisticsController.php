<?php

namespace App\Http\Controllers;

use App\DivisionType;
use App\Exceptions\CrmException;
use App\Http\Controllers\Controller;
use App\Line;
use App\Models\BudgetSetting;
use App\Models\CommercialRequest\CommercialRequest;
use App\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BudgetStatisticsController extends ApiController
{
    public function getBudgets()
    {
        /**@var User $user */
        $user = Auth::user();
        $isAdmin = $user->hasRole('admin');
        $data = collect([]);
        // $budgetSettings = BudgetSetting::where('key', 'per_product_or_line')->value('value');
        // $fields = collect(['id', 'product', 'pro_code', 'budget', 'consumed', 'remained']);
        $lines = $user->userLines();
        $products = Product::select(
            'products.id as id',
            'products.ucode as pro_code',
            'products.name as name',
            'lines.name as line',
            'lines.id as line_id',
        )
            ->leftJoin('line_products', 'products.id', 'line_products.product_id')
            ->leftJoin('lines', 'line_products.line_id', 'lines.id')
            ->where('line_products.from_date', '<=', now())
            ->where(fn($q) => $q->where('line_products.to_date', null)->orWhere('line_products.to_date', '>=', now()))
            ->whereIntegerInRaw('line_products.line_id', $lines->pluck('id'))->get();
        // throw new CrmException([$lines, $products]);
        // throw new CrmException($products);
        $products->each(function ($product) use ($data, $user, $isAdmin) {
            $data = $data->push($this->statistics($product, $user, $isAdmin));
        });
        return $this->respond($data);
    }
    private function statistics($product, $user, $isAdmin)
    {
        $setting = BudgetSetting::where('key', 'budget_period')->value('value');
        $line = Line::find($product->line_id);
        $division_type = DivisionType::select('id', 'name', 'last_level')->where('last_level', '=', 1)->value('id');
        $divisions = $user->allBelowDivisions($line)
            ->where('division_type_id', '=', $division_type)->where('is_kol', 0)->unique('id')->pluck('id')->toArray();
        $budgets = $this->budget($line, $divisions, $product, $setting)->sum('total');
        $consumed = $this->commercials($line, $divisions, $product, $user, $isAdmin)->sum('total');
        $data = collect([
            'id' => $product->id,
            'product' => $product->name,
            'pro_code' => $product->pro_code ?? '',
            'budget' => $budgets,
            'consumed' => $consumed,
            'remained' => $budgets - $consumed,
        ]);
        return $data;
    }
    public function budget($line, $divisions, $product, $setting)
    {

        $budgets = DB::table('budgets')->select(
            'budgets.id as id',
            'budgets.from_date as from_date',
            'lines.name as line',
            DB::raw('IFNULL(group_concat(distinct crm_line_divisions.name),"") as division'),
            DB::raw('IFNULL(group_concat(distinct crm_users.name),"") as employee'),
            DB::raw('IFNULL(group_concat(distinct crm_products.name),"") as product'),
            DB::raw('IFNULL(group_concat(distinct crm_products.id),"") as product_id'),
            DB::raw('IFNULL(group_concat(distinct crm_division_types.color),"") as color'),
            DB::raw('IFNULL(crm_budgets.amount,"") as total'),
        )
            ->leftJoin('lines', 'budgets.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'budgets.div_id', 'line_divisions.id')
            ->leftJoin('products', 'budgets.product_id', 'products.id')
            ->leftJoin('line_users_divisions', 'line_divisions.id', 'line_users_divisions.line_division_id')
            ->leftJoin('users', 'line_users_divisions.user_id', 'users.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->orderBy('id', 'DESC')
            ->whereRaw("find_in_set($product->id,crm_products.id)")
            ->whereNull('budgets.deleted_at')
            ->where('budgets.budgetable_type', 'App\RequestType')
            ->where('budgets.type_id', 1);
        // $division_type = DivisionType::select('id', 'name', 'last_level')->where('last_level', '=', 1)->value('id');
        // $divisions = $user->allBelowDivisions($line)
        //     ->where('division_type_id', '=', $division_type)->unique('id')->pluck('id')->toArray();
        $budgets = $budgets->whereIntegerInRaw('line_divisions.id', $divisions);
        if ($setting == 'Year')
            $budgets = $budgets->whereYear('budgets.from_date', Carbon::now()->format('Y'));

        $budgets = $budgets->groupBy("id")->get();
        return $budgets;
    }
    public function commercials($line, $divisions, $product, $user, $isAdmin)
    {
        $commercials = DB::table('commercial_requests')->select(
            'commercial_requests.id as id',
            'users.name as employee',
            DB::raw('IFNULL(crm_products.name,"") as product'),
            DB::raw('IFNULL(crm_products.id,"") as product_id'),
            DB::raw('IFNULL(crm_commercial_products.ratio,"") as ratio'),
            DB::raw('IFNULL(crm_paid_requests.amount,"") as paid'),
            DB::raw('IFNULL(crm_plan_visit_details.approval,"") as status'),
            DB::raw('IFNULL(crm_commercial_requests.amount,"") as amount'),
        )
            ->selectRaw('crm_paid_requests.amount * crm_commercial_products.ratio / 100  as total')
            ->leftJoin('users', 'commercial_requests.user_id', 'users.id')
            ->leftJoin('commercial_lines', 'commercial_requests.id', 'commercial_lines.request_id')
            ->leftJoin('lines', 'commercial_lines.line_id', 'lines.id')
            ->leftJoin('commercial_divisions', 'commercial_requests.id', 'commercial_divisions.request_id')
            ->leftJoin('line_divisions', 'commercial_divisions.div_id', 'line_divisions.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin('commercial_products', 'commercial_requests.id', 'commercial_products.request_id')
            ->leftJoin('products', 'commercial_products.product_id', 'products.id')
            ->leftJoin('commercial_doctor_cost_types', 'commercial_requests.id', 'commercial_doctor_cost_types.request_id')
            ->leftJoin('commercial_user_cost_types', 'commercial_requests.id', 'commercial_user_cost_types.request_id')
            ->leftJoin('doctors', 'commercial_doctor_cost_types.doctor_id', 'doctors.id')
            ->leftJoin('users as employees', 'commercial_user_cost_types.user_id', 'employees.id')
            ->leftJoin('commercial_pharmacies', 'commercial_requests.id', 'commercial_pharmacies.request_id')
            ->leftJoin('accounts', 'commercial_pharmacies.pharmacy_id', 'accounts.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('commercial_requests.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', CommercialRequest::class);
                }
            )
            ->leftJoin(
                'paid_requests',
                function ($join) {
                    $join->on('commercial_requests.id', '=', 'paid_requests.paidable_id');
                    $join->where('paid_requests.paidable_type', CommercialRequest::class);
                }
            )
            ->orderBy('id', 'DESC')
            ->whereNull('commercial_requests.deleted_at')
            ->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null))
            ->where('lines.id', $line->id);
        // if (!$isAdmin) {
        //     $commercials = $commercials->where('users.id', $user->id);
        // }
        $commercials = $commercials->whereIntegerInRaw('line_divisions.id', $divisions)
            ->whereYear('commercial_requests.from_date', Carbon::now()->format('Y'))
            ->where('commercial_products.product_id', $product->id)
            ->groupBy(
                "id",
                "products.id",
                "products.name",
                "commercial_products.ratio",
                "commercial_products.units",
                "paid_requests.amount",
                "plan_visit_details.approval"
            )->get();
        return $commercials;
    }
}
