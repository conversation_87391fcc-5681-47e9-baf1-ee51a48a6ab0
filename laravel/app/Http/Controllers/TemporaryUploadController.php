<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Jobs\ReprocessingTempUploadsJob;
use App\Models\TemporaryUpload;
use Illuminate\Http\Request;

class TemporaryUploadController extends ApiController
{
    public function update(Request $request)
    {
        foreach ($request->temps as $temp) {
            TemporaryUpload::find($temp['temp_id'])->update(
                [
                    'records' => $temp,
                    'status' => TemporaryUpload::PENDING
                ]
            );
        }
        ReprocessingTempUploadsJob::dispatch($request->file_id)->onQueue('high');
        return $this->respond();
    }
}
