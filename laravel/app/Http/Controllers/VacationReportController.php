<?php

namespace App\Http\Controllers;

use App\DivisionType;
use App\Exceptions\CrmException;
use App\Http\Controllers\Controller;
use App\Line;
use App\LineUser;
use App\Models\OffDay;
use App\User;
use App\Vacation;
use App\VacationType;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use League\Glide\Manipulators\Crop;

class VacationReportController extends ApiController
{
    public function getLineData(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = Line::whereIntegerInRaw('id', $request->lines)->get();
        $users = $user->belowUsersOfAllLinesWithPositions($lines);
        return response()->json(['users' => $users]);
    }
    public function filter(Request $request, Line $line)
    {
        /**@var User authUser */
        $user = Auth::user();
        $vacationRequest = $request->vacationFilter;
        $from = Carbon::parse($vacationRequest['fromDate'])->startOfDay();
        $to = Carbon::parse($vacationRequest['toDate'])->endOfDay();
        $month = [Carbon::parse($from)->format('m'), Carbon::parse($to)->format('m')];
        $year = Carbon::parse($from)->format('Y');
        $vacationTypesNames = VacationType::when(
            !empty($vacationRequest['types']),
            fn($q) => $q->whereIntegerInRaw("id", $vacationRequest['types'])
        )->get()->pluck('name');
        $lines = Line::when(
            !empty($vacationRequest['lines']),
            fn($q) => $q->whereIntegerInRaw("id", $vacationRequest['lines'])
        )->get();
        $months = [];
        $period = CarbonPeriod::create($from, '1 month', $to);
        foreach ($period as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $year,
        ];
        $filtered = new Collection([]);
        $data = new Collection([]);
        // $users = LineUser::whereIntegerInRaw('line_id', $vacationRequest['lines'])
        //     ->where('from_date', '<=', (string)Carbon::now())
        //     ->where(fn($q) => $q->where('to_date', '=', null)
        //         ->orWhere('to_date', '>=', (string)Carbon::now()))
        //     ->when(!empty($vacationRequest['users']), fn($q) => $q->whereIn("line_users.user_id", $vacationRequest['users']))
        //     ->with('user')->get()->pluck('user');
        foreach ($lines as $line) {
            $users = $line->users($from, $to)->when(!empty($visit['users']), fn($q) => $q->whereIn("line_users.user_id", $vacationRequest['users']))
                ->get();
            $filtered = $filtered->merge($user->filterUsers($line, $users, $vacationRequest, $from, $to));
        }
        $filtered = $filtered->unique('id')->values()->pluck('id');
        $data  = $this->statistics($filtered, $vacationRequest, $from, $to);

        return response()->json([
            'data' => $data,
            'vacationTypesNames' => $vacationTypesNames,
            'dates' => $dates
        ]);
    }


    private function statistics($users, $request, $from, $to)
    {
        $vacations = Vacation::select(
            'vacations.id',
            DB::raw('group_concat(distinct crm_lines.name) as line'),
            DB::raw('group_concat(distinct crm_division_types.color) as color'),
            'users.fullname as employee',
            DB::raw('IFNULL(crm_users.emp_code,"") as emp_code'),
            // 'users.emp_code as emp_code',
            'vacations.from_date as from',
            'vacations.to_date as to',
            DB::raw("
                    CASE
                        WHEN crm_plan_visit_details.approval = 1 THEN 'Approved'
                        WHEN crm_plan_visit_details.approval = 0 THEN 'Disapproved'
                        ELSE 'Pending'
                    END as status
                "),
            'vacation_types.name as type',
            DB::raw('IFNULL(crm_shifts.name,"Full Day") as shift'),
            // DB::raw('group_concat(distinct crm_plan_visit_details.approval) as status'),
            DB::raw('group_concat(distinct crm_attachments.path) as file'),
            DB::raw('IFNULL(crm_vacations.notes,"") as notes')
        )
            ->leftJoin('users', 'vacations.user_id', '=', 'users.id')
            ->leftJoin('line_users_divisions', function ($join) use ($from, $to) {
                $join->on('users.id', '=', 'line_users_divisions.user_id')
                    ->where('line_users_divisions.deleted_at', null)
                    ->where('line_users_divisions.from_date', '<=', $from->toDateString() ?? Carbon::now())
                    ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)
                        ->orWhere('line_users_divisions.to_date', '>=', $to->toDateString() ?? Carbon::now()));
            })
            ->leftJoin('line_divisions', function ($join) use ($from, $to) {
                $join->on('line_users_divisions.line_division_id', '=', 'line_divisions.id')
                    ->where('line_divisions.deleted_at', null)
                    ->where('line_divisions.from_date', '<=', $from->toDateString() ?? Carbon::now())
                    ->where(fn($q) => $q->where('line_divisions.to_date', '=', null)
                        ->orWhere('line_divisions.to_date', '>=', $to->toDateString() ?? Carbon::now()));
            })
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin('lines', 'line_users_divisions.line_id', '=', 'lines.id')
            ->leftJoin('vacation_types', 'vacations.vacation_type_id', '=', 'vacation_types.id')
            ->leftJoin('shifts', 'vacations.shift_id', '=', 'shifts.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('vacations.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', Vacation::class);
                }
            )
            ->leftJoin(
                'attachments',
                function ($join) {
                    $join->on('vacations.id', '=', 'attachments.attachable_id');
                    $join->where('attachments.attachable_type', Vacation::class);
                }
            )
            // ->where(function ($query) use ($from, $to) {
            //     $query->whereBetween('vacations.from_date', [$from, $to])
            //         ->orWhereBetween('vacations.to_date', [$from, $to]);
            // })
            // ->whereBetween(DB::raw("(DATE_FORMAT(crm_vacations.from_date,'%m'))"), $month)
            // ->whereYear('vacations.from_date', $year)->whereYear('vacations.to_date', $year)
            ->where(function ($query) use ($from, $to) {
                $query->whereBetween("vacations.from_date", [$from, $to])
                    ->orWhereBetween("vacations.to_date", [$from, $to])
                    ->orWhere(function ($query) use ($from, $to) {
                        $query
                            ->where("vacations.from_date", "<=", $from)
                            ->where("vacations.to_date", ">=", $to);
                    });
            })
            ->whereIntegerInRaw('vacations.user_id', $users);
        if (!empty($request['types'])) {
            $vacations = $vacations->whereIntegerInRaw('vacations.vacation_type_id', $request['types']);
        }

        $vacations = match ($request['approval']) {
            1 => $vacations->where('plan_visit_details.approval', 1),
            2 => $vacations->where('plan_visit_details.approval', 0),
            3 => $vacations,
            null => $vacations
        };
        $vacations = $vacations->groupBy('vacations.id', 'plan_visit_details.approval')->get();

        $vacations = $vacations->filter(function ($vacation) use ($from, $to) {
            $period = CarbonPeriod::create($vacation->from, $vacation->to);
            foreach ($period as $date) {
                if ($date->between($from, $to)) {
                    if (
                        !OffDay::isOffDay($vacation->from, $vacation->to, $date, null)
                    ) {
                        return [$vacation];
                    }
                }
            }
        });
        return $vacations->values();
    }
    public function downloadExcel(Request $request)
    {
        $vacations = $request->vacations;
        $results = collect([]);
        collect($vacations)->each(function ($vacation) use ($results) {
            $data = collect([
                'id' => $vacation['id'],
                'line' => $vacation['line'],
                'employee' => $vacation['employee'],
                'color' => $vacation['color'],
                'code' => $vacation['emp_code'],
                'from' => $vacation['from'],
                'to' => $vacation['to'],
                'type' => $vacation['type'],
                'shift' => $vacation['shift'],
                'status' => $vacation['status'],
                'notes' => $vacation['notes'],
            ]);
            $this->vacationFlow($vacation['id'])->each(function ($approval, $index) use ($data) {
                $data->put('approve by ' . $index + 1, $approval['approved_by']);
                $data->put('status ' . $index + 1, $approval['status']);
                $data->put('date ' . $index + 1, $approval['date']);
                $data->put('reason ' . $index + 1, $approval['reason']);
            });
            $results = $results->push($data);
        });
        return $this->respond($results);
    }
    // private function getStatus($status)
    // {
    //     if ($status === null) return 'pending';
    //     if ($status === 1) return 'Approved';
    //     if ($status === 0) return 'Disapproved';
    // }
    public function vacationFlow($id)
    {
        $vacation = Vacation::find($id);
        $approvalFlow = $vacation->details?->approvalFlows()->count();
        $data = collect([]);
        if ($approvalFlow > 0) {
            $data = $data->merge($vacation->details?->approvalFlows()->get());
        } else {
            $data = $data->push($vacation->details);
        }
        $data = $data->map(function ($detail) use ($vacation) {
            return [
                'approved_by' => $detail?->user?->name ?? '',
                'position' => $detail?->user?->menuroles ?? '',
                'status' => $detail?->status == null ? 'Pending' : ($detail?->status == 1 ? 'Approved' : 'Disapproved'),
                'reason' => $detail?->approval == 0 ? $vacation->reasons->first()?->reason : "",
                'date' => isNullable($detail?->approval) ? '' : Carbon::parse($detail?->updated_at)->toDateString(),
            ];
        });
        return $data;
    }
}
