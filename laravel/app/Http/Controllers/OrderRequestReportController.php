<?php

namespace App\Http\Controllers;

use App\DivisionType;
use App\Exceptions\CrmException;
use App\LineBricks;
use App\Models\OrderRequest;
use App\Services\Enums\OrderPayment;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class OrderRequestReportController extends ApiController
{
    public function getBricks()
    {
        /**@var User */
        $user = Auth::user();
        $lines = $user->userLines();
        $division_type = DivisionType::where('last_level', 1)->value('id');
        $divisions = collect([]);
        foreach ($lines as $line) {
            $divisions = $divisions->push($user->userDivisions($line)->where('is_kol', 0)->where('division_type_id', $division_type));
        }
        $divisions = $divisions->collapse()->pluck('id')->unique()->toArray();
        $bricks = LineBricks::whereIntegerInRaw('line_division_id', $divisions)->where('from_date', '<=', (string)Carbon::now())
            ->where(fn($q) => $q->where('to_date', '=', null)->orWhere('to_date', '>=', (string)Carbon::now()))->with('brick')->get()->pluck('brick')->unique('id')->values();
        return $this->respond($bricks);
    }

    public function filter(Request $request)
    {
        $orderFilter = $request->orderFilter;
        $from = Carbon::parse($orderFilter['fromDate'])->startOfDay();
        $to = Carbon::parse($orderFilter['toDate'])->endOfDay();
        $fields = ['s', 'brick', 'employee', 'emp_code', 'account', 'date', 'product', 'action', 'path'];
        $data = DB::table('order_requests')->select(
            'order_requests.id as id',
            'order_requests.payment as payment',
            'order_requests.path as path',
            'actual_visits.id as visit_id',
            'lines.name as line',
            'line_divisions.name as division',
            'bricks.name as brick',
            'users.fullname as employee',
            'users.emp_code as emp_code',
            'accounts.name as account',
            'actual_visits.visit_date as date',
            DB::raw('group_concat(distinct CONCAT(crm_products.name," (", crm_order_request_details.quantity ,")")) AS product'),
        )
            ->leftJoin('order_request_details', 'order_requests.id', 'order_request_details.order_request_id')
            ->leftJoin('products', 'order_request_details.product_id', 'products.id')
            ->leftJoin('actual_visits', 'order_requests.visit_id', 'actual_visits.id')
            ->leftJoin('lines', 'actual_visits.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'actual_visits.div_id', 'line_divisions.id')
            ->leftJoin('bricks', 'actual_visits.brick_id', 'bricks.id')
            ->leftJoin('accounts', 'actual_visits.account_id', 'accounts.id')
            ->leftJoin('users', 'order_requests.user_id', 'users.id')
            ->whereIntegerInRaw('bricks.id', $orderFilter['bricks'])
            ->where('order_request_details.quantity', '>', 0)
            ->where('order_requests.payment', 'Pending')
            ->whereBetween('actual_visits.visit_date', [$from, $to])
            ->groupBy('id')->get();
        return response()->json([
            'data' => $data,
            'fields' => $fields
        ]);
    }
    public function save(Request $request)
    {
        // throw new CrmException($request->all());
        $orders = $request->items;
        foreach ($orders as $order) {
            OrderRequest::where('id', $order['id'])->update([
                'path' => $order['path'],
                'url' => Storage::url($order['path']),
                'payment' => $order['payment'],
                'user_id' => Auth::user()->id,
            ]);
        }
        return $this->respondSuccess();
    }
}
