<?php

namespace App\Http\Controllers;

use App\ActualVisit;
use App\Exceptions\CrmException;
use App\LineDivision;
use App\Models\CheckLocation;
use App\Models\Expenses\Expense;
use App\Models\Expenses\ExpenseDetails;
use App\Models\Expenses\ExpenseDivision;
use App\Models\Expenses\KilometersAverage;
use App\Models\Expenses\PerLocation\ExpensePriceFactor;
use App\Models\Expenses\Types\ExpenseType;
use App\Models\ExpenseSetting;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AutomaticExpenseController extends ApiController
{
    public function store(Request $request)
    {
        $lastLocation = $request->location;
        /**@var USer */
        $user = Auth::user();
        $centerPoint = $user->divisions()->whereNotNull('ll')->whereNotNull('lg')->first();
        $automatic_expense_settings = ExpenseSetting::where('key', 'use_automatic_expense')->value('value');
        $automatic_expense = ExpenseType::where('automatic_expense', 1)->first();
        $kilometersAverage = KilometersAverage::value('kilo_meter');
        $calc = KilometersAverage::value('calculation');
        $factorPrice = ExpensePriceFactor::where('from_date', '<=', now())
            ->where(fn ($q) => $q->whereNull('to_date')->orWhere('to_date', '>', (string) Carbon::now()))->first();
        if ($automatic_expense_settings == 'Yes' && $kilometersAverage && $factorPrice) {
            if ($lastLocation) {
                if ($centerPoint->ll != 0 && $centerPoint->lg != 0 && $lastLocation['checkout_date'] === null) {
                    $location = CheckLocation::find($lastLocation['id']);
                    $location->update([
                        'checkout_date' => now(),
                        'checkout_ll' => $request->position['lat'],
                        'checkout_lg' => $request->position['lng'],
                    ]);
                    $message = $this->saveAutomaticExpense($location, $automatic_expense, $kilometersAverage, $factorPrice, $calc);
                    return response()->json(["message" => $message, "status" => "Checkout Created Successfully"]);
                } else if ($centerPoint->ll != 0 && $centerPoint->lg != 0 && $lastLocation['checkout_date'] !== null) {
                    CheckLocation::create([
                        'user_id' => Auth::id(),
                        'checkin_date' => now(),
                        'checkin_ll' => $centerPoint->ll,
                        'checkin_lg' => $centerPoint->lg,
                        'visited_division_id' => $request->division,
                        'center_division_id' => $centerPoint->id,
                    ]);
                    return response()->json(["status" => "Checkin Created Successfully"]);
                } else throw new Exception('Press another time to save your location');
            } else {
                if ($centerPoint->ll != 0 && $centerPoint->lg != 0) {
                    CheckLocation::create([
                        'user_id' => Auth::id(),
                        'checkin_date' => now(),
                        'checkin_ll' => $centerPoint->ll,
                        'checkin_lg' => $centerPoint->lg,
                        'visited_division_id' => $request->division,
                        'center_division_id' => $centerPoint->id,
                    ]);
                    return response()->json(["status" => "Checkin Created Successfully"]);
                } else throw new Exception('Press another time to save your location');
            }
        } else {
            throw new Exception('Please Check Expense Setting');
        }
    }


    public function saveAutomaticExpense($location, $automatic_expense, $kilometersAverage, $factorPrice, $calc)
    {
        $positions = collect([]);
        $positions = $positions->push([
            'll' => $location['checkin_ll'],
            'lg' => $location['checkin_lg'],
        ]);
        $actualVisits = ActualVisit::select('ll', 'lg', 'id', 'line_id', 'div_id', 'user_id')
            ->where('user_id', Auth::id())
            ->whereBetween('created_at', [$location['checkin_date'], $location['checkout_date']])
            ->get();
        $positions = $positions->merge($actualVisits);
        $positions = $positions->push([
            'll' => $location['checkout_ll'],
            'lg' => $location['checkout_lg'],
        ]);
        $positions = $positions->values();
        $dist = $this->routeDistance($positions);
        $message = '';
        if (count($actualVisits) > 0 && $dist > $kilometersAverage) {
            DB::transaction(function () use ($actualVisits, $dist, $automatic_expense, $factorPrice, $kilometersAverage, $calc) {
                $finalDistance = $dist - $kilometersAverage;
                $expense = Expense::create([
                    'user_id' => $actualVisits[0]->user_id,
                    'line_id' => $actualVisits[0]->line_id,
                    'date' => Carbon::now()->toDateString(),
                ]);
                ExpenseDetails::create([
                    'expense_id' => $expense->id,
                    'date' => Carbon::now(),
                    'type_id' => $automatic_expense->id,
                    'distance' => (float) $dist,
                    'amount' => $calc == 'After'
                        ? round(($finalDistance * $factorPrice->price) / $factorPrice->kilo_meter, 2)
                        : round(($dist * $factorPrice->price) / $factorPrice->kilo_meter, 2),
                    'description' => 'Automatic Expense For '
                        . Auth::user()->name . ' at '
                        . Carbon::parse($actualVisits[0]->created_at)->format('Y-m-d'),
                ]);
                $totalAmount = $expense->details->sum('amount');
                $expense->update(['amount' => $totalAmount]);
                $actualVisits->pluck('div_id')->unique('id')->values()->each(function ($division) use ($expense) {
                    ExpenseDivision::create([
                        'expense_id' => $expense->id,
                        'div_id' => $division
                    ]);
                });
            });
        } else {
            $message = 'Distance of your Visits less than ' . $kilometersAverage . ' kilo meters';
        }
        return $message;
    }

    private function routeDistance($positions, $unit = "K")
    {
        $length = $positions->count();
        $dist = [];
        Log::info("center: ");
        Log::info($positions[0]);
        for ($i = 0; $i < $length - 2; $i++) {
            Log::info("visit" . $i . ": ");
            Log::info($positions[$i + 1]);
            $dist[] = $this->distanceBetweenTwoCoordinates($positions[0], $positions[$i + 1], $unit);
        }
        Log::info("Distance Array: ");
        Log::info($dist);
        if (!empty($dist) > 0) {
            return max($dist);
        } else {
            return 0;
        }
    }

    function distanceBetweenTwoCoordinates($p1, $p2, $unit)
    {
        $lat1 = (float)$p1['ll'];
        $lon1 = (float)$p1['lg'];
        $lat2 = (float)$p2['ll'];
        $lon2 = (float)$p2['lg'];
        if (($lat1 == $lat2) && ($lon1 == $lon2)) {
            return 0;
        } else {
            $theta = $lon1 - $lon2;
            $dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) +  cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
            $dist = acos($dist);
            $dist = rad2deg($dist);
            $miles = $dist * 60 * 1.1515;
            $unit = strtoupper($unit);

            if ($unit == "K") {
                return ($miles * 1.609344 * 1.2);
            } else if ($unit == "N") {
                return ($miles * 0.8684);
            } else {
                return $miles;
            }
        }
    }
}
