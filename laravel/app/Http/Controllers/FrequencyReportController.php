<?php

namespace App\Http\Controllers;

use App\Account;
use App\AccountType;
use App\ActualVisit;
use App\ClassFrequency;
use App\DivisionType;
use App\Doctor;
use App\DoctorFrequency;
use App\Exceptions\CrmException;
use App\Line;
use App\LineDivision;
use App\LineDivisionUser;
use App\Models\Kpi;
use App\Models\ListType;
use App\Models\OtherSetting;
use App\Models\PostVisitKpi;
use App\Models\SpecialityClassFrequency;
use App\Services\DoctorService;
use App\Services\Enums\KPITypes;
use App\Services\PostVisitKpisService;
use App\Shift;
use App\SpecialityFrequency;
use App\User;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

use function PHPUnit\Framework\isEmpty;

class FrequencyReportController extends ApiController
{
    public function __construct(private readonly PostVisitKpisService $postVisitKpisService) {}
    private const CLASS_FREQUENCY = 1;
    private const DOCTOR_FREQUENCY = 2;
    private const SPECILITY_FREQUENCY = 3;
    private const SPECILITY_CLASS_FREQUENCY = 4;
    public function lines()
    {
        /**@var User $user */
        $user = Auth::user();
        $frequencyValue = OtherSetting::where('key', 'type_of_frequency')->value('value');
        if ($frequencyValue == 1) $freqTypes = [['id' => 1, 'name' => 'Class']];
        if ($frequencyValue == 2) $freqTypes = [['id' => 2, 'name' => 'Doctor']];
        if ($frequencyValue == 3) $freqTypes = [['id' => 3, 'name' => 'Speciality']];
        if ($frequencyValue == 4) $freqTypes = [['id' => 4, 'name' => 'Speciality Class']];
        $lines = $user->userLines();
        $shifts = Shift::select('id', 'name')->get();
        $account_types = AccountType::select('id', 'name')->orderBy('sort', 'ASC')->get();
        // $lines = Line::userLines(Auth::user());
        return $this->respond(compact('lines', 'freqTypes', 'account_types', 'shifts'));
    }
    public function getLineData(Line $line)
    {
        /**@var User $user */
        $user = Auth::user();
        $divisions = $user->userDivisions($line);
        $users = $user->belowUsersWithPositions($line);

        $specialities = $line->specialities;
        return $this->respond(compact('users', 'divisions', 'specialities'));
    }

    public function getFrequencySubQuery($list, $month, $year)
    {
        // throw new CrmException($month);
        return match ($list['freqType']) {
            self::CLASS_FREQUENCY => ClassFrequency::query()
                ->selectRaw('SUM(crm_class_frequencies.frequency) as frequency')
                ->where('class_frequencies.line_id', $list['line'])
                ->whereColumn('class_frequencies.class_id', 'doctors.class_id')
                ->whereBetween(DB::raw("(DATE_FORMAT(crm_class_frequencies.date,'%m'))"), $month)
                ->whereYear('class_frequencies.date', $year)
                ->groupBy('class_frequencies.class_id'),
            self::DOCTOR_FREQUENCY => DoctorFrequency::query()
                ->selectRaw('SUM(crm_doctor_frequencies.frequency) as frequency')
                ->where('doctor_frequencies.line_id', $list['line'])
                ->whereColumn('doctor_frequencies.doctor_id', 'doctors.id')
                ->whereRaw("YEAR(crm_doctor_frequencies.date) = ?", [$year])  // Using direct YEAR filter
                ->whereRaw("MONTH(crm_doctor_frequencies.date) BETWEEN ? AND ?", [$month[0], $month[1]]) // Filtering on indexed date column
                ->groupBy('doctor_frequencies.doctor_id'),
            self::SPECILITY_FREQUENCY => SpecialityFrequency::query()
                ->selectRaw('SUM(crm_speciality_frequencies.frequency) as frequency')
                ->where('speciality_frequencies.line_id', $list['line'])
                ->whereColumn('speciality_frequencies.specilaity_id', 'doctors.speciality_id')
                ->whereBetween(DB::raw("(DATE_FORMAT(crm_speciality_frequencies.date,'%m'))"), $month)
                ->whereYear('speciality_frequencies.date', $year)
                ->groupBy('speciality_frequencies.speciality_id'),
            self::SPECILITY_CLASS_FREQUENCY => SpecialityClassFrequency::query()
                ->selectRaw('SUM(crm_speciality_class_frequencies.frequency) as frequency')
                ->where('speciality_class_frequencies.line_id', $list['line'])
                ->whereColumn('speciality_class_frequencies.speciality_id', 'doctors.speciality_id')
                ->whereColumn('speciality_class_frequencies.class_id', 'doctors.class_id')
                ->whereBetween(DB::raw("(DATE_FORMAT(crm_speciality_class_frequencies.date,'%m'))"), $month)
                ->whereYear('speciality_class_frequencies.date', $year)
                ->groupBy('speciality_class_frequencies.speciality_id', 'speciality_class_frequencies.class_id'),
        };
    }
    private function getActualsSubQuery($list, $month, $year, $object, $table)
    {
        return ActualVisit::query()
            ->selectRaw('COUNT(crm_actual_visits.id) as actuals')
            ->where('actual_visits.line_id', $list['line'])
            ->where($table, $object->id)
            ->whereColumn('actual_visits.account_dr_id', 'doctors.id')
            ->whereBetween(DB::raw("(DATE_FORMAT(crm_actual_visits.visit_date,'%m'))"), $month)
            ->whereYear('actual_visits.visit_date', $year);
    }
    private function doctors($list, $object, $table, $filter, $line, $month, $year, $from, $to, $shifts)
    {
        $divisions = [];
        $division_type = DivisionType::where('last_level', '=', 1)->first()->id;
        if ($filter == 1) {
            if ($object->is_kol) {
                $divisions = [$object->id];
            } else {
                $divisions = $object->getBelowDivisions()->where('is_kol', 0)
                    ->where('division_type_id', '=', $division_type)->unique('id')->pluck('id')->toArray();
            }
        }
        if ($filter == 2) {
            $divisions = $object?->allBelowDivisions($line, $from, $to)->where('is_kol', 0)
                ->where('division_type_id', '=', $division_type)->unique('id')->pluck('id')->toArray();
        }
        // throw new CrmException($line);
        // $setting = ListType::first()->type == 'Default List' ? true : false;
        // $doctors = (new DoctorService)->getDoctorsِCoverage(
        //     lines: [$line->id],
        //     divisions: $divisions,
        //     from: $from,
        //     to: $to,
        //     specialities: $list['specialities'],
        //     accountTypes: $list['types'],
        //     shifts: $shifts
        // );
        $accountLinesSubquery = DB::table('account_lines')
            ->select('account_lines.id', 'account_lines.account_id')
            ->where('account_lines.line_id', $list['line'])
            ->whereNull('account_lines.deleted_at')
            ->where('account_lines.from_date', '<=', $from->toDateString())
            ->where(fn($q) => $q->whereNull('account_lines.to_date')
                ->orWhere('account_lines.to_date', '>=', $to->toDateString()))
            ->whereIntegerInRaw('account_lines.line_division_id', $divisions);
        $setting = ListType::first()->type == 'Default List';
        $doctors = Account::select(
            [
                'doctors.id as doctor_id',
                'doctors.id as id',
                'doctors.name as doctor',
                'doctors.ucode as ucode',
                DB::raw('IFNULL(group_concat(distinct crm_d.name),"") as class'),
                DB::raw('IFNULL(group_concat(distinct crm_d.id),"") as doc_class_id'),
                'specialities.name as speciality',
                'specialities.id as speciality_id',
                "frequency" => fn($query) => $query->selectSub(
                    $this->getFrequencySubQuery($list, $month, $year),
                    "frequency"
                ),
                "actuals" => fn($query) => $query->selectSub(
                    $this->getActualsSubQuery($list, $month, $year, $object, $table),
                    "actuals"
                ),
            ]
        )
            ->where('accounts.active_date', '<=', Carbon::now())
            ->where(fn($q) => $q->where('accounts.inactive_date', '=', null)->orWhere('accounts.inactive_date', '>=', (string)Carbon::now()))
            ->join('account_types', function ($join) use ($list, $shifts) {
                $join->on('accounts.type_id', 'account_types.id');
                if (!empty($list['types'])) {
                    $join->whereIntegerInRaw('account_types.id', $list['types']);
                }
                if (!empty($shifts)) {
                    $join->whereIntegerInRaw("account_types.shift_id", $shifts);
                }
            })
            ->joinSub($accountLinesSubquery, 'filtered_account_lines', function ($join) {
                $join->on('accounts.id', '=', 'filtered_account_lines.account_id');
            });
        if (!$setting) {
            $doctors = $doctors
                ->join(
                    'new_account_doctors',
                    function ($join) use ($list, $to, $from) {
                        $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                        $join->on('filtered_account_lines.id', '=', 'new_account_doctors.account_lines_id')
                            ->where('new_account_doctors.line_id', $list['line'])
                            ->whereNull('new_account_doctors.deleted_at')
                            ->where('new_account_doctors.from_date', '<=', $from->toDateString())
                            ->where(fn($q) => $q->whereNull('new_account_doctors.to_date')
                                ->orWhere('new_account_doctors.to_date', '>=', $to->toDateString()));
                    }
                );
        } else {
            $doctors = $doctors
                ->join('new_account_doctors', function ($join) use ($list, $from, $to) {
                    $join->on('accounts.id', 'new_account_doctors.account_id')
                        ->where('new_account_doctors.line_id', $list['line'])
                        ->whereNull('new_account_doctors.deleted_at')
                        ->where('new_account_doctors.from_date', '<=', $from->toDateString())
                        ->where(fn($q) => $q->whereNull('new_account_doctors.to_date')
                            ->orWhere('new_account_doctors.to_date', '>=', $to->toDateString()));
                });
        }
        $doctors = $doctors->join('doctors', function ($join) use ($list) {
            $join->on('new_account_doctors.doctor_id', 'doctors.id')
                ->whereNull('doctors.deleted_at');
            if (!empty($list['specialities'])) {
                $join->whereIntegerInRaw('doctors.speciality_id', $list['specialities']);
            }
        })
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('classes as d', 'doctors.class_id', 'd.id')
            ->where('doctors.active_date', '<=', Carbon::now())
            ->where(fn($q) => $q->where('doctors.inactive_date', '=', null)->orWhere('doctors.inactive_date', '>=', (string)Carbon::now()));

        $doctors = $doctors->groupBy("doctors.id", "specialities.id");
        // throw new CrmException($doctors->get());
        return $doctors->get();
        // return $doctors;
    }


    private function frequencyType($doctor, $list, $line, $month, $year)
    {
        if ($list['freqType'] == 1) {
            $frequencies = ClassFrequency::select(['id', 'line_id', 'class_id', 'class_frequencies.frequency as frequency', 'class_frequencies.date as date'])
                ->where('class_frequencies.line_id', $line['id'])
                ->where('class_frequencies.class_id', $doctor['doc_class_id'])
                ->whereBetween(DB::raw("(DATE_FORMAT(crm_class_frequencies.date,'%m'))"), $month)
                ->whereYear('class_frequencies.date', $year)->groupBy('id', 'class_frequencies.frequency')->get();
        }
        if ($list['freqType'] == 2) {
            $frequencies = DoctorFrequency::select(['id', 'line_id', 'doctor_id', 'account_id', 'date', 'doctor_frequencies.frequency as frequency', 'doctor_frequencies.date as date'])
                ->where('doctor_frequencies.line_id', $line['id'])
                ->where('doctor_frequencies.doctor_id', $doctor['id'])
                ->where('doctor_frequencies.account_id', $doctor['account_id'])
                ->whereBetween(DB::raw("(DATE_FORMAT(crm_doctor_frequencies.date,'%m'))"), $month)
                ->whereYear('doctor_frequencies.date', $year)->groupBy('id', 'doctor_frequencies.frequency')->get();
        }
        if ($list['freqType'] == 3) {
            $frequencies = SpecialityFrequency::select(['id', 'line_id', 'speciality_id', 'speciality_frequencies.frequency as frequency', 'speciality_frequencies.date as date'])
                ->where('speciality_frequencies.line_id', $line->id)
                ->where('speciality_frequencies.speciality_id', $doctor['speciality_id'])
                ->whereBetween(DB::raw("(DATE_FORMAT(crm_speciality_frequencies.date,'%m'))"), $month)
                ->whereYear('speciality_frequencies.date', $year)->groupBy('id', 'speciality_frequencies.frequency')->get();
        }
        if ($list['freqType'] == 4) {
            $frequencies = SpecialityClassFrequency::select(['speciality_class_frequencies.id', 'speciality_class_frequencies.line_id', 'speciality_class_frequencies.speciality_id', 'speciality_class_frequencies.frequency as frequency', 'speciality_class_frequencies.date as date'])
                ->where('speciality_class_frequencies.line_id', $line->id)
                ->where('speciality_class_frequencies.speciality_id', $doctor['speciality_id'])
                ->where('speciality_class_frequencies.class_id', $doctor['doc_class_id'])
                ->whereBetween(DB::raw("(DATE_FORMAT(crm_speciality_class_frequencies.date,'%m'))"), $month)
                ->whereYear('speciality_class_frequencies.date', $year)->groupBy('id', 'speciality_class_frequencies.frequency')->get();
        }
        return $frequencies;
    }

    private function actuals($list, $object, $table, $line, $doctors, $month, $year)
    {

        $data = ActualVisit::select([
            'actual_visits.id as id',
            //     'actual_visits.visit_date',
            'actual_visits.account_dr_id',
            //     DB::raw("DATE_FORMAT(crm_actual_visits.created_at,'%Y-%m-%d %H:%s:%i') as date"),
            //     'specialities.id as speciality_id',
            //     'bricks.name as brick',
            //     'lines.name as line',
            //     'line_divisions.name as division',
            //     'users.fullname as user',
            //     'accounts.name as account',
            //     'accounts.id as account_id',
            //     'account_types.name as acc_type',
            // DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            // DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            //     DB::raw('IFNULL(group_concat(distinct crm_products.name),"") as product'),
            //     'specialities.name as speciality',
            //     'visit_types.name as type',
            //     'plan_visit_details.approval as status'
        ])
            // ->leftJoin('lines', 'actual_visits.line_id', 'lines.id')
            // ->leftJoin('line_divisions', 'actual_visits.div_id', 'line_divisions.id')
            // ->leftJoin('users', 'actual_visits.user_id', 'users.id')
            // ->leftJoin('accounts', 'actual_visits.account_id', 'accounts.id')
            // ->leftJoin('bricks', 'actual_visits.brick_id', 'bricks.id')
            // ->leftJoin('account_types', 'actual_visits.acc_type_id', 'account_types.id')
            ->leftJoin('doctors', 'actual_visits.account_dr_id', 'doctors.id')
            // ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            // ->leftJoin('visit_types', 'actual_visits.visit_type_id', 'visit_types.id')
            // ->leftJoin(
            //     'plan_visit_details',
            //     function ($join) {
            //         $join->on('actual_visits.id', '=', 'plan_visit_details.visitable_id');
            //         $join->where('plan_visit_details.visitable_type', 'App\ActualVisit');
            //     }
            // )
            // ->leftJoin('actual_visit_products', 'actual_visits.id', 'actual_visit_products.visit_id')
            // ->leftJoin('products', 'actual_visit_products.product_id', 'products.id')
            ->whereNull('actual_visits.deleted_at')
            ->where('actual_visits.line_id', $line->id)
            ->whereIntegerInRaw('actual_visits.account_dr_id', $doctors)
            // ->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null))
            ->where($table, $object->id)
            ->whereBetween(DB::raw("(DATE_FORMAT(crm_actual_visits.visit_date,'%m'))"), $month)->whereYear('visit_date', $year);
        if (!empty($list['types'])) {
            $data = $data->whereIntegerInRaw('actual_visits.acc_type_id', $list['types']);
        }
        if (!empty($list['specialities'])) {
            $data = $data->whereIntegerInRaw('doctors.speciality_id', $list['specialities']);
        }
        $data = $data->groupBy('id')->get();
        return $data;
    }


    public function filter(Request $request)
    {
        $list = $request->listFilter;
        $from = Carbon::parse($list['fromDate'])->startOfDay();
        $to = Carbon::parse($list['toDate'])->endOfDay();
        $shifts = Shift::when(!empty($list['shifts']), fn($q) => $q->whereIntegerInRaw("shifts.id", $list['shifts']))
            ->get()->pluck('id')->toArray();
        // $month = Carbon::parse($list['date'])->format('m');
        $month = [Carbon::parse($from)->format('m'), Carbon::parse($to)->format('m')];
        $year = Carbon::parse($from)->format('Y');
        $line = Line::find($list['line']);
        /**@var User $user */
        $user = Auth::user();
        $data = collect([]);
        $division_type = DivisionType::where('last_level', '=', 1)->value('id');
        $filtered = new Collection([]);
        $data = new Collection([]);
        if ($list['filter'] == 1) {
            $divisions = $line->divisions($from, $to)
                ->when(!empty($list['divisions']), fn($q) => $q->whereIn("line_divisions.id", $list['divisions']))->get();
            $filtered = $filtered->merge($user->filterDivisions($line, $divisions, $list, $from, $to));
        }
        if ($list['filter'] == 2) {
            $users = $line->users($from, $to)
                ->when(!empty($list['users']), fn($q) => $q->whereIn("line_users.user_id", $list['users']))->get();
            $filtered = $filtered->merge($user->filterUsers($line, $users, $list, $from, $to));
        }
        $filtered->each(function ($object) use ($line, $month, $year, $data, $list, $division_type, $from, $to, $shifts) {
            $data = $data->push($this->statistics($line, $object, $list, $list['filter'], $month, $year, $division_type, $from, $to, $shifts));
        });
        $fields = collect([]);
        if ($list['filter'] == 1) {
            $fields = collect([
                "line",
                "division",
                "employee",
                "emp_code",
                "num_doctors",
                "meet",
                "below",
                "above",
                "ratio(meet)",
                "ratio(meet + above)",
                "target_v",
                "actual_v",
                "meet_actual_v",
                "achieve",
            ]);
        } else {
            $fields = collect([
                "line",
                "employee",
                "emp_code",
                "num_doctors",
                "meet",
                "below",
                "above",
                "ratio(meet)",
                "ratio(meet + above)",
                "target_v",
                "actual_v",
                "meet_actual_v",
                "achieve",
            ]);
        }
        $clickable_fields = collect([
            "num_doctors",
            "meet",
            "below",
            "above",
            "total_frequencies",
            "actual_v",
        ]);
        $months = [];
        $period = CarbonPeriod::create($from, '1 month', $to);
        foreach ($period as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $year,
        ];
        return response()->json([
            'data' => $data->unique("id")->values(),
            'fields' => $fields,
            'dates' => $dates,
            'clickable_fields' => $clickable_fields
        ]);
    }

    private function statistics($line, $object, $list, $filter, $month, $year, $division_type, $from, $to, $shifts)
    {
        $below = collect([]);
        $divisions = [];
        if ($list['filter'] == 2) {
            // $below = $object->belowUsersWithPositions($line);
            $divisions = $object->allBelowDivisions($line, $from, $to)->where('is_kol', 0)
                ->where('division_type_id', $division_type)->unique('id')->pluck('id')->toArray();
            collect($divisions)->each(function ($division) use ($below, $line, $from, $to) {
                $div = LineDivision::find($division);
                $below = $below->push($div->users($from, $to)->where('line_id', $line->id)->get());
            });
            $below = $below->collapse()->unique('id')->values();
        } else {
            if ($object->is_kol) {
                $below = collect([$object]);
            } else {
                $below = $object->getBelowDivisions($from, $to)->where('is_kol', 0)
                    ->where('division_type_id', $division_type)->unique('id')->values();
            }
        }
        // $doctors = $this->doctors($list, $object, $filter, $line, $month, $year);
        // $doctorIds = $doctors->pluck('id');
        // $actuals = $this->actuals($list, $object, $filter == 1 ? 'line_divisions.id' : 'users.id', $line, $doctorIds, $month, $year);
        // $freq = $this->frequencies($doctors, $actuals, $list, $line, $month, $year);
        $freq = $this->frequencies($below, $filter, $list, $line, $month, $year, $from, $to, $shifts);
        $frequency = collect([
            'id' => $object->id,
            'line' => $line->name,
            'division' => $filter == 1 ? $object?->name : $object->divisions($from, $to)->where('line_divisions.line_id', $line->id)->pluck('name')->implode(','),
            'employee' => $filter == 1 ? $object->users($from, $to)?->where('line_users_divisions.line_id', $line->id)->pluck('fullname')->implode(',') : $object?->fullname,
            'emp_code' => $filter == 1 ? $object->users($from, $to)?->where('line_users_divisions.line_id', $line->id)->pluck('emp_code')->implode(',') : ($object?->emp_code ? $object?->emp_code : ''),
            'color' => $filter == 1 ? $object?->DivisionType->color : $object->divisions($from, $to)?->first()?->DivisionType->color,
            'num_doctors' => $freq['doctors_count'],
            'meet' => $freq['meet'],
            'below' => $freq['below'],
            'above' => $freq['above'],
            'ratio(meet)' => $freq['ratio1'],
            'ratio(meet + above)' => $freq['ratio2'],
            'target_v' => $freq['total'],
            'actual_v' => $freq['actuals_count'],
            'meet_actual_v' => $freq['actualCount'],
            'achieve' => $freq['total'] ? round(($freq['actualCount'] / $freq['total']) * 100, 2) . ' %' : 0 . ' %',
        ]);
        return $frequency;
    }

    private function frequencies($objects, $filter, $list, $line, $month, $year, $from, $to, $shifts)
    {
        // throw new CrmException($line);
        $meet = 0;
        $below = 0;
        $above = 0;
        $total = 0;
        $ratio1 = 0;
        $ratio2 = 0;
        $doctors_count = 0;
        $actuals_count = 0;
        $actualCount = 0;
        $table = $filter == 1 ? 'actual_visits.div_id' : 'actual_visits.user_id';
        foreach ($objects as $object) {
            $doctors = $this->doctors($list, $object, $table, $filter, $line, $month, $year, $from, $to, $shifts);
            // $doctorIds = $doctors->pluck('id');
            $doctors_count += $doctors->count();
            // $actuals = $this->actuals($list, $object, $table, $line, $doctorIds, $month, $year);
            // $actuals_count += $actuals->count();
            $doctors->each(function ($doctor) use (
                &$actuals_count,
                &$meet,
                &$below,
                &$above,
                &$total,
                &$actualCount
            ) {
                // $frequency = $this->frequencyType($doctor, $list, $line, $month, $year);
                $frequency = (int)$doctor->frequency;
                // $frequency = (int)$frequency->sum('frequency');
                $total += $frequency;
                $actual = $doctor->actuals;
                $actuals_count += $actual;
                // $actual = $actuals->where('account_dr_id', $doctor->id)->count();
                // throw new CrmException([$actual, $frequency]);
                if ($frequency === 0) {
                    $below++;
                } else {
                    if ($frequency === $actual) $meet++;
                    if ($frequency > $actual) $below++;
                    if ($frequency < $actual) $above++;
                }
                if ($actual && $actual > $frequency) $actualCount += $frequency;
                if ($actual && $actual <= $frequency) $actualCount += $actual;
            });
        }
        $ratio1 += $doctors_count != 0 ? round(($meet / $doctors_count) * 100, 2) : 0;
        $ratio2 += $doctors_count != 0 ? round((($meet + $above) / $doctors_count) * 100, 2) : 0;
        return array(
            'doctors_count' => $doctors_count,
            'actuals_count' => $actuals_count,
            'meet' => $meet,
            'below' => $below,
            'above' => $above,
            'total' => $total,
            'ratio1' => $ratio1,
            'ratio2' => $ratio2,
            'actualCount' => $actualCount,
        );
    }

    public function showData(Request $request)
    {
        $list = $request->listFilter;
        $line = Line::find($list['line']);
        $shifts = Shift::when(!empty($list['shifts']), fn($q) => $q->whereIntegerInRaw("shifts.id", $list['shifts']))
            ->get()->pluck('id')->toArray();
        $from = Carbon::parse($list['fromDate'])->startOfDay();
        $to = Carbon::parse($list['toDate'])->endOfDay();
        $month = [Carbon::parse($from)->format('m'), Carbon::parse($to)->format('m')];
        $year = Carbon::parse($from)->format('Y');
        if ($request->div != null) {
            $object = LineDivision::find($request->div);
        } else {
            $object = User::find($request->user);
        }
        // $doctors = $this->doctors($list, $object, $list['filter'], $line, $month, $year, $from, $to, $shifts);


        $divisions = [];
        $division_type = DivisionType::where('last_level', '=', 1)->first()->id;
        if ($list['filter'] == 1) {
            if ($object->is_kol) {
                $divisions = [$object->id];
            } else {
                $divisions = $object->getBelowDivisions()->where('is_kol', 0)
                    ->where('division_type_id', '=', $division_type)->unique('id')->pluck('id')->toArray();
            }
        }
        if ($list['filter'] == 2) {
            $divisions = $object?->allBelowDivisions($line, $from, $to)->where('is_kol', 0)
                ->where('division_type_id', '=', $division_type)->unique('id')->pluck('id')->toArray();
        }
        $doctors = (new DoctorService)->getDoctorsِCoverage(
            lines: [$line->id],
            divisions: $divisions,
            from: $from,
            to: $to,
            specialities: $list['specialities'],
            accountTypes: $list['types'],
            shifts: $shifts
        );



        $doctorIds = $doctors->pluck('id');
        $actuals = $this->showActualData($list, $object, $list['filter'] == 1 ? 'actual_visits.div_id' : 'actual_visits.user_id', $line, $doctorIds, $month, $year);

        if ($request->column == 'actual_v') {
            $data = $this->showAllActual($actuals);
            return $this->respond($data);
        }
        $data = $doctors->map(function ($doctor) use ($actuals, $request, $list, $line, $month, $year) {
            $frequency = $this->frequencyType($doctor, $list, $line, $month, $year);
            $frequency = (int)$frequency->sum('frequency');
            $actual = $actuals->where('account_dr_id', $doctor->id)->count();
            switch ($request->column) {
                case 'meet':
                    $this->editDoctorForFrequency($doctor, $actual, $frequency, $frequency !== 0 && $frequency === $actual);
                    break;
                case 'below':
                    $this->editDoctorForFrequency($doctor, $actual, $frequency, $frequency === 0 || $frequency > $actual);
                    break;
                case 'above':
                    $this->editDoctorForFrequency($doctor, $actual, $frequency, $frequency !== 0 && $frequency < $actual);
                    break;
                case 'num_doctors':
                    $doctor->setAttribute('frequency', $frequency ?? '');
                    $doctor->setAttribute('actual', $actual ?? '');
                    break;
                case 'total_frequencies':
                    $doctor->setAttribute('frequency', $frequency !== 0 ? $frequency : '');
                    break;
            }
            return $doctor;
        })->filter(fn($doctor) => $doctor != null)->values()->map(function ($doctor) use ($actuals) {
            return [
                'doctor_id' => $doctor->id,
                'line' => $doctor->line,
                'division' => $doctor->division ? $doctor->division : '',
                'brick' => $doctor->brick,
                'account' => $doctor->account,
                'account_id' => $doctor->account_id,
                'acc_type' => $doctor->account_type,
                'doctor' => $doctor->doctor,
                'speciality' => $doctor->speciality,
                'doc_class' => $doctor->class,
                'frequency' => $doctor->frequency,
                'actuals' => $actuals->where('account_dr_id', $doctor->id)->count(),
            ];
        });
        return  $this->respond($data);
    }

    private function editDoctorForFrequency(&$doctor, $actual, $frequency, $condition,)
    {
        if ($condition) {
            $doctor->setAttribute('frequency', $frequency ?? '');
            $doctor->setAttribute('actual', $actual ?? '');
        } else {
            $doctor = null;
        }
    }
    private function showAllActual($actuals)
    {
        $actuals = $actuals->map(function ($actual) {
            return [
                'id' => $actual->id,
                'line' => $actual->line,
                'division' => $actual->division ? $actual->division : '',
                'brick' => $actual->brick,
                'employee' => $actual->user ? $actual->user : '',
                'account' => $actual->account,
                'acc_type' => $actual->acc_type,
                'doctor' => $actual->doctor ? $actual->doctor : '',
                'speciality' => $actual->speciality,
                'date' => $actual->date,
                'type' => $actual->type,
            ];
        })->toArray();
        return $actuals;
    }

    private function showActualData($list, $object, $table, $line, $doctors, $month, $year)
    {

        $data = ActualVisit::select([
            'actual_visits.id as id',
            'actual_visits.visit_date',
            'actual_visits.account_dr_id',
            DB::raw("DATE_FORMAT(crm_actual_visits.visit_date,'%Y-%m-%d %H:%s:%i') as date"),
            'specialities.id as speciality_id',
            'bricks.name as brick',
            'lines.name as line',
            'line_divisions.name as division',
            'users.fullname as user',
            'accounts.name as account',
            'accounts.id as account_id',
            'account_types.name as acc_type',
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            DB::raw('IFNULL(group_concat(distinct crm_products.name),"") as product'),
            'specialities.name as speciality',
            'visit_types.name as type',
        ])
            ->leftJoin('lines', 'actual_visits.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'actual_visits.div_id', 'line_divisions.id')
            ->leftJoin('users', 'actual_visits.user_id', 'users.id')
            ->leftJoin('accounts', 'actual_visits.account_id', 'accounts.id')
            ->leftJoin('bricks', 'actual_visits.brick_id', 'bricks.id')
            ->leftJoin('account_types', 'actual_visits.acc_type_id', 'account_types.id')
            ->leftJoin('doctors', 'actual_visits.account_dr_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('visit_types', 'actual_visits.visit_type_id', 'visit_types.id')
            ->leftJoin('actual_visit_products', 'actual_visits.id', 'actual_visit_products.visit_id')
            ->leftJoin('products', 'actual_visit_products.product_id', 'products.id')
            ->whereNull('actual_visits.deleted_at')
            ->where('actual_visits.line_id', $line->id)
            ->whereIntegerInRaw('actual_visits.account_dr_id', $doctors)
            // ->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null))
            ->where($table, $object->id)
            ->whereBetween(DB::raw("(DATE_FORMAT(crm_actual_visits.visit_date,'%m'))"), $month)->whereYear('visit_date', $year);
        if (!empty($list['types'])) {
            $data = $data->whereIntegerInRaw('actual_visits.acc_type_id', $list['types']);
        }
        if (!empty($list['specialities'])) {
            $data = $data->whereIntegerInRaw('doctors.speciality_id', $list['specialities']);
        }
        $data = $data->groupBy('id')->get();
        return $data;
    }

    public function post(Request $request)
    {
        $frequencyData = $request->data;
        $filters = $request->filters;
        $from = Carbon::parse($filters['fromDate'])->startOfMonth()->toDateString();
        $to = Carbon::parse($filters['toDate'])->endOfMonth()->toDateString();
        $period = CarbonPeriod::create($from, '1 month', $to);

        $shifts = $filters['shifts'] ?? [];
        $shiftsCount = count($shifts);

        // Handle case with multiple or zero shifts
        if ($shiftsCount !== 1) {
            $kpi = Kpi::where('name', 'Frequency')->first();
            return $this->handlePost(KPITypes::FREQUENCY, $frequencyData, $period, $kpi);
        }

        // Handle single shift case
        $shift = Shift::find($shifts[0]);
        if (!$shift) {
            return $this->respondWithError('Invalid shift selected.');
        }

        $kpiName = $shift->name . ' Frequency';
        $kpi = Kpi::where('name', $kpiName)->first();

        $shiftFieldMap = [
            1 => ['key' => 'am_frequency', 'type' => KPITypes::AM_FREQUENCY],
            2 => ['key' => 'pm_frequency', 'type' => KPITypes::PM_FREQUENCY],
            3 => ['key' => 'ph_frequency', 'type' => KPITypes::PH_FREQUENCY],
        ];

        if (!isset($shiftFieldMap[$shift->id])) {
            return $this->respondWithError('Unsupported shift ID.');
        }

        $field = $shiftFieldMap[$shift->id]['key'];
        $kpiType = $shiftFieldMap[$shift->id]['type'];

        $formattedData = array_map(function ($item) use ($field) {
            $value = floatval(str_replace('%', '', $item['ratio(meet)']));
            return [
                'id' => $item['id'],
                $field => round($value, 2),
            ];
        }, $frequencyData);

        return $this->handlePost($kpiType, $formattedData, $period, $kpi);
    }

    private function handlePost($kpiType, $data, $period, $kpi)
    {
        $this->postVisitKpisService->post($kpiType, $data, $period, $kpi);
        return $this->respondSuccess();
    }
}
