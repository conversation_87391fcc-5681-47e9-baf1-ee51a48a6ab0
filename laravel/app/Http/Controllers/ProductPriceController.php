<?php

namespace App\Http\Controllers;

use App\Action;
use App\Distributor;
use App\Form;
use App\Helpers\LogActivity;
use App\Http\Controllers\Controller;
use App\Http\Requests\ProductPriceRequest;
use App\Permission;
use App\Product;
use App\Productprice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ProductPriceController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $distributors = Distributor::where('deleted_at',null)->orderBy('sort', 'ASC')->get(['id', 'name']);
        return response()->json(['distributors' => $distributors]);
    }

    public function getProductPrices(Product $product)
    {
        $product_prices = DB::table('product_prices')
        ->select(
            'product_prices.id',
            'product_prices.distributor_id',
            'distributors.name as distributor',
            'products.name as product',
            'product_prices.selling_price',
            'product_prices.avg_price',
            'product_prices.avg_tender_price',
            'product_prices.avg_target_price',
            'product_prices.from_date',
            'product_prices.to_date'
        )
        ->leftJoin('products', 'product_prices.product_id', '=', 'products.id')
        ->leftJoin('distributors', 'product_prices.distributor_id', '=', 'distributors.id')
        ->where('product_prices.product_id', '=', $product->id)
        ->where('product_prices.deleted_at', '=', null)
        ->get();
    LogActivity::addLog();

    return response()->json(['product_prices'=>$product_prices]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(ProductPriceRequest $request, Product $product)
    {
        $product_price = Productprice::create($request->all());

        $model_id = $product_price->id;
        $model_type = ('App\Productprice');

        LogActivity::addLog( $model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $product_price = DB::table('product_prices')
            ->select(
                'product_prices.id',
                'product_prices.distributor_id',
                'product_prices.product_id',
                'product_prices.selling_price',
                'product_prices.avg_price',
                'product_prices.avg_tender_price',
                'product_prices.avg_target_price',
                'product_prices.from_date',
                'product_prices.to_date'
            )
            ->where('product_prices.id', '=', $id)
            ->where('product_prices.deleted_at', '=', null)
            ->first();
        
        
        
        
        
        
        $model_id = $id;
        $model_type = ('App\Productprice');
        LogActivity::addLog( $model_id, $model_type);
        return response()->json(['product_price' => $product_price]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(ProductPriceRequest $request, $id)
    {

        $product_price = Productprice::find($id);
        $product_price->product_id       = $request->input('product_id');
        $product_price->distributor_id       = $request->input('distributor_id');
        $product_price->selling_price       = $request->input('selling_price');
        $product_price->avg_price       = $request->input('avg_price');
        $product_price->avg_tender_price       = $request->input('avg_tender_price');
        $product_price->avg_target_price       = $request->input('avg_target_price');
        $product_price->from_date       = $request->input('from_date');
        $product_price->to_date      = $request->input('to_date');
        $product_price->save();

        
        
        
        
        
        
        $model_id = $id;
        $model_type = ('App\Productprice');

        LogActivity::addLog( $model_id, $model_type);

        return response()->json(['status' => 'success']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Productprice $productPrice)
    {
        $productPrice->delete();

        
        
        
        
        
        
        $model_id = $productPrice->id;
        $model_type = ('App\Productprice');

        LogActivity::addLog( $model_id, $model_type);

        return response()->json(['status' => 'success']);
    }
}
