<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Http\Requests\WidgetSettingRequest;
use App\WidgetSetting;
use Illuminate\Http\Request;
use App\User;
use App\Widget;
use App\WidgetModule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class WidgetSettingController extends ApiController
{
    /**
     * Store a newly created resource in storage.
     *
     * @param  WidgetSettingRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(WidgetSettingRequest $request)
    {
        auth()->user()->widgets()->detach();

        auth()->user()->widgets()->syncWithoutDetaching($request->widgets);

        return $this->respondSuccess();
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\WidgetSetting  $widgetSetting
     * @return \Illuminate\Http\Response
     */
    public function show()
    {

        $modules = WidgetModule::with('widgets')->get();
        $widgets = auth()->user()->widgets;

        return $this->respond(compact('modules', 'widgets'));
    }
}
