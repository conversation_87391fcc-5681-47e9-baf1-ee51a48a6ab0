<?php

namespace App\Http\Controllers;

use App\Chart;
use App\ChartLabel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;


class ChartLabelController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $chart_labels = DB::table('chart_labels')
            ->select('chart_labels.id', 'chart_labels.name as label','charts.name as chart')
            ->leftJoin('charts', 'chart_labels.chart_id', '=', 'charts.id')
            ->where('chart_labels.deleted_at','=',null)
            ->get();
        $total = count($chart_labels);
        return response()->json(compact('chart_labels', 'total'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $charts = Chart::select('charts.id', 'charts.name')->get();
        return response()->json(['status' => 'success', 'charts'=> $charts]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $chart_label = new ChartLabel();
        $chart_label->name = $request->input('name');
        $chart_label->chart_id = $request->input('chart_id');
        $chart_label->save();

        return response()->json(['status' => 'success']);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\ChartLabel  $chartLabel
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $chart_label = DB::table('chart_labels')
        ->select('chart_labels.id', 'chart_labels.name as label', 'charts.name as chart')
            ->leftJoin('charts', 'chart_labels.chart_id', '=', 'charts.id')
            ->where('chart_labels.id', '=', $id)
            ->first();

        return response()->json($chart_label);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\ChartLabel  $chartLabel
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $charts = Chart::select('charts.id', 'charts.name')->get();
        $chart_label = DB::table('chart_labels')
        ->select('chart_labels.id', 'chart_labels.name', 'chart_labels.chart_id')
        ->where('chart_labels.id', '=', $id)
            ->first();

        return response()->json( ['charts'=> $charts, 'chart_label'=> $chart_label]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\ChartLabel  $chartLabel
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $chart_label = ChartLabel::find($id);
        $chart_label->name      = $request->input('name');
        $chart_label->chart_id  = $request->input('chart_id');
        $chart_label->save();

        return response()->json(['status' => 'success']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\ChartLabel  $chartLabel
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $chart_label = ChartLabel::find($id);

        if ($chart_label) {
            $chart_label->delete();
        }

        return response()->json(['status' => 'success']);
    }
}
