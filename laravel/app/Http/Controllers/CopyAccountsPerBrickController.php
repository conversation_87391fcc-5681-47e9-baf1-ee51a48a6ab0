<?php

namespace App\Http\Controllers;

use App\Account;
use App\AccountLines;
use App\Line;
use App\LineBricks;
use App\Models\NewAccountDoctor;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class CopyAccountsPerBrickController extends ApiController
{
    public function bricks(Line $line)
    {
        return $this->respond($line->bricks()->where('line_bricks.line_id', $line->id)->get()->unique("id")->values());
    }
    public function index(Request $request)
    {
        $listFilter = $request->list;
        $fields = [
            "s",
            "account",
            "account_type",
            "line",
            "division",
            "brick",
            "from_date",
            "to_date",
        ];
        $accounts = Account::select(
            'accounts.id as id',
            'lines.name as line',
            'lines.id as line_id',
            'line_divisions.name as division',
            'line_divisions.id as div_id',
            DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
            DB::raw('IFNULL(group_concat(distinct crm_bricks.id),"") as brick_id'),
            DB::raw('IFNULL(crm_accounts.code,"") as acc_code'),
            DB::raw('IFNULL(crm_accounts.name,"") as account'),
            'account_types.name as account_type',
            'account_lines.id as acc_line_id',
            DB::raw('DATE_FORMAT(crm_account_lines.from_date,"%d-%m-%Y") as from_date'),
            DB::raw('IFNULL(DATE_FORMAT(crm_account_lines.to_date,"%d-%m-%Y"),"") as to_date'),
            'division_types.color'
        )
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
            ->join(
                'account_lines',
                function ($join) use ($listFilter) {
                    $join->on('accounts.id', 'account_lines.account_id');
                    // ->where('account_lines.line_id', $listFilter['line_id'])
                    // ->where('account_lines.line_division_id', $listFilter['division_id'])
                    // ->where('account_lines.brick_id', $listFilter['brick_id']);
                }
            )
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->where('account_lines.line_id', $listFilter['line_id'])
            ->whereIntegerInRaw('account_lines.brick_id', $listFilter['bricks'])
            ->where('accounts.deleted_at', null)
            ->where('account_lines.deleted_at', null)
            ->orderBy('accounts.code', 'asc');
        $accounts = $accounts->groupBy(
            "accounts.id",
            "lines.id",
            "line_divisions.id",
            "account_lines.id",
            "account_lines.from_date",
            "account_lines.to_date",
        )->get();

        return $this->respond(['accounts' => $accounts, 'fields' => $fields]);
    }
    public function store(Request $request)
    {
        $accounts = collect($request->accounts)->unique('id')->values();
        DB::transaction(function () use ($request, $accounts) {
            foreach ($request->bricks as $brick) {
                $lineBricks = LineBricks::select('line_id', 'brick_id', 'line_division_id', 'from_date', 'to_date')
                    ->whereNot('line_id', $request->line_id)
                    ->where('brick_id', $brick)
                    ->whereNull('deleted_at')->where('from_date', '<=', now())
                    ->where(fn($q) => $q->where('to_date', '>', (string) Carbon::now())->orWhere('to_date', null))
                    ->get();
                foreach ($accounts as $account) {
                    foreach ($lineBricks as $lineBrick) {
                        $exists = AccountLines::select('line_id', 'line_division_id', 'brick_id', 'from_date', 'to_date')
                            ->where('account_id', $account['id'])
                            // ->where('line_id', $account['line_id'])
                            ->where('line_division_id', $lineBrick->line_division_id)
                            ->where('brick_id', $brick)
                            ->exists();
                        if (!$exists) {
                            $accountLine = AccountLines::find($account['acc_line_id']);
                            $newAccountLine = AccountLines::create([
                                'account_id' => $accountLine->account_id,
                                'line_id' => $lineBrick->line_id,
                                'line_division_id' => $lineBrick->line_division_id,
                                'brick_id' => $brick,
                                'class_id' => $accountLine->class_id,
                                'visit_id' => $accountLine->visit_id,
                                'll' => $accountLine->ll,
                                'lg' => $accountLine->lg,
                                'from_date' => Carbon::parse('2035-01-01')->toDateString(),
                                'to_date' => null,
                            ]);
                            $newAccountDoctors = NewAccountDoctor::where('account_id', $accountLine->account_id)
                                ->where('account_lines_id', $accountLine->id)
                                ->get();
                            foreach ($newAccountDoctors as $newAccountDoctor) {
                                NewAccountDoctor::create([
                                    'account_id' => $newAccountLine->account_id,
                                    'line_id' => $newAccountLine->line_id,
                                    'doctor_id' => $newAccountDoctor->doctor_id,
                                    'account_lines_id' => $newAccountLine->id,
                                    'from_date' => Carbon::parse('2035-01-01')->toDateString(),
                                    'to_date' => null,
                                ]);
                            }
                        }
                    }

                    // throw new CrmException([$exists, Brick::find($brick)]);

                }
            }
        });
        return $this->respondSuccess();
    }
}
