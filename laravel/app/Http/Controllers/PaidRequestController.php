<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Http\Controllers\Controller;
use App\Models\Attachment;
use App\Models\PaidRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PaidRequestController extends ApiController
{
    public function show(PaidRequest $paidRequest)
    {
        return $this->respond(PaidRequest::find($paidRequest->id));
    }
    public function update(PaidRequest $paidRequest, Request $request)
    {
        $paidRequest->attachments()->delete();
        if ($request->attach) {
            Attachment::create([
                'attachable_id' => $paidRequest->id,
                'attachable_type' => PaidRequest::class,
                'path' => $request->attach,
            ]);
        }
        $paidRequest->update([
            'user_id' => Auth::id(),
            'amount' => $request->amount,
            'type' => $request->type,
            'description' => $request->description,
            'ref_no' => $request->ref_no,
            'date' => $request->date,
        ]);

        LogActivity::addLog($paidRequest->id, PaidRequest::class);
        return $this->respondSuccess();
    }
    public function destroy(PaidRequest $paidRequest)
    {
        $paidRequest->delete();
        return $this->respondSuccess();
    }
}
