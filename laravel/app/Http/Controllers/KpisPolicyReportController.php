<?php

namespace App\Http\Controllers;

use App\DivisionType;
use App\Exceptions\CrmException;
use App\Http\Controllers\Controller;
use App\Line;
use App\Models\Kpi;
use App\Position;
use Illuminate\Http\Request;

class KpisPolicyReportController extends ApiController
{
    public function getRoles(Request $request)
    {
        $roles = collect([]);
        $lines = Line::whereIntegerInRaw('id', $request->lines)->get();
        foreach ($lines as $line) {
            $roles = $roles->merge(collect([...$line->divisionTypes, ...$line->getLinePositions()])
                ->map(fn ($role) => ['id' => $role->id . '_' . get_class($role), 'name' => $role->name]))->unique(function ($item) {
                return $item['id'] . $item['name'];
            });
        }
        $kpis = Kpi::select(
            'kpis.id',
            'kpis.name',
            'kpis.short_name',
            'kpi_ratios.ratio',
            'kpi_ratios.line_id',
            'kpi_ratios.roleable_id',
            'kpi_ratios.roleable_type',
            'kpi_ratios.minimum'
        )
            ->leftJoin('kpi_ratios', 'kpis.id', 'kpi_ratios.kpi_id')
            ->whereIntegerInRaw('kpi_ratios.line_id', $lines)
            ->get()->unique('id')->values();
        // throw new CrmException($roles);
        return $this->respond(['roles' => $roles, 'kpis' => $kpis]);
    }
    public function filter(Request $request)
    {
        $kpis = $request->kpisFilter;
        $items = collect([]);
        $kpiIds = Kpi::when(!empty($kpis['kpis']), fn ($q) => $q->whereIntegerInRaw("lines.id", $kpis['lines']))->pluck('id');
        $fields = collect(['line', 'role', 'kpi', 'minimum', 'ratio']);
        foreach ($kpis['role'] as $role) {
            $kpi = Kpi::select(
                'kpis.id',
                'kpis.name as kpi',
                'kpis.short_name',
                'lines.name as line',
                'kpi_ratios.ratio',
                'kpi_ratios.line_id',
                'kpi_ratios.roleable_id',
                'kpi_ratios.roleable_type',
                'kpi_ratios.minimum'
            )
                ->leftJoin('kpi_ratios', 'kpis.id', 'kpi_ratios.kpi_id')
                ->leftJoin('lines', 'kpi_ratios.line_id', 'lines.id');
            if ($role['roleable_type'] == Position::class) {
                $kpi = $kpi->addSelect('positions.name as role')->leftJoin('positions', function ($join) {
                    $join->on('kpi_ratios.roleable_id', 'positions.id')
                        ->where('kpi_ratios.roleable_type', Position::class);
                });
            }
            if ($role['roleable_type'] == DivisionType::class) {
                $kpi = $kpi->addSelect('division_types.name as role')
                    ->leftJoin('division_types', function ($join) {
                        $join->on('kpi_ratios.roleable_id', 'division_types.id')
                            ->where('kpi_ratios.roleable_type', DivisionType::class);
                    });
            }

            $kpi = $kpi->whereIntegerInRaw('kpi_ratios.line_id', $kpis['lines'])
                ->whereIntegerInRaw('kpis.id', $kpiIds)
                ->where('kpi_ratios.roleable_id', $role['roleable_id'])->get();
            $items = $items->merge($kpi);
        }
        return $this->respond(['items' => $items, 'fields' => $fields]);
    }
}
