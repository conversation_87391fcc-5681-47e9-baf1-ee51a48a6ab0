<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\DB;
use App\Http\Requests\DistributorRequest;
use App\Distributor;
use App\DivisionType;
use Maatwebsite\Excel\Excel as ExcelType;
use App\Helpers\LogActivity;
use App\Helpers\ExcelImporter;
use App\Http\Requests\ImportRequest;
use App\Http\Requests\MailRequest;
use App\Imports\Updates\DistributorsImport as UpdatesDistributorsImport;

class DistributorsController extends ApiController
{

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $you = auth()->user()->id;
        $distributors = DB::table('distributors')
            ->select('distributors.id', 'distributors.name', 'distributors.notes', 'distributors.sort')
            ->whereNull('deleted_at')
            ->orderBy('distributors.sort', 'asc')
            ->get();
        $total = count($distributors);
        LogActivity::addLog();
        return response()->json(compact('distributors', 'you', 'total'));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $distributor = DB::table('distributors')
            ->select(
                'distributors.id',
                'distributors.name',
                'distributors.notes',
                'distributors.sort',
                'distributors.unified_distributor_id'
            )
            ->where('distributors.id', '=', $id)
            ->first();
        $model_id = $id;
        $model_type = ('App\Distributor');

        LogActivity::addLog($model_id, $model_type);
        return response()->json([$distributor]);
    }

    public function store(DistributorRequest $request)
    {
        $max_sort = Distributor::withTrashed()->max('sort');
        if (!$max_sort) {
            $max_sort = 100;
        } else {
            $max_sort += 100;
        }

        $distributor = new Distributor();
        $distributor->name = $request->input('name');
        $distributor->notes = $request->input('notes');
        $distributor->sort = $max_sort;
        $distributor->unified_distributor_id = $request->input('unified_distributor_id');
        $distributor->save();
        $model_id = $distributor->id;
        $model_type = Distributor::class;
        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success', 'distributor' => $distributor]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $distributor = DB::table('distributors')
            ->select(
                'distributors.id',
                'distributors.name',
                'distributors.notes',
                'distributors.sort',
                'unified_distributor_id'
            )
            ->where('distributors.id', '=', $id)
            ->first();
        $model_id = $id;
        $model_type = Distributor::class;
        LogActivity::addLog($model_id, $model_type);
        return response()->json($distributor);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(DistributorRequest $request, $id)
    {
        $distributor = Distributor::find($id);
        $distributor->name       = $request->input('name');
        $distributor->notes      = $request->input('notes');
        $distributor->sort      = $request->input('sort');
        $distributor->unified_distributor_id = $request->input('unified_distributor_id');
        $distributor->save();
        $model_id = $id;
        $model_type = Distributor::class;

        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $distributor = Distributor::find($id);
        $productDistributor = DB::table("product_prices")->where("distributor_id", $id)->count();
        if ($distributor) {
            if ($productDistributor > 0) {
                return response()->json(['statusText' => 'failed'], 422);
            }
            $distributor->delete();
        }
        $model_id = $id;
        $model_type = Distributor::class;

        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    public function import(ImportRequest $request)
    {
        Distributor::import($request);
        return $this->respondSuccess();
    }

    public function updateByImport(ImportRequest $request)
    {
        DivisionType::import(request: $request, update: true);

        return $this->respondSuccess();
    }


    public function exportdistributors()
    {
        return Distributor::export(ExcelType::XLSX);
    }

    public function exportcsv()
    {
        return Distributor::export(ExcelType::CSV);
    }

    public function exportpdf()
    {
        $distributors = Distributor::where('deleted_at', null)->get();
        return Distributor::exportPdf($distributors);
    }

    public function sendmail(MailRequest $request)
    {
        $distributors = Distributor::where('deleted_at', null)->get();
        return Distributor::sendMail($request, $distributors);
    }
}
