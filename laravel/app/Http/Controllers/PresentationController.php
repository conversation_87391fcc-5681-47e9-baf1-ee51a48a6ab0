<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Helpers\ExcelImporter;
use App\Http\Requests\ImportRequest;
use App\Http\Requests\MailRequest;
use App\Services\Enums\MediaTypes;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Excel as ExcelType;
use App\Http\Requests\PresentationRequest;
use App\Imports\Updates\PresentationsImport;
use App\Models\Attachment;
use App\Models\EDetailing\Presentation;
use App\Models\EDetailing\PresentationAttachment;
use App\Models\EDetailing\PresentationSlide;
use App\Models\EDetailing\ProductPresentation;
use App\Models\EDetailing\Statistic;
use App\Models\EDetailing\Thumbnail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;

class PresentationController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $presentations = Presentation::get();
        return response()->json(['presentations' => $presentations]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(PresentationRequest $request)
    {

        DB::transaction(function () use ($request) {
            $presentation = Presentation::create([
                'name' => $request->name,
                'notes' => $request->notes,
            ]);
            foreach ($request->attachments as $url) {
                $thumbnail = Thumbnail::create([
                    'attachment_id' => null
                ]);

                Attachment::create([
                    'path' => $url['thumbnail'] ?? "/img/default.jpeg",
                    'attachable_id' => $thumbnail->id,
                    'attachable_type' => Thumbnail::class,
                ]);

                $slide = PresentationSlide::create([
                    'presentation_id' => $presentation->id,
                    'thumbnail_id' => $thumbnail->id,
                    'media_type' => MediaTypes::get($request->type_id)
                ]);

                Attachment::create([
                    'path' => $url['slide'],
                    'attachable_id' => $slide->id,
                    'attachable_type' => PresentationSlide::class,
                ]);


                $slide->save();
            }
        });
        return $this->respondCreated();
    }

    public function mediaTypes()
    {
        return $this->respond(MediaTypes::getAllMapped());
    }


    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(int $id)
    {
        $slides = PresentationSlide::where("presentation_id", $id)
            ->with(["presentation", "attachment", "thumbnail.attachment"])
            ->get()
            ->map(function (PresentationSlide $slide) use ($id) {
                return [
                    "title" => $slide->presentation->name,
                    "description" => $slide->presentation->note,
                    "mediaType" => $slide->media_type,
                    "thumb" => $slide->thumbnail->attachment->url,
                    "src" => $slide->attachment->url,
                    "id" => $slide->id,
                    "presentation_id" => $id
                ];
            });

        return response()->json(['slides' => $slides]);
    }

    public function edit(int $id)
    {
        $presentation = Presentation::where("id", $id)
            ->with(["slides.attachment", "slides.thumbnail.attachment"])
            ->first();
        $type = null;
        $attachments = $presentation->slides->map(function (PresentationSlide $slide) use (&$type) {
            $thumbnailAttachment = $slide->thumbnail?->attachment;
            $slideAttachment = $slide->attachment;
            $type = $slide->media_type->getId();
            return [
                "id" => $slide->id,
                "thumbnail" => $thumbnailAttachment ? [
                    "id" => $thumbnailAttachment->id,
                    "url" => $thumbnailAttachment->url,
                    "path" => $thumbnailAttachment->path,
                    "name" => $thumbnailAttachment->url,
                ] : null,
                "slide" => $slideAttachment ? [
                    "id" => $slideAttachment->id,
                    "url" => $slideAttachment->url,
                    "path" => $thumbnailAttachment->path,
                    "name" => $slideAttachment->url,
                ] : null,
            ];
        })->toArray();

        // Convert presentation to an array and inject attachments
        $responseData = $presentation->toArray();
        $responseData['attachments'] = $attachments;
        $responseData['type_id'] = $type;
        unset($responseData['slides']);

        return $this->respond($responseData);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(PresentationRequest $request, $id)
    {
        $presentation = Presentation::find($id);
        DB::transaction(function () use ($request, $presentation) {
            $presentation->update([
                'name' => $request->name,
                'notes' => $request->notes,
            ]);
            $presentation->attachments()->delete();
            $presentation->thumbnailAttachments()->delete();
            $presentation->thumbnails()->delete();
            $presentation->slides()->delete();
            foreach ($request->attachments as $attachment) {
                $thumbnail = Thumbnail::create([
                    'attachment_id' => null
                ]);

                Attachment::create([
                    'path' => $attachment['thumbnail'] ?? "/img/default.jpeg",
                    'attachable_id' => $thumbnail->id,
                    'attachable_type' => Thumbnail::class,
                ]);

                $slide = PresentationSlide::create([
                    'presentation_id' => $presentation->id,
                    'thumbnail_id' => $thumbnail->id,
                    'media_type' => MediaTypes::get($request->type_id)
                ]);

                Attachment::create([
                    'path' => $attachment['slide'],
                    'attachable_id' => $slide->id,
                    'attachable_type' => PresentationSlide::class,
                ]);


                $slide->save();
            }
        });
        return $this->respondSuccess();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(int $id)
    {
        $presentation = Presentation::findOrFail($id);
        $presentation->attachments()->delete();
        $presentation->thumbnailAttachments()->delete();
        $presentation->thumbnails()->delete();
        $presentation->slides()->delete();
        $presentation->delete();

        return $this->respondSuccess();
    }

    public function import(ImportRequest $request)
    {
        Presentation::import($request);
        return $this->respondSuccess();
    }

    public function importProductPresentation(ImportRequest $request)
    {
        ProductPresentation::import($request);
        return $this->respondSuccess();
    }

    public function updateByImport(ImportRequest $request)
    {
        ProductPresentation::import(request: $request, update: true);
        return $this->respondSuccess();
    }

    public function export()
    {
        return Presentation::export(ExcelType::XLSX);
    }

    public function exportcsv()
    {
        return Presentation::export(ExcelType::CSV);
    }

    public function exportpdf()
    {
        $types = Presentation::where('deleted_at', null)->get();
        return Presentation::exportPdf($types);
    }

    public function sendmail(MailRequest $request)
    {
        $types = Presentation::where('deleted_at', null)->get();
        return Presentation::sendmail($request, $types);
    }

    public function insertStatistics(Request $request)
    {
        $Statistic = Statistic::create([
            'product_id' => $request->product_id,
            'presentation_id' => $request->presentation_id,
            'slide_id' => $request->slide_id,
            'start' => $request->start,
            'end' => $request->end,
            'rate' => $request->rate,
            'comment' => $request->comment,
            'user_id' => auth()->id()
        ]);
        return $this->respond($Statistic->id);
    }

    public function updateStatistics(Statistic $statistic, Request $request)
    {
        $statistic->update([
            'end' => $request->end
        ]);

        return $this->respond();
    }

    public function destroySlide($id)
    {
        $presentationSlide = PresentationSlide::findorFail($id);
        $presentationSlide->attachment()->delete();
        $presentationSlide->thumbnailAttachment()->delete();
        $presentationSlide->thumbnail()->delete();
        $presentationSlide->delete();

        return $this->respondSuccess();
    }
}
