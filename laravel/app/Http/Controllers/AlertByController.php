<?php

namespace App\Http\Controllers;

use App\Models\AlertBy;
use Illuminate\Http\Request;

class AlertByController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\AlertBy  $alertBy
     * @return \Illuminate\Http\Response
     */
    public function show(AlertBy $alertBy)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\AlertBy  $alertBy
     * @return \Illuminate\Http\Response
     */
    public function edit(AlertBy $alertBy)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\AlertBy  $alertBy
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, AlertBy $alertBy)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\AlertBy  $alertBy
     * @return \Illuminate\Http\Response
     */
    public function destroy(AlertBy $alertBy)
    {
        //
    }
}
