<?php

namespace App\Http\Controllers;

use App\Country;
use App\Exports\CountriesExport;
use App\Imports\CountriesImport;
use App\Setting;
use Illuminate\Http\Request;
use App\Http\Requests\CountryRequest;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Excel as ExcelType;
use Illuminate\Support\Facades\Response;
use Meneses\LaravelMpdf\Facades\LaravelMpdf as PDF;
use Illuminate\Support\Facades\Mail;
use Spatie\Permission\Models\Permission;
use App\Helpers\LogActivity;
use App\Form;
use App\Action;
use App\Helpers\ExcelImporter;
use App\Http\Requests\ImportRequest;
use App\Http\Requests\MailRequest;
use App\Import;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Tymon\JWTAuth\Exceptions\JWTException;
use App\Imports\Updates\CountriesImport as UpdatesCountriesImport;

class CountryController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $countries = DB::table('countries')
        ->select('countries.id', 'countries.name', 'countries.sort')
        ->whereNull('deleted_at')
        ->orderBy('countries.sort', 'asc')
        ->get();
        $total = count($countries);
        
        
        
        
        
        
        LogActivity::addLog();
        return response()->json( compact('countries','total') );
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $max_sort = Country::withTrashed()->max('sort');
        if(!$max_sort){
            $max_sort = 100;
        }else{
            $max_sort += 100;
        }
        return response()->json( ['status' => 'success','max_sort' => $max_sort] );
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(CountryRequest $request)
    {
        $max_sort = Country::withTrashed()->max('sort');
        if(!$max_sort){
            $max_sort = 100;
        }else{
            $max_sort += 100;
        }
        $country = new Country();
        $country->name = $request->input('name');
        $country->sort = $max_sort;
        $country->save();

        
        
        
        
        
        
        $model_id = $country->id;
        $model_type = ('App\Country');

        LogActivity::addLog($model_id,$model_type);

        $max_sort = Country::withTrashed()->max('sort');
        if(!$max_sort){
            $max_sort = 100;
        }else{
            $max_sort += 100;
        }
        return response()->json( ['status' => 'success','max_sort' => $max_sort] );    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Country  $country
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $country = DB::table('countries')
        ->select('countries.id', 'countries.name', 'countries.sort')
        ->where('countries.id', '=', $id)
        ->first();
        
        
        
        
        
        
        $model_id = $id;
        $model_type = ('App\Country');

        LogActivity::addLog($model_id,$model_type);
        return response()->json( $country );
    }
    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Country  $country
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $country = DB::table('countries')
        ->select('countries.id', 'countries.name', 'countries.sort')
        ->where('countries.id', '=', $id)
        ->first();
        
        
        
        
        
        
        $model_id = $id;
        $model_type = ('App\Country');
        LogActivity::addLog($model_id,$model_type);
        return response()->json( $country );
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Country  $country
     * @return \Illuminate\Http\Response
     */
    public function update(CountryRequest $request, $id)
    {
        $country = Country::find($id);
        $country->name       = $request->input('name');
        $country->sort      = $request->input('sort');
        $country->save();
        
        
        
        
        
        
        $model_id = $id;
        $model_type = ('App\Country');

        LogActivity::addLog($model_id,$model_type);
        return response()->json( ['status' => 'success'] );
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Country  $country
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $country = Country::find($id);
        $lineCountry = DB::table("lines")->where("country_id", $id)->count();
        if($country){
            // if ($lineCountry > 0)
            // {
            //     return response()->json(['statusText'=>'failed'],422);
            // }
            $country->delete();
        }
        
        
        
        
        
        
        $model_id = $id;
        $model_type = ('App\Country');

        LogActivity::addLog($model_id,$model_type);
        return response()->json( ['status' => 'success'] );
    }

    public function import(ImportRequest $request)
    {
        Country::import($request);
        return $this->respondSuccess();
    }

    public function updateByImport(ImportRequest $request)
    {
        Country::import(request: $request, update: true);

        return $this->respondSuccess();
    }


    public function exportcountries()
    {
        return Country::export(ExcelType::XLSX);
    }


    public function exportcsv()
    {
        return Country::export(ExcelType::CSV);
    }


    public function exportpdf()
    {
        $countries = Country::all();
        $header = Setting::where('key','pdf_header')->pluck('value')->first();
        $image = Setting::where('key','pdf_logo')->pluck('value')->first();
        $footer = Setting::where('key','pdf_footer')->pluck('value')->first();
        $user_id = Auth::user()->id;
        $user_name = Auth::user()->name;
        $exported_date = Carbon::now();
        $pdf = PDF::loadView('countries.print', compact('footer','image','header','countries','user_id','user_name','exported_date'));
        
        
        
        
        
        
        LogActivity::addLog();
        return $pdf->download('countries.pdf');
    }

    public function sendmail(MailRequest $request)
    {
        $countries = Country::where('countries.deleted_at', '=', null)->get();
        return Country::sendMail($request,$countries);
    }
}
