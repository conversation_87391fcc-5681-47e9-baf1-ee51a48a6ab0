<?php

namespace App\Http\Controllers\Groups;

use App\AccountLines;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Http\Controllers\ApiController;
use App\Http\Controllers\Controller;
use App\Line;
use App\Models\Grouping;
use App\Services\GroupAccountService;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class GroupingAccountLinesController extends ApiController
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $listFilter = $request->listFilter;
        $line = Line::find($listFilter['line']);
        $filtered = new Collection();
        $filteredDivisions = new Collection();
        $data = new Collection();
        $division_type = DivisionType::where('last_level', '=', 1)->first()->id;
        $divisions = $line->divisions()
            ->when(!empty($listFilter['divisions']), fn($q) => $q->whereIntegerInRaw("line_divisions.id", $listFilter['divisions']))->get();
        $filtered = $filtered->merge($authUser->filterDivisions($line, $divisions, $listFilter));
        $filtered->each(function ($division) use ($filteredDivisions, $division_type) {
            $filteredDivisions = $filteredDivisions->push($division?->getBelowDivisions()
                ->where('division_type_id', '=', $division_type)->where('is_kol', 0));
        });

        $filteredDivisions = $filteredDivisions->collapse()->pluck('id')->unique()->toArray();
        $accounts = collect([]);

        $accounts = (new GroupAccountService())->ActiveAccountLines([$line->id], $filteredDivisions, $listFilter['bricks'], $listFilter['types']);

        $groups = Grouping::select('id', 'name')->where('groupable_type', AccountLines::class)->get();

        $fields = collect([
            "s",
            "id",
            "account_id",
            "line",
            "division",
            "brick",
            "account",
            "acc_class",
            "group_name",
            "account_type",
            "acc_mobile",
            "from_date",
            "to_date",
        ]);
        return $this->respond(compact('accounts', 'fields', 'groups'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $accountLinesIds = collect($request->accounts)->pluck('id');
        // throw new CrmException$request->group_id);
        AccountLines::whereIntegerInRaw('id', $accountLinesIds)->update([
            'group_id' => $request->group
        ]);
        LogActivity::addLogs($accountLinesIds, AccountLines::class);
        return $this->respondCreated();
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
