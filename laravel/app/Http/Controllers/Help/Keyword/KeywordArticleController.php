<?php

namespace App\Http\Controllers\Help\Keyword;

use App\Http\Controllers\ApiController;
use App\Http\Controllers\Controller;
use App\Models\Help\Keyword;
use Illuminate\Http\Request;

class KeywordArticleController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Keyword $keyword)
    {
        $articles = $keyword->articles()->get();
        return $this->respondAll($articles);
    }
}
