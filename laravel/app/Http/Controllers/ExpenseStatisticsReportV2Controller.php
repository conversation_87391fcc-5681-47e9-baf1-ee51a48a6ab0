<?php

namespace App\Http\Controllers;

use App\ActualVisit;
use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Http\Controllers\Controller;
use App\Line;
use App\LineDivision;
use App\LineDivisionUser;
use App\LineUser;
use App\Models\ApprovalFlowUser;
use App\Models\ApprovalSetting;
use App\Models\Attachment;
use App\Models\Expenses\EditExpenseDetail;
use App\Models\Expenses\Expense;
use App\Models\Expenses\ExpenseDetails;
use App\Models\Expenses\PerLocation\ExpenseLocationSetting;
use App\Models\Expenses\Types\ExpenseType;
use App\Models\Meal;
use App\Role;
use App\Services\ExpenseService;
use App\User;
use App\UserPosition;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ExpenseStatisticsReportV2Controller extends ApiController
{
    private function getExpenses($authLines, $linesAapprovables, $expense, $object, $table, $from, $to, $type = null, $flow, $authUser, ?array $filtered = [])
    {

        $data = Expense::select(
            'expenses.id as id',
            'lines.name as line',
            'lines.id as line_id',
            'users.fullname as user',
            'users.emp_code as code',
            // DB::raw('IFNULL(group_concat(distinct crm_products.name),"") as product'),
            DB::raw('IFNULL(group_concat(distinct crm_expense_details.date),"") as date'),
            DB::raw('IFNULL(group_concat(distinct crm_expense_details.type_id),"") as type_id'),
            DB::raw('IFNULL(group_concat(distinct crm_expense_types.name),"") as type'),
            DB::raw('IFNULL(sum(crm_expense_details.amount),"") as amount'),
            DB::raw('IFNULL(group_concat(distinct crm_line_divisions.name),"") as division'),
            DB::raw('IFNULL(group_concat(distinct crm_expense_details.description),"") as description'),

        )
            ->leftJoin('users', 'expenses.user_id', 'users.id')
            ->leftJoin('lines', 'expenses.line_id', 'lines.id')
            ->leftJoin('expense_details', function ($join) {
                $join->on('expenses.id', 'expense_details.expense_id');
            })
            ->leftJoin('expense_types', 'expense_details.type_id', 'expense_types.id')
            ->leftJoin('expense_divisions', 'expenses.id', 'expense_divisions.expense_id')
            // ->leftJoin('expense_products', 'expenses.id', 'expense_products.expense_id')
            // ->leftJoin('products', 'expense_products.product_id', 'products.id')
            ->leftJoin('line_divisions', 'expense_divisions.div_id', 'line_divisions.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('expenses.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', Expense::class);
                }
            )
            ->where('expenses.deleted_at', '=', null)
            // ->whereBetween(DB::raw("(DATE_FORMAT(crm_expenses.date,'%Y-%m-%d'))"), [$from, $to]);
            ->whereBetween('expenses.date', [$from, $to]);
        if ($object) {
            $data = $data->where($table, $object?->id);
        }
        // if (!empty($expense['products'])) {
        //     $data = $data->whereIn('expense_products.product_id', $expense['products']);
        // }
        if (!empty($filtered)) {
            $data = $data->whereIn('users.id', $filtered);
        }
        $data = match ($expense['approval']) {
            1 => $data->whereNull('plan_visit_details.approval'),
            2 => $data->where('plan_visit_details.approval', 1),
            3 => $data->where('plan_visit_details.approval', 0),
            4 => $data->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null)),
            5 => $data,
        };

        $required = 0;
        $dataFlow = '';
        $scanLevel = 1;
        $data = $data->groupBy("id")->get();
        $data = $data->filter(
            function (Expense $expense) use ($authLines, $linesAapprovables, $scanLevel, $flow, $authUser, &$required, $dataFlow, $from, $to) {
                if (!$authUser->hasRole('admin') && !$authUser->hasRole('sub admin') || $authUser->hasRole('Gemstone Admin')) {
                    if (
                        $expense->approvals?->approvalFlows()->where('user_id', $authUser->id)?->first() == null
                    ) {
                        if (!$flow) {
                            return is_null($expense->approvals?->approval);
                        } else {
                            if (!isNullable($expense->approvals)) {
                                $flows = $expense->approvals;
                                $disapproved = $flows->approvalFlows()->where('approval', 0)->first();
                                if ($disapproved) return;
                                $approvablesCountOnThisShit = $flows->approvalFlows()->count();
                                $expense = Expense::find($expense->id);
                                $lines = $expense->line()->get();
                                $data = $authUser->approvalWidget($expense, $authUser, Expense::class, $from, $to, $lines, $linesAapprovables, $authLines);
                                $dataFlow = $data['linesDataFlow'];
                                $currentFlow = $dataFlow?->flow;
                                $required = $dataFlow?->required;
                                $vacantCount = $data['vacantCount'];
                                $haveToApprove = $approvablesCountOnThisShit === $currentFlow - ($vacantCount + $scanLevel);
                                return $haveToApprove;
                            }
                        }
                    } else {
                        return $expense;
                    }
                } else {
                    return $expense;
                }
            }
        )->values();
        // }

        return array('expenses' => $data, 'is_required' => $required);
    }
    public function getUsers(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $users = collect([]);
        $lines = Line::select('id', 'name')->get();
        $from = $request->from ? Carbon::parse($request->from)->startOfDay() : Carbon::now();
        $to = $request->to ? Carbon::parse($request->to)->endOfDay() : Carbon::now();

        if ($request->filter == 1) {
            $positions = UserPosition::with('user')->where('user_positions.from_date', '<=', $from?->toDateString())
                ->where(
                    fn($q) => $q->where('user_positions.to_date', '>=', $to?->toDateString())
                        ->orWhere('user_positions.to_date', null)
                )->get()->map(function ($userPosition) {
                    return [
                        'id' => $userPosition->user?->id,
                        'fullname' => $userPosition->user?->fullname,
                    ];
                });
            $users = collect($positions)->merge(
                LineDivision::where('division_type_id', 1)->where('from_date', '<=', $from?->toDateString())->whereNull('deleted_at')
                    ->where(fn($q) => $q->where('to_date', '=', null)->orWhere('to_date', '>=', $to?->toDateString()))->get()
                    ->map(function ($division) use ($from, $to) {
                        $user = $division->users($from, $to)?->first();
                        return [
                            'id' => $user?->id,
                            'fullname' => $user?->fullname,
                        ];
                    })
            )
                ->filter(fn($user) => $user['id'] != null);
        }
        if ($request->filter == 3) {
            $positions = UserPosition::with('user')->where('user_positions.from_date', '<=', $from?->toDateString())
                ->where(
                    fn($q) => $q->where('user_positions.to_date', '>=', $to?->toDateString())
                        ->orWhere('user_positions.to_date', null)
                )->get()->pluck('user');
            $lines = Line::select('id', 'name')->whereIntegerInRaw('id', $request->lines)->get();
            foreach ($lines as $line) {
                $divisions = $line->divisions($from, $to)->orderBy('division_type_id', 'ASC')->get();
                $users = $users->merge(LineDivisionUser::whereIntegerInRaw('line_division_id', $divisions)
                    ->with('user')->get()->pluck('user'));
            }
            $users = $users->merge($positions);
        }
        if ($request->filter == 2) {
            $users = $user->belowUsersOfAllLinesWithPositions(lines: $lines, from: $from, to: $to);
        }
        return $this->respond($users->unique('id')->values());
    }
    public  function lines(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = Line::select('id', 'name')->get();
        $from = Carbon::parse($request->from)->startOfDay();
        $to = Carbon::parse($request->to)->endOfDay();
        $users = $user->belowUsersOfAllLinesWithPositions(lines: $lines, from: $from, to: $to);
        return response()->json([
            'lines' => $lines,
            'users' => $users
        ]);
    }
    public function getLineData(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $from = $request->from ? Carbon::parse($request->from)->startOfDay() : Carbon::now();
        $to = $request->to ? Carbon::parse($request->to)->endOfDay() : Carbon::now();
        $lines = Line::whereIntegerInRaw('id', $request->lines)->get();
        $users = $user->belowUsersOfAllLinesWithPositions(lines: $lines, from: $from, to: $to);
        return response()->json([
            'users' => $users,
        ]);
    }
    public function filter(Request $request)
    {
        // throw new CrmException('hello');
        $withFlow = ApprovalSetting::where('key', 'expense_approval_center_flow')->value('value') == 'Yes';
        /**@var User authUser */
        $authUser = Auth::user();
        $expense = $request->expenseFilter;
        $from = Carbon::parse($expense['fromDate'])->startOfDay();
        $to = Carbon::parse($expense['toDate'])->endOfDay();
        $approvalData = $authUser->userApprovals($from, $to);
        $authUserLines = collect($approvalData['lines'])->pluck('id')->toArray();
        $linesAapprovables = $approvalData['linesAapprovables'];
        $fields = collect(['line', 'division', 'employee', 'emp_code', 'total_number', 'total_amount', 'status', 'edited_by']);
        $clickable_fields = collect([]);
        $clickable_fields = $clickable_fields->merge(['total_number', 'total_amount']);

        $filtered = new Collection([]);
        $data = new Collection([]);
        $users = collect([]);
        $lines = Line::when(!empty($expense['lines']), fn($q) => $q->whereIntegerInRaw("lines.id", $expense['lines']))->get();
        foreach ($lines as $line) {
            if ($expense['filter'] == 2) {
                $users = $line->users($from, $to)->whereIn("line_users.user_id", $expense['users'])->get();
                $filtered = $filtered->merge($authUser->filterUsers($line, $users, $expense, $from, $to));
            }
            if ($expense['filter'] == 3) {
                $filtered = User::whereIntegerInRaw('id', $expense['users'])->get();
            }
            if ($expense['filter'] == 2) {
                $users = collect(UserPosition::with('user')->get()->map(function ($userPosition) {
                    return [
                        'id' => $userPosition->user?->id,
                        'fullname' => $userPosition->user?->fullname,
                    ];
                }))->merge(LineDivision::where('division_type_id', 1)->where('from_date', '<=', (string)Carbon::now())->whereNull('deleted_at')
                    ->where(fn($q) => $q->where('to_date', '=', null)->orWhere('to_date', '>=', (string)Carbon::now()))->get()->map(function ($division) use ($from, $to) {
                        return [
                            'id' => $division->users($from, $to)?->first()?->id,
                            'fullname' => $division->users($from, $to)?->first()?->fullname,
                        ];
                    }))->filter(fn($user) => $user['id'] != null)->values();
                // throw new CrmException($users);
                $filtered = empty($expense['users']) ? $users : User::whereIn('id', $expense['users'])->get();
            }
        }
        $line = count($lines->pluck('name')) > 3 ? '' : $lines->pluck('name')->implode(', ');
        $data = $this->statistics($filtered->unique('id')->values(), $expense, $from, $to, $withFlow, $authUser, $authUserLines, $linesAapprovables);
        $results = $data['results'];
        $expenses = $data['expenses'];
        // throw new CrmException([
        //     'data' => $results->unique("id")->values(),
        //     'expenses' => $expenses,
        //     'users' => $filtered->unique('id')->values(),
        //     'fields' => $fields,
        //     'clickable_fields' => $clickable_fields,
        //     'line' => $line
        // ]);
        return $this->respond([
            'data' => $results->unique("id")->values(),
            'expenses' => $expenses,
            'users' => $filtered->unique('id')->values(),
            'fields' => $fields,
            'clickable_fields' => $clickable_fields,
            'line' => $line
        ]);
    }
    private function statistics($objects, $expense, $from, $to, $flow, $authUser, $authUserLines, $linesAapprovables)
    {
        $lines = [];
        $totalData = collect([]);
        $results = collect([]);
        $cloneExpenses = collect([]);
        foreach ($objects as $object) {
            $totalNumbers = 0;
            $totalAmounts = 0;
            $object = User::find($object['id']);
            $lines = $object->hasPosition() ? $object->userPosition()->first()->lines->pluck('id')->toArray() : $object->lines($from, $to)->get()->pluck('id')->toArray();
            $data = collect([
                'id' => $object->id,
                'line' => $object->lines($from, $to)->whereIntegerInRaw('line_users.line_id', $lines)->pluck('name')->implode(','),
                'division' =>  $object->divisions($from, $to)->whereIntegerInRaw('line_divisions.line_id', $lines)->pluck('name')->implode(','),
                'employee' => $object?->fullname,
                'emp_code' => $object?->emp_code ?? '',
                'color' => $object->divisions($from, $to)?->first()?->DivisionType->color,
            ]);
            $status = 1;
            $mainData = $this->getExpenses($authUserLines, $linesAapprovables, $expense, $object, 'users.id', $from, $to, null, $flow, $authUser);
            $cloneExpenses = $cloneExpenses->merge(collect($mainData['expenses']));
            $expenses = collect($mainData['expenses'])
                ->each(function ($expense) use (&$status, &$totalAmounts, $from, $to) {
                    $totalAmounts += $expense->details()
                        ->whereDate('date', '<=', $to)
                        ->sum('amount');
                    $approval = $this->getStatus($expense->id);
                    if ($approval != 1)
                        $status++;
                });
            $totalNumbers = $expenses->unique('id')->count();
            $data->put('total_number', $totalNumbers);
            $data->put('total_amount', round($totalAmounts, 2));
            if ($totalData->has('total_numbers')) {
                $totalData->transform(function ($item, $key) use ($totalNumbers) {
                    if ($key == 'total_numbers') {
                        return round(($item + $totalNumbers), 2);
                    } else {
                        return round($item, 2);
                    }
                });
            } else {
                $totalData->put('total_numbers', round($totalNumbers, 2) ?? 0);
            }

            if ($totalData->has('total_amount')) {
                $totalData->transform(function ($item, $key) use ($totalAmounts) {
                    if ($key == 'total_amount') {
                        return round(($item + $totalAmounts), 2);
                    } else {
                        return round($item, 2);
                    }
                });
            } else {
                $totalData->put('total_amount', round($totalAmounts, 2) ?? 0);
            }
            $data->put('colorAmount', $status);
            $results =  $results->push($data);
        }
        $results = $results->push(
            collect([
                'id' => '',
                'line' => '',
                'division' => 'Total',
                'employee' => 'Expenses',
                'emp_code' => '',
                'status' => '',
                'total_number' => $totalData->get('total_numbers'),
                'total_amount' => $totalData->get('total_amount'),

            ])
        );
        return array('results' => $results, 'expenses' => $cloneExpenses);
    }
    public function showData(Request $request)
    {
        $expense = $request->listFilter;
        $column = $request->column;
        $from = Carbon::parse($expense['fromDate'])->startOfDay();
        $to = Carbon::parse($expense['toDate'])->endOfDay();

        $object = '';
        $expenses = collect([]);

        $object = User::find($request->user);

        $data = null;
        /**@var User */
        $authUser = Auth::user();
        $approvalData = $authUser->userApprovals($from, $to);
        $authUserLines = collect($approvalData['lines'])->pluck('id')->toArray();
        $linesAapprovables = $approvalData['linesAapprovables'];
        $flow = ApprovalSetting::where('key', 'expense_approval_center_flow')->value('value') == 'Yes';

        if ($column == 'total_number') {
            $mainData = $this->getExpenses($authUserLines, $linesAapprovables, $expense, $object, 'users.id', $from, $to, null, $flow, $authUser);
            $expenses = collect($mainData['expenses']);
            $expenseIds = $expenses->pluck('id')->implode(' , ');
            $user = $expenses[0]->user;
            $lines = $expenses[0]->line;
            $data = $expenses->values()->map(function ($expense) {
                return [
                    'id' => $expense->id,
                    'line' => $expense->line,
                    'division' => $expense->division,
                    'employee' => $expense->user,
                    'type' => $expense->type ?? '',
                    'product' => $expense->product ?? '',
                    'description' => $expense->description ?? '',
                    'amount' => $expense->amount,
                    'status' => $this->getStatus($expense->id),
                    'feedback' => $this->getFeedbacks($expense->id),
                ];
            });
            $fields = ['id', 'line', 'division', 'employee', 'product', 'type', 'description', 'status', 'feedback'];
            return $this->respond(['data' => $data, 'expenseIds' => $expenseIds, 'user' => $user, 'lines' => $lines, 'fields' => $fields]);
        }
        if ($column == 'total_amount') {
            $data = collect([]);
            $mainData = [];
            $mainData = $this->getExpenses($authUserLines, $linesAapprovables, $expense, $object, 'users.id', $from, $to, null, $flow, $authUser);
            $expenses = collect($mainData['expenses']);
            $is_required = $mainData['is_required'];
            $expenseIds = $expenses->pluck('id')->implode(' , ');
            $user = $expenses->first()?->user;
            $code = $expenses->first()?->code;
            $lines = $expenses->first()?->line;
            // $expenses = $expenses;
            $ids = $expenses->pluck('id');
            $details = ExpenseDetails::whereIntegerInRaw('expense_id', $ids)->orderBy('date', 'ASC')->get();
            $totalAmount = 0;
            $data = $details->map(function ($detail) use ($details, &$totalAmount) {
                $totalAmount += $detail->amount;
                $user = $detail->expense?->user;
                $brick = ActualVisit::where('user_id', $user->id)
                    ->whereDate('visit_date', Carbon::parse($detail->date)->toDateString())->first()?->brick?->name;
                $amount = floatval($detail->amount);
                return [
                    'ex_id' => $detail->expense?->id,
                    'id' => $detail->id,
                    'line' => $detail->expense?->line?->name,
                    'employee' => $user?->fullname,
                    'code' => $user?->emp_code,
                    'date' => Carbon::parse($detail->date)->toDateString() ?? '',
                    'type' => $detail->type?->name ?? '',
                    'color' => $this->getColor($details, $detail) > 1 ? '#c0392b' : '',
                    'from' => $detail->location?->source?->name ?? '',
                    'to' => $detail->location?->destination?->name ?? '',
                    'distance' => $detail->distance ?? '',
                    'value' => $detail->location?->price ?? '',
                    'meal' => $detail->meal->name ?? '',
                    'meal_price' => $detail->meal->price ?? '',
                    'desc' => $detail->description ?? '',
                    'file' => Attachment::where('attachable_id', $detail->id)
                        ->where('attachable_type', ExpenseDetails::class)->get()->pluck('url') ?? '',
                    'amount' => $amount,
                    'edited_by' => $detail->users?->first(),
                    'status' => $detail->expense->approvals?->approval,
                    'brick' => $brick ?? '',
                    'actions' => ''
                ];
            });
            $fields = collect([
                'ex_id',
                'date',
                'type',
                'from',
                'to',
                'distance',
                'value',
                'meal',
                'meal_price',
                'desc',
                'file',
                'amount',
                'brick',
                'status',
                'actions'
            ]);
            return $this->respond(['data' => $data, 'expenseIds' => $expenseIds, 'user' => $user, 'code' => $code, 'lines' => $lines, 'fields' => $fields, 'total' => $totalAmount, 'is_required' => $is_required]);
        }
        // }
    }
    private function getColor($details, $detail)
    {
        $count = 0;
        $details->each(function ($record) use ($detail, &$count) {
            $date = Carbon::parse($detail->date)->toDateString();
            $recordDate = Carbon::parse($record->date)->toDateString();
            $typeDetail = $detail->type?->id;
            $typeRecord = $record->type?->id;
            if ($date == $recordDate && $typeDetail == $typeRecord) {
                $count++;
            }
            // $color = $count > 1 ? 'red' : '';
        });
        return $count;
    }
    private function getStatus($id)
    {
        $expense = Expense::find($id);
        if ($expense->approvals?->approval === null) return null;
        if ($expense->approvals?->approval === 1) return 1;
        if ($expense->approvals?->approval === 0) return 0;
    }
    private function getFeedbacks($id)
    {
        $expense = Expense::find($id);
        if (count($expense->feedbacks) > 0)  return $expense->feedbacks->pluck('feedback');
        else return '';
    }

    public function getExpenseDetails(ExpenseDetails $detail)
    {
        /**@var User */
        $user = User::find($detail->expense?->user_id);
        $role = explode(',', $user->menuroles)[0];
        $roleId = Role::where('name', $role)->value('id');
        $date = Carbon::parse($detail->date)->format('Y-m');
        $meals = Meal::where(DB::raw("(DATE_FORMAT(from_date,'%Y-%m'))"), '<=', $date)
            ->where(fn($q) => $q->where(DB::raw("(DATE_FORMAT(to_date,'%Y-%m'))"), '>=', $date)->orWhere('to_date', '=', null))
            ->where('role_id', $roleId)->get();
        $divisions = ExpenseLocationSetting::select('id', 'name')->get();
        $types = ExpenseType::select('id', 'name')->get();
        $authUser = Auth::user();
        $details = collect([$detail])->map(function ($detail) use ($authUser) {
            return [
                'id' => $detail->id,
                'amount' => number_format($detail->amount, 2),
                'date' => Carbon::parse($detail->date)->toDateString(),
                'description' => $detail->description,
                'type' => $detail->type->name,
                'from' => $detail->location?->source?->name,
                'to' => $detail->location?->destination?->name,
                'km' => $detail->location?->kilo_meter,
                'km_price' => $detail->location?->price,
                'meal' => $detail->meal,
                'meal_price' => $detail->meal?->price,
                'is_location' => $detail->type->is_location,
                'edit_notes' => $detail->editExpenseDetail()->where('user_id', $authUser->id)->first()?->notes,
            ];
        })->collect();
        return response()->json([
            'detail' => $details->first(),
            'meals' => $meals,
            'types' => $types,
            'divisions' => $divisions,
        ]);
    }
    public function updateDetails(Request $request, ExpenseDetails $detail)
    {
        // throw new CrmException($request->all());
        $detailRequest = $request->detail;
        $expense = $detail->expense;
        $user = Auth::user();
        DB::transaction(function () use ($expense, $detailRequest, $detail, $user) {
            if ($detailRequest['is_location'] == 1) {
                $detail->update([
                    'amount' => $detailRequest['amount'],
                    'meal_id' => $detailRequest['meal'] ? $detailRequest['meal']['id'] : null,
                ]);
            } else {
                $detail->update([
                    'amount' => $detailRequest['amount'],
                ]);
            }

            // if ($detailRequest['edit_notes']) {
            $exists = EditExpenseDetail::where('user_id', $user->id)
                ->where('expense_detail_id', $detailRequest['id'])->first();
            if ($exists) {
                $exists->update(['notes' => $detailRequest['edit_notes']]);
            } else {
                EditExpenseDetail::create([
                    'expense_detail_id' => $detailRequest['id'],
                    'user_id' => $user->id,
                    'notes' => $detailRequest['edit_notes'],
                ]);
            }
            // }
            $totalAmount = 0;
            $expenseDetails = $expense->details;
            foreach ($expenseDetails as $expenseDetail) {
                $totalAmount += $expenseDetail->amount;
            }
            $expense->update(['amount' => $totalAmount]);
        });
        $model_id = $expense->id;
        $model_type = Expense::class;
        LogActivity::addLog($model_id, $model_type);
        return $this->respondSuccess();
    }
    public function deleteExpenseDetail(ExpenseDetails $expenseDetail)
    {
        // throw new CrmException($expenseDetail->attachments);
        $expense = $expenseDetail->expense;
        if ($expenseDetail) {
            $expenseDetail->editExpenseDetail()->forceDelete();
            $expenseDetail->attachments()->forceDelete();
            $expenseDetail->forceDelete();
        }
        if (count($expense->details) == 0) {
            $expense->divisions()->forceDelete();
            $expense->products()->forceDelete();
            $expense->attachments()->forceDelete();
            $expense->forceDelete();
        }

        return $this->respondSuccess();
    }
    public function editedUsers(Request $request)
    {
        $expense = $request->listFilter;
        // $column = $request->column;
        $from = Carbon::parse($expense['fromDate'])->startOfDay();
        $to = Carbon::parse($expense['toDate'])->endOfDay();
        /**@var User */
        $authUser = Auth::user();
        $flow = ApprovalSetting::where('key', 'expense_approval_center_flow')->value('value') == 'Yes';
        $object = '';
        $expenses = collect([]);

        $object = User::find($request->user);
        $approvalData = $authUser->userApprovals($from, $to);
        $authUserLines = collect($approvalData['lines'])->pluck('id')->toArray();
        $linesAapprovables = $approvalData['linesAapprovables'];
        $mainData = $this->getExpenses($authUserLines, $linesAapprovables, $expense, $object, 'users.id', $from, $to, null, $flow, $authUser);
        $expenses = collect($mainData['expenses']);
        // throw new CrmException($expenses);
        $expenseIds = $expenses->pluck('id')->implode(' , ');
        $data = collect([]);
        $user =  $expenses->first()?->user;
        $lines = $expenses->first()?->line;
        $totalAmount = 0;
        $expenses->values()->each(function ($expense) use (&$data, $from, $to, &$totalAmount) {
            $expense = Expense::find($expense->id);
            $data = $data->merge($expense->details()->whereBetween('date', [$from, $to])->orderBy('date', 'ASC')->get()->map(function ($detail) use (&$totalAmount) {
                $totalAmount += $detail->amount;
                return [
                    'id' => $detail->id,
                    'date' => Carbon::parse($detail->date)->toDateString() ?? '',
                    'type' => $detail->type?->name ?? '',
                    'from' => $detail->location?->source?->name ?? '',
                    'to' => $detail->location?->destination?->name ?? '',
                    'distance' => $detail->distance ?? '',
                    'value' => $detail->location?->price ?? '',
                    'meal' => $detail->meal->name ?? '',
                    'meal_price' => $detail->meal->price ?? '',
                    'desc' => $detail->description ?? '',
                    'amount' => $detail->amount ?? '',
                    'edited_by' => $detail->users->pluck('fullname')->implode(' , ')
                ];
            }));
        });
        return $this->respond(['data' => $data, 'expenseIds' => $expenseIds, 'user' => $user, 'lines' => $lines, 'totalAmount' => $totalAmount]);
    }
    public function editedUsersNotes(ExpenseDetails $detail)
    {
        $data = $detail->editExpenseDetail()->get()->map(function ($editedBy) use ($detail) {
            return [
                'id' => $detail->id,
                'date' => Carbon::parse($detail->date)->toDateString() ?? '',
                'type' => $detail->type?->name ?? '',
                'from' => $detail->location?->source?->name ?? '',
                'to' => $detail->location?->destination?->name ?? '',
                'distance' => $detail->distance ?? '',
                'value' => $detail->location?->price ?? '',
                'meal' => $detail->meal->name ?? '',
                'meal_price' => $detail->meal->price ?? '',
                'desc' => $detail->description ?? '',
                'amount' => $detail->amount ?? '',
                'edited_by' => $editedBy->user?->fullname,
                'comment' => $editedBy->notes
            ];
        });
        return $this->respond($data);
    }
    public function ApproveExpenses(Request $request)
    {
        foreach ($request->details as $detail) {
            $expenseDetail = ExpenseDetails::find($detail['id']);
            $expense = $expenseDetail->expense;
            if ($expense->approvals->approval == null) {
                ApprovalFlowUser::firstOrCreate([
                    'detail_id' => $expense->approvals->id,
                    'user_id' => Auth::id(),
                    'approval' => 1
                ]);
            }
            // if ($request->is_required == 1) 
            $expense->approvals->update(['approval' => 1, 'user_id' => Auth::id()]);
        }
        return $this->respondSuccess();
    }
    public function disApproveExpenses(Request $request)
    {
        // throw new CrmException($request->all());
        foreach ($request->details as $detail) {
            $expenseDetail = ExpenseDetails::find($detail['id']);
            $expense = $expenseDetail->expense;
            if ($expense->approvals->approval == null) {
                ApprovalFlowUser::firstOrCreate([
                    'detail_id' => $expense->approvals->id,
                    'user_id' => Auth::id(),
                    'approval' => 0
                ]);
            }
            // if ($request->is_required == 1) 
            $expense->approvals->update(['approval' => 0, 'user_id' => Auth::id()]);
        }
        return $this->respondSuccess();
    }

    public function showExpensesApprovals(Request $request)
    {
        $expense = $request->listFilter;
        $column = $request->column;
        $from = Carbon::parse($expense['fromDate'])->startOfDay();
        $to = Carbon::parse($expense['toDate'])->endOfDay();

        $object = '';
        if ($request->div != null) {
            $object = LineDivision::find($request->div);
        } else {
            $object = User::find($request->user);
        }
        /**@var User */
        $authUser = Auth::user();
        $approvalData = $authUser->userApprovals($from, $to);
        $authUserLines = collect($approvalData['lines'])->pluck('id')->toArray();
        $linesAapprovables = $approvalData['linesAapprovables'];
        $flow = ApprovalSetting::where('key', 'expense_approval_center_flow')->value('value') == 'Yes';
        $mainData = $this->getExpenses($authUserLines, $linesAapprovables, $expense, $object, 'users.id', $from, $to, null, $flow, $authUser);
        $expenses = collect($mainData['expenses']);
        $expenses = $expenses->map(function ($expense) {
            return [
                'id' => $expense->id,
                'line' => $expense->line,
                'employee' => $expense->user,
                'date' => Carbon::parse($expense->details->first()->date)->format('Y-m'),
                'approvals' => '',
            ];
        });
        return $this->respond($expenses);
    }
    public function printExpenses(Request $request)
    {
        $expense = $request->listFilter;
        $column = $request->column;
        $from = Carbon::parse($expense['fromDate'])->startOfDay();
        $to = Carbon::parse($expense['toDate'])->endOfDay();
        $object = '';
        $expenses = collect([]);

        $object = User::find($request->user);

        $data = null;
        /**@var User */
        $authUser = Auth::user();
        $approvalData = $authUser->userApprovals($from, $to);
        $authUserLines = collect($approvalData['lines'])->pluck('id')->toArray();
        $linesAapprovables = $approvalData['linesAapprovables'];
        $flow = ApprovalSetting::where('key', 'expense_approval_center_flow')->value('value') == 'Yes';
        $mainData = [];
        if ($object)
            $mainData = $this->getExpenses($authUserLines, $linesAapprovables, $expense, $object, 'users.id', $from, $to, null, $flow, $authUser);
        else {
            $filtered = new Collection();
            $lines = Line::when(!empty($expense['lines']), fn($q) => $q->whereIntegerInRaw("lines.id", $expense['lines']))->get();
            foreach ($lines as $line) {
                $users = $line->users()->whereIn("line_users.user_id", $expense['users'])->get();
                $filtered = $filtered->merge($authUser->filterUsers($line, $users, $expense));
            }
            $filtered = $filtered->pluck('id')->unique()->toArray();
            $mainData = $this->getExpenses($authUserLines, $linesAapprovables, $expense, $object, 'users.id', $from, $to, null, $flow, $authUser, $filtered);
        }
        $expenses = collect($mainData['expenses']);
        $is_required = $mainData['is_required'];
        $expenseIds = $expenses->pluck('id')->implode(' , ');
        $user = $expenses->first()?->user;
        $code = $expenses->first()?->code;
        $lines = $expenses->first()?->line;
        // $expenses = $expenses;
        $ids = $expenses->pluck('id');
        $details = ExpenseDetails::whereIntegerInRaw('expense_id', $ids)->whereBetween('date', [$from, $to])->orderBy('date', 'ASC')->get();
        $totalAmount = 0;
        $data = $details->map(function ($detail) use ($details, &$totalAmount) {
            $totalAmount += $detail->amount;
            $user = $detail->expense?->user;
            $amount = floatval($detail->amount);
            return [
                'ex_id' => $detail->expense?->id,
                'id' => $detail->id,
                'line' => $detail->expense?->line?->name,
                'employee' => $user?->fullname,
                'code' => $user?->emp_code,
                'date' => Carbon::parse($detail->date)->toDateString() ?? '',
                'type' => $detail->type?->name ?? '',
                'color' => $this->getColor($details, $detail) > 1 ? '#c0392b' : '',
                'from' => $detail->location?->source?->name ?? '',
                'to' => $detail->location?->destination?->name ?? '',
                'distance' => $detail->distance ?? '',
                'value' => $detail->location?->price ?? '',
                'meal' => $detail->meal->name ?? '',
                'meal_price' => $detail->meal->price ?? '',
                'desc' => $detail->description ?? '',
                'file' => Attachment::where('attachable_id', $detail->id)
                    ->where('attachable_type', ExpenseDetails::class)->get()->pluck('url') ?? '',
                'amount' => $amount,
                'edited_by' => $detail->users?->first(),
                'status' => $detail->expense->approvals?->approval,
                'actions' => ''
            ];
        });
        $fields = collect(['ex_id', 'date', 'type', 'from', 'to', 'distance', 'value', 'meal', 'meal_price', 'desc', 'amount', 'status']);
        return $this->respond(['data' => $data, 'expenseIds' => $expenseIds, 'user' => $user, 'code' => $code, 'lines' => $lines, 'fields' => $fields, 'total' => $totalAmount, 'is_required' => $is_required]);
    }

    public function getTotalAmountExpenses(Request $request)
    {
        $expense = $request->listFilter;
        // $column = $request->column;
        // $from = Carbon::parse($expense['fromDate'])->startOfDay();
        $to = Carbon::parse($expense['toDate'])->endOfDay();

        // $expenses = collect([]);

        // $objects = collect($request->users)->pluck('id')->toArray();
        $data = null;
        /**@var User */
        // $expenses = (new ExpenseService())->getExpenses($expense, $objects, 'users.id', $from, $to);
        $details = ExpenseDetails::whereIntegerInRaw('expense_id', collect($request->expenses)->pluck('id')->toArray())
            ->whereDate('date', '<=', $to)
            // ->whereBetween('date', [$from, $to])
            ->orderBy('date', 'ASC')->get();
        // $details = ExpenseDetails::select(
        //     'expenses.id as ex_id',
        //     'expense_details.id as id',
        //     'expense_details.description as desc',
        //     DB::raw('DATE_FORMAT(crm_expense_details.date, "%Y-%m-%d") as date'),
        //     'lines.name as line',
        //     'employee.fullname as employee',
        //     'source.name as from',
        //     'destination.name as to',
        //     'expense_location_prices.kilo_meter as distance',
        //     'expense_location_prices.price as price',
        //     'meals.name as meal',
        //     'meals.price as meal_price',
        //     // 'attachments.path as file',
        //     'plan_visit_details.approval as status',
        //     DB::raw('IFNULL(sum(crm_expense_details.amount),"") as amount'),
        //     // DB::raw('IFNULL(group_concat(distinct attachments.path),"") as file'),

        // )
        //     ->leftJoin('expenses', 'expense_details.expense_id', 'expenses.id')
        //     ->leftJoin('lines', 'expenses.line_id', 'lines.id')
        //     ->leftJoin('users as employee', 'expenses.user_id', 'employee.id')
        //     ->leftJoin('expense_location_prices', 'expense_details.expense_price_id', 'expense_location_prices.id')
        //     ->leftJoin('expense_location_settings as source', 'expense_location_prices.source_id', 'source.id')
        //     ->leftJoin('expense_location_settings as destination', 'expense_location_prices.destination_id', 'destination.id')
        //     ->leftJoin('meals', 'expense_details.meal_id', 'meals.id')
        //     ->leftJoin('edit_expense_details', 'expense_details.id', 'edit_expense_details.expense_detail_id')
        //     ->leftJoin('users as edited', 'edit_expense_details.user_id', 'edited.id')

        //     ->leftJoin(
        //         'attachments',
        //         function ($join) {
        //             $join->on('expenses.id', '=', 'attachments.attachable_id');
        //             $join->where('attachments.attachable_type', ExpenseDetails::class);
        //         }
        //     )
        //     ->leftJoin(
        //         'plan_visit_details',
        //         function ($join) {
        //             $join->on('expenses.id', '=', 'plan_visit_details.visitable_id');
        //             $join->where('plan_visit_details.visitable_type', Expense::class);
        //         }
        //     )
        //     ->whereIntegerInRaw('expenses.id', $expenses->pluck('id'))
        //     ->whereBetween('expense_details.date', [$from, $to])
        //     ->orderBy('expense_details.date', 'ASC')
        //     ->groupBy('id', 'plan_visit_details.approval')
        //     ->get();
        $totalAmount = 0;

        $data = $details->map(function ($detail) use ($details, &$totalAmount) {
            $totalAmount += $detail->amount;
            $user = $detail->expense?->user;
            $amount = floatval($detail->amount);
            return [
                'ex_id' => $detail->expense?->id,
                'id' => $detail->id,
                'line' => $detail->expense?->line?->name,
                'employee' => $user?->fullname,
                'code' => $user?->emp_code,
                'date' => Carbon::parse($detail->date)->toDateString() ?? '',
                'type' => $detail->type?->name ?? '',
                // 'color' => $this->getColor($details, $detail) > 1 ? '#c0392b' : '',
                'from' => $detail->location?->source?->name ?? '',
                'to' => $detail->location?->destination?->name ?? '',
                'distance' => $detail->distance ?? '',
                'value' => $detail->location?->price ?? '',
                'meal' => $detail->meal->name ?? '',
                'meal_price' => $detail->meal->price ?? '',
                'desc' => $detail->description ?? '',
                'file' => Attachment::where('attachable_id', $detail->id)
                    ->where('attachable_type', ExpenseDetails::class)->get()->pluck('url') ?? '',
                'amount' => $amount,
                'edited_by' => $detail->users?->first(),
                'status' => $detail->expense->approvals?->approval,
                'actions' => ''
            ];
        });
        // throw new CrmException($data);
        $fields = collect(['ex_id', 'date', 'type', 'from', 'to', 'distance', 'value', 'meal', 'meal_price', 'desc', 'file', 'amount', 'status', 'actions']);
        return $this->respond(['data' => $data, 'total' => $totalAmount,  'fields' => $fields]);
    }
    public function getApprovalUser(Request $request)
    {
        $expense = Expense::find($request->id);
        $approvalFlow = $expense->approvals?->approvalFlows()->count();
        $data = collect([]);
        if ($approvalFlow > 0) {
            $data = $data->merge($expense->approvals?->approvalFlows()->get());
        } else {
            $data = $data->push($expense->approvals);
        }
        $data = $data->map(function ($detail) use ($expense) {
            return [
                'id' => $detail?->id ?? null,
                'expense_id' => $expense?->id ?? null,
                'status' => $detail?->approval ?? null,
                'reason' => $detail?->approval == 0 ? $expense->reasons->first()?->reason : "",
                'approved_by' => $detail?->user?->fullname ?? '',
                'position' => $detail?->user?->menuroles ?? '',
                'date' => is_null($detail?->approval) ? "" : Carbon::parse($detail?->updated_at)->toDateString(),
                'actions' => ''

            ];
        });
        $mainApproval = $expense->approvals?->approval;
        $fields = ['id', 'expense_id', 'status', 'reason', 'approved_by', 'position', 'date', 'actions', 'delete'];
        return $this->respond(['approvals' => $data, 'fields' => $fields, 'mainApproval' => $mainApproval]);
    }
    public function updateApprovalUser(Request $request)
    {
        $approvals = $request->approvals;
        $expense = Expense::find($approvals[0]['expense_id']);
        $approvalFlows = $expense->approvals?->approvalFlows->count();
        if ($approvalFlows > 0) {
            foreach ($approvals as $approval) {
                ApprovalFlowUser::find($approval['id'])->update(['approval' => $approval['status']]);
            }
        }
        // if ($request->mainApproval !== null) {
        $expense->approvals()->update(['approval' => $request->mainApproval]);
        // }
        return $this->respondSuccess();
    }

    public function deleteApprovalUser($id)
    {
        // throw new CrmException(ApprovalFlowUser::find($id));
        $model_id = $id;
        $model_type = ApprovalFlowUser::class;
        LogActivity::addLog($model_id, $model_type);
        ApprovalFlowUser::find($id)->delete();
        return $this->respondSuccess();
    }
}
