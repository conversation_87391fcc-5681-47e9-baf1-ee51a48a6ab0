<?php

namespace App\Http\Controllers;

use App\DivisionType;
use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Http\Controllers\Controller;
use App\Line;
use App\Models\FieldDayKpi;
use App\Models\FieldDayVisit;
use App\Position;
use App\Shift;
use App\VisitType;
use Illuminate\Http\Request;

class FieldDaysController extends ApiController
{
    public function index()
    {
        $fieldDays = FieldDayKpi::get()->map(function ($day) {
            return [
                'id' => $day->id,
                'line' => $day->line->name,
                'role' => $day->roleable_type == DivisionType::class ?
                    DivisionType::find($day->roleable_id)->name : Position::find($day->roleable_id)->name,
                'minimum' => $day->minimum,
            ];
        });
        LogActivity::addLog();
        return $this->respond($fieldDays);
    }
    public function getDetails(Request $request)
    {
        return $this->respond(FieldDayVisit::where('field_day_id', $request->id)->get()->map(function ($day) {
            return [
                'id' => $day->id,
                'visit_type' => $day->visitType->name,
                'shift' => $day->shift?->name ?? 'All',
                'count' => $day->count
            ];
        }));
    }
    public function show()
    {
        $lines = Line::select(['id', 'name'])->with('positions', 'divisionTypes')->get()->map(fn ($line) => [
            'id' => $line->id,
            'name' => $line->name,
            'roles' => collect([...$line->divisionTypes, ...$line->positions])
                ->map(fn ($role) => ['id' => $role->id . '_' . get_class($role), 'name' => $role->name])
        ]);
        $visitTypes = VisitType::select('id', 'name')->get();
        $shifts = Shift::select('id', 'name')->get();
        return $this->respond(['lines' => $lines, 'visitTypes' => $visitTypes, 'shifts' => $shifts]);
    }
    public function store(Request $request)
    {
        $fieldDay = FieldDayKpi::create([
            'line_id' => $request->line_id,
            'roleable_id' => $request->roleable_id,
            'roleable_type' => $request->roleable_type,
            'minimum' => $request->minimum,
        ]);
        if (!empty($request->types)) {
            foreach ($request->types as $type) {
                if ($type['count'] != 0) {
                    FieldDayVisit::create([
                        'field_day_id' => $fieldDay->id,
                        'visit_type_id' => $type['type_id'],
                        'shift_id' => null,
                        'count' => $type['count']
                    ]);
                } else {
                    foreach ($type['shifts'] as $shift) {
                        FieldDayVisit::create([
                            'field_day_id' => $fieldDay->id,
                            'visit_type_id' => $type['type_id'],
                            'shift_id' => $shift['shift_id'],
                            'count' => $shift['count']
                        ]);
                    }
                }
            }
        }
        $model_id = $fieldDay->id;
        $model_type = FieldDayKpi::class;

        LogActivity::addLog($model_id, $model_type);
        return $this->respondCreated();
    }

    public function destroy(FieldDayKpi $fieldDay)
    {
        $model_id = $fieldDay->id;
        $model_type = FieldDayKpi::class;

        if (!empty($fieldDay->fieldDayVisits)) {
            $fieldDay->delete();
        }
        $fieldDay->delete();
        LogActivity::addLog($model_id, $model_type);
        return $this->respondSuccess();
    }
}
