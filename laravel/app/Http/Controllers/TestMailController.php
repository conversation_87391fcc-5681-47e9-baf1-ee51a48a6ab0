<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Http\Controllers\Controller;
use App\Http\Requests\MailRequest;
use App\Mail\MyTestMail;
use App\Setting;
use App\Support;
use App\SupportAttachment;
use Barryvdh\DomPDF\PDF;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class TestMailController extends Controller
{
    public function sendEmail($id)
    {
        $details = Support::where('id', $id)->get()->map(function ($support) {
            return [
                'id' => $support->id,
                'type' => $support->supportTypes->name,
                'title' => $support->title,
                'brief' => $support->brief,
                'company' => $support->companies->name,
            ];
        })->first();
        $attachments = SupportAttachment::where('support_id', $id)->get();
        $users = ['<EMAIL>', '<EMAIL>'];
        $ccUsers = ['<EMAIL>', '<EMAIL>'];
        Mail::to($users)->cc($ccUsers)->send(new MyTestMail($details, $attachments));
        return response()->json(['status' => 'success']);
    }
}
