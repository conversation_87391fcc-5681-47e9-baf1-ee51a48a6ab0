<?php

namespace App\Http\Controllers;

use App\DivisionType;
use App\Line;
use App\LineApproval;
use App\LineDivisionType;
use App\Models\ApprovalSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Mpdf\Tag\I;

class ApprovalSettingController extends ApiController
{
    private $parent_names = array();
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $approval_settings = DB::table('approval_settings')
        ->select(
            'approval_settings.id',
            'approval_settings.name',
            'approval_settings.key',
            'approval_settings.value',
            'approval_settings.type'
        )
        ->whereNull('deleted_at')
        ->get();

    return $this->respond($approval_settings);
    }


    /**
     * Display the specified resource.
     *
     * @param  \App\LineApproval  $lineApproval
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $approval_setting = DB::table('approval_settings')
            ->select(
                'approval_settings.id',
                'approval_settings.name',
                'approval_settings.key',
                'approval_settings.value',
                'approval_settings.type'
            )
            ->where('approval_settings.id', '=', $id)
            ->first();

        return $this->respond($approval_setting);
    }


    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\LineApproval  $lineApproval
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request,$id)
    {
        $approval_setting = ApprovalSetting::find($id);
        $approval_setting->name = $request->input('name');
        $approval_setting->key = $request->input('key');
        $approval_setting->value = $request->input('value');
        $approval_setting->type = $request->input('type');
        $approval_setting->save();
        return $this->respondSuccess();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\LineApproval  $lineApproval
     * @return \Illuminate\Http\Response
     */
    public function destroy(LineApproval $lineApproval)
    {
        //
    }
}
