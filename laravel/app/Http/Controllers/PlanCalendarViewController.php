<?php

namespace App\Http\Controllers;

use App\Account;
use App\ActualVisit;
use App\ClassFrequency;
use App\Doctor;
use App\DoctorFrequency;
use App\Models\OtherSetting;
use App\Models\PlanLevel;
use App\Models\SpecialityClassFrequency;
use App\PlanSetting;
use App\PlanVisit;
use App\PlanVisitColumn;
use App\Services\Enums\PlanVisitSettings\DeletePlan;
use App\Services\FrequencyTypeValidationService;
use App\SpecialityFrequency;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use DB;
use Exception;
use Illuminate\Http\Request;

use function PHPUnit\Framework\isNull;

class PlanCalendarViewController extends Controller
{
    public function getPlanSchedule(Request $request)
    {
        $user = auth()->user();
        $planFilter = $request->planFilter;

        // Get the date range
        $fromDate = new Carbon($planFilter['fromDate']);
        $toDate = new Carbon($planFilter['toDate']);


        // $plan_level = PlanSetting::where('key', 'plan_level')->value('value');
        $planLevel = PlanLevel::where(fn($q)=> $q->whereNull('line_id')->orWhere('line_id',$planFilter['line']))->first()->level;
        if ($planLevel == 'Account') {
            $data = $this->accountLevel($planFilter);
        }
        if ($planLevel == 'Doctor') {
            $data = $this->doctorLevel($planFilter);
        }

        $planEditAndDelete = DeletePlan::from(PlanSetting::where('key', 'delete_plan')->value('value'));
        $isPlanWithShift = PlanSetting::where('key', 'plan_shift')->value('value') === 'yes';

        $oldVisits = PlanVisit::where('user_id', $user->id)
            ->whereBetween(
                'visit_date',
                [
                    $fromDate,
                    $toDate
                ]
            )
            ->with(
                "account",
                "account.type",
                "doctor",
                "doctor.speciality"
            )
            ->get()->map(function ($oldVisit) use ($planLevel, $planEditAndDelete) {
                $approval = $oldVisit->details->approval;
                $isApproved = $approval === 1;
                return [
                    'Id' => $oldVisit->id,
                    'Subject' => $oldVisit->account->name,
                    'DoctorName' => $oldVisit->doctor?->name ?? "",
                    'AccountType' => $oldVisit->account
                        ->type->name,
                    'Speciality' => $oldVisit->doctor?->speciality?->name ?? "",
                    'DivId' => $oldVisit->div_id,
                    'AccountId' => $oldVisit->account_id,
                    'DoctorId' => $oldVisit->account_dr_id,
                    'Shift' => $oldVisit->shift_id,
                    'StartTime' => Carbon::parse($oldVisit->visit_date),
                    'EndTime' => Carbon::parse($oldVisit->visit_date)->addMinutes(30),
                    'Time' => date('H:i', strtotime($oldVisit->visit_date)) ? date('H:i', strtotime($oldVisit->visit_date)) : null,
                    'IsReadOnly' => match ($planEditAndDelete) {
                        DeletePlan::AFTER_APPROVAL => !$isApproved,
                        DeletePlan::BEFORE_APPROVAL => !($approval === null),
                        DeletePlan::CANT_EDIT_DELETE => true,
                    },
                    'CalendarId' => $approval === null
                        ? 1
                        : (!!$approval ? 2 : 3)
                ];
            });

        if (count($data) == 0) {
            throw new Exception('There are no list');
        }

        return response()->json([
            'data' => $data,
            'minDate' => $fromDate->startOfDay(), //->format('Y-m-d H:i:s'),
            'maxDate' => $toDate->endOfDay(), //->format('Y-m-d H:i:s'),
            'isWithShift' => $isPlanWithShift,
            'isPerAccount' => $planLevel == 'Account',
            'oldVisits' => $oldVisits,
        ]);
    }

    public function accountLevel($planFilter)
    {
        $accounts = Account::select(
            'accounts.id',
            'accounts.name as account',
            'account_types.name as account_type',
            'bricks.name as brick',
            'line_divisions.id as div_id',
            'classes.name as class',
            'class_frequencies.frequency as frequency',
        )
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
            ->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('classes', 'account_lines.class_id', 'classes.id')
            ->leftJoin('class_frequencies', 'classes.id', 'class_frequencies.class_id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->where(fn ($q) => $q->where('accounts.inactive_date', '=', null)->orWhere('accounts.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn ($q) => $q->where('account_lines.to_date', '=', null)->orWhere('account_lines.to_date', '>=', (string)Carbon::now()))
            ->where('accounts.active_date', '<=', Carbon::now())
            ->whereIntegerInRaw('accounts.type_id', $planFilter['types'])
            ->where('account_lines.line_id', $planFilter['line'])
            ->where('account_lines.line_id', $planFilter['line'])
            ->whereIntegerInRaw('account_lines.line_division_id', $planFilter['divisions']);
        if (!empty($planFilter['bricks'])) {
            $accounts = $accounts->whereIntegerInRaw('account_lines.brick_id', $planFilter['bricks']);
        }
        $accounts = $accounts->get()->unique('id')->map(function ($account) {
            return [
                'Id' => mt_rand(0, 999999),
                'AccountId' => $account->id,
                'DivId' => $account->div_id,
                'AccountName' => $account->account,
                'AccountType' => $account->account_type,
                'Speciality' => $account->speciality,
                'DoctorId' => null,
                'DoctorName' => null,
                'Class' => $account->class,
            ];
        });
        return $accounts->values();
    }

    public function doctorLevel($planFilter)
    {
        $accounts = Account::select(
            'accounts.id',
            'accounts.name as account',
            'account_types.name as account_type',
            'account_types.id as acc_type_id',
            'specialities.name as speciality',
            'specialities.id as speciality_id',
            'bricks.name as brick',
            'line_divisions.id as div_id',
            'classes.name as class',
            'doctors.id as account_dr_id',
            'doctors.name as doctor',
            'new_account_doctors.from_date as from',
            'new_account_doctors.to_date as to',
        )
            ->leftJoin('new_account_doctors', 'accounts.id', 'new_account_doctors.account_id')
            ->leftJoin('doctors', 'new_account_doctors.doctor_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
            ->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('classes', 'doctors.class_id', 'classes.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->where(fn ($q) => $q->where('accounts.inactive_date', '=', null)->orWhere('accounts.inactive_date', '>=', $planFilter['toDate']))
            ->where(fn ($q) => $q->where('doctors.inactive_date', '=', null)->orWhere('doctors.inactive_date', '>=', $planFilter['toDate']))
            ->where(fn ($q) => $q->where('account_lines.to_date', '=', null)->orWhere('account_lines.to_date', '>=', $planFilter['toDate']))
            ->where(fn ($q) => $q->where('new_account_doctors.to_date', '=', null)->orWhere('new_account_doctors.to_date', '>=', $planFilter['toDate']))
            ->where('accounts.active_date', '<=', $planFilter['fromDate'])
            ->where('doctors.active_date', '<=', $planFilter['fromDate'])
            ->where('account_lines.from_date', '<=', $planFilter['fromDate'])
            ->where('new_account_doctors.from_date', '<=', $planFilter['fromDate'])
            ->where('accounts.deleted_at', null)
            ->where('account_lines.deleted_at', null)
            ->where('new_account_doctors.deleted_at', null)
            ->where('doctors.deleted_at', null)
            ->whereIntegerInRaw('accounts.type_id', $planFilter['types'])
            ->where('account_lines.line_id', $planFilter['line'])
            ->where('new_account_doctors.line_id', $planFilter['line'])
            ->whereIntegerInRaw('account_lines.line_division_id', $planFilter['divisions'])
            ->whereIntegerInRaw('doctors.speciality_id', $planFilter['specialities']);
        if (!empty($planFilter['bricks'])) {
            $accounts = $accounts->whereIntegerInRaw('account_lines.brick_id', $planFilter['bricks']);
        }
        $accounts = $accounts->get()
            ->unique(function ($item) {
                return $item['id'] . $item['account_dr_id'] . $item['acc_type_id'] . $item['speciality_id'];
            })
            ->map(function ($account)use($planFilter) {
                return [
                    'Id' => mt_rand(0, 999999),
                    'AccountId' => $account->id,
                    'DivId' => $account->div_id,
                    'AccountName' => $account->account,
                    'AccountType' => $account->account_type,
                    'Frequency' => (new FrequencyTypeValidationService)->classType($account->id, $account->account_dr_id, $planFilter['line'], $planFilter['fromDate']) ?? '',
                    'Speciality' => $account->speciality,
                    'DoctorId' => $account->account_dr_id,
                    'DoctorName' => $account->doctor,
                    'Class' => $account->class,
                ];
            });
        return $accounts->values();
    }
}
