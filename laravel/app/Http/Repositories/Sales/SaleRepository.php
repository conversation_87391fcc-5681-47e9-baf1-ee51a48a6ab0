<?php

namespace App\Http\Repositories\Sales;

use App\Action;
use App\Distributor;
use App\Exceptions\CrmException;
use App\Exceptions\FileNotFoundException;
use App\Form;
use App\Helpers\LogActivity;
use App\Helpers\Template;
use App\Mapping;
use App\Product;
use App\Sale;
use App\SalesTypes;
use Spatie\Permission\Models\Permission;

class SaleRepository
{

    public function readFile($request)
    {
        //validate the file data
        $onlyValidate = true;
        $onlyImport = false;
        $validSales = Sale::import($request, $onlyValidate, $onlyImport, ['distributor' => $request->distributor, 'type' => $request->type,'date' => $request->date]);
        // throw new CrmException($validSales,1);
        return $validSales->map(function ($validSale) use ($request) {
            return [
                'brick' => Mapping::where('code', '=', $validSale['brick'])->get()->first(),
                'distributor' => Distributor::find($request->distributor),
                'type' => SalesTypes::find($request->type),
                'sales' => (int)$validSale['sales'],
                'bonus' => (int)$validSale['bonus'],
                'region' => $validSale['region'],
                'product' => Distributor::find($request->distributor)->products()->where('code', '=',$validSale['product'])->first(),
                'value' => (int)$validSale['value'],
                'date' => $validSale['date'] ?? $request->date,
            ];
        });
    }

    public function saveFile($request): void
    {
        Sale::import(
            $request,
            array: $request->except("file")
        );
    }

    public function getFileAttributes($filename)
    {
        return (new Template())->getFileAttributes($filename);
    }
}
