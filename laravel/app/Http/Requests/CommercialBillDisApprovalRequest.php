<?php

namespace App\Http\Requests;

use App\Models\CommercialRequest\CommercialBill;
use App\Models\CommercialRequest\CommercialRequest;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CommercialBillDisApprovalRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'bills.*.visitable_id' => ['required', 'exists:commercial_bills,id'],
            'bills.*.visitable_type' => ['required', Rule::in([CommercialBill::class])],
            // 'commercials.*.reason_id' => ['nullable']
        ];
    }
}
