<?php

namespace App\Http\Requests;

use App\Services\Enums\AlertAttachmentExtention;
use App\Services\Enums\AlertPeriodPattern;
use App\Services\Enums\AlertTypeReport;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AlertRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }



    public function rules()
    {
        return [
            'period' => ['required',Rule::in(AlertPeriodPattern::toArray()),],
            'time' => ['required','date_format:H:i'],
            'format' => ['required',Rule::in(AlertAttachmentExtention::toArray())],
            'type' => ['required',Rule::in(AlertTypeReport::toArray())],
            'users.*' => ['required',Rule::exists('users', 'id')],
            'methods.*' => ['required',Rule::exists('sending_methods', 'id')],
        ];

    }
}
