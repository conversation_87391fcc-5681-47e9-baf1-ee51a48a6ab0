<?php

namespace App\Http\Requests;

use App\ActualVisitSetting;
use App\ErrorMessages;
use App\Exceptions\CrmException;
use App\Helpers\CrmExcelDate;
use App\Models\OffDay;
use App\Models\VacationBalance;
use App\PublicHoliday;
use App\Setting;
use App\User;
use App\Vacation;
use App\VacationType;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\RequiredIf;

class VacationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    public function rules()
    {
        // throw new CrmException($this->all());
        $notesSetting = Setting::where('key', 'required_notes')->value('value');

        $accept_visits_within_vacations = ActualVisitSetting::where('key', 'accept_visits_within_vacations')->value('value');
        /**@var User $user */
        $user = Auth::user();
        $attachment_allowed_extensions = Setting::where('key', 'allowed_attchment_extentions')->first();

        $dates = collect([]);
        $period = CarbonPeriod::create($this->from_date, $this->to_date);
        foreach ($period as $date) {
            $dates->push($date->format('Y-m-d'));
        }
        foreach ($dates as $date) {
            if (PublicHoliday::isPublicHoliday($this->from_date, $this->to_date, $date)) {
                throw new Exception("This date is created as a public holiday");
            }
        }
        return [
            'from_date'         => [
                'required',
                'string',
                'date_format:' . CrmExcelDate::OFFICIAL_FORMAT,
                function ($attribute, $value, $fail) {
                    $exists = Vacation::where('user_id', Auth::id())->whereHas('details', function ($q) {
                        $q->where(fn($q) => $q->whereNull('approval')->orWhere('approval', 1));
                    })->where(fn($q) => $q->whereNull('shift_id')->orWhere('shift_id', $this->shift_id))->where(fn($q) => $q->where('from_date', $value)->orWhere('to_date', $value))->exists();
                    if ($exists) $fail("The " . $attribute . " " . ErrorMessages::where("slug", "unique")->value("message_en"));
                },
                function ($attribute, $value, $fail) {
                    $fromMonth = Carbon::parse($value)->format('m');
                    $dates = collect([]);
                    Vacation::where('user_id', Auth::id())->whereHas('details', function ($q) {
                        $q->where(fn($q) => $q->whereNull('approval')->orWhere('approval', 1));
                    })->whereMonth('from_date', $fromMonth)->where(fn($q) => $q->whereNull('shift_id')->orWhere('shift_id', $this->shift_id))->get()
                        ->each(function ($vacation) use ($dates, $value, $attribute, $fail) {

                            $period = CarbonPeriod::create($vacation->from_date, $vacation->to_date);
                            foreach ($period as $date) {
                                $dates->push($date->format('Y-m-d 00:00:00'));
                            }
                            foreach ($dates as $date) {
                                if ($date == $value) {
                                    $fail("The " . $attribute . " " . ErrorMessages::where("slug", "unique")->value("message_en"));
                                }
                            }
                        });
                },

                function ($attribute, $value, $fail) {
                    $actualExtraTime = ActualVisitSetting::where('key', 'actual_extra_time')->value('value');
                    $actualExtraTimeWithVacation = ActualVisitSetting::where('key', 'actual_extra_time_with_vacations')->value('value');
                    if ($actualExtraTimeWithVacation == 'Yes') {
                        $validDate = Carbon::today()->startOfDay()->addHours($actualExtraTime)->toDateTimeString();
                        if (
                            $value < Carbon::today()->toDateTimeString() &&
                            Carbon::now()->toDateTimeString() > $validDate
                        ) {
                            $fail('The ' . $attribute . ' not valid because it exceeded ' . $validDate);
                        }
                    }
                },
                function ($attribute, $value, $fail) use ($user, $accept_visits_within_vacations) {
                    if ($accept_visits_within_vacations == 'No') {
                        $visits = $user->actualVisits()->whereDate('visit_date', Carbon::parse($value)->toDateString());
                        $offices = $user->officeWork()->whereDate('date', Carbon::parse($value)->toDateString());
                        if ($offices->exists()) {
                            $offices->get()->each(function ($office) use ($attribute) {
                                if ($office->shift_id == $this->shift_id || ($this->full_day == 1 && isNullable($this->shift_id))) {
                                    throw new Exception('The ' . $attribute . ' not valid because user has office work on this day ');
                                }
                            });
                        }
                        if ($visits->exists()) {
                            $visits->get()->each(function ($visit) use ($attribute) {
                                if ($visit->accountType->shift->id == $this->shift_id || ($this->full_day == 1 && isNullable($this->shift_id))) {
                                    throw new Exception('The ' . $attribute . ' not valid because user has actual Visits on this day ');
                                }
                            });
                        }
                    }
                }
            ],
            'to_date'           => [
                'required',
                'string',
                'after_or_equal:from_date',
                'date_format:' . CrmExcelDate::OFFICIAL_FORMAT,
                function ($attribute, $value, $fail) {
                    $exists = Vacation::where('user_id', Auth::id())->whereHas('details', function ($q) {
                        $q->where(fn($q) => $q->whereNull('approval')->orWhere('approval', 1));
                    })->where(fn($q) => $q->whereNull('shift_id')->orWhere('shift_id', $this->shift_id))->where(fn($q) => $q->where('from_date', $value)->orWhere('to_date', $value))->exists();
                    if ($exists) $fail("The " . $attribute . " " . ErrorMessages::where("slug", "unique")->value("message_en"));
                },
                function ($attribute, $value, $fail) {
                    $dates = collect([]);
                    Vacation::where('user_id', Auth::id())->whereHas('details', function ($q) {
                        $q->where(fn($q) => $q->whereNull('approval')->orWhere('approval', 1));
                    })->where(fn($q) => $q->whereNull('shift_id')->orWhere('shift_id', $this->shift_id))->get()->each(function ($vacation) use ($dates, $value, $attribute, $fail) {
                        $period = CarbonPeriod::create($vacation->from_date, $vacation->to_date);
                        foreach ($period as $date) {
                            $dates->push($date->format('Y-m-d 00:00:00'));
                        }
                        foreach ($dates as $date) {
                            if ($date == $value) {
                                $fail("The " . $attribute . " " . ErrorMessages::where("slug", "unique")->value("message_en"));
                            }
                        }
                    });
                },

                function ($attribute, $value, $fail) use ($user, $accept_visits_within_vacations) {
                    if ($accept_visits_within_vacations == 'No') {
                        $visits = $user->actualVisits()->whereDate('visit_date', Carbon::parse($value)->toDateString());
                        $offices = $user->officeWork()->whereDate('date', Carbon::parse($value)->toDateString());
                        if ($offices->exists()) {
                            $offices->get()->each(function ($office) use ($attribute) {
                                if ($office->shift_id == $this->shift_id || ($this->full_day == 1 && isNullable($this->shift_id))) {
                                    throw new Exception('The ' . $attribute . ' not valid because user has office work on this day ');
                                }
                            });
                        }
                        if ($visits->exists()) {
                            $visits->get()->each(function ($visit) use ($attribute) {
                                if ($visit->accountType->shift->id == $this->shift_id) {
                                    throw new Exception('The ' . $attribute . ' not valid because user has actual Visits on this day ');
                                }
                            });
                        }
                    }
                }
            ],
            'vacation_type_id'  => ['required', 'integer', 'exists_not_soft_deleted:vacation_types,id', function ($attribute, $value, $fail) {
                $vacationType = VacationType::find($value);
                $fromDate = $vacationType->startDate?->day ? Carbon::now()->addDays($vacationType->startDate?->day)->toDateString() : null;
                if ($fromDate && $fromDate > Carbon::parse($this->from_date)->toDateString()) {
                    $fail('This Date Not Valid');
                }
            }, function ($attribute, $value, $fail) {
                $with_attach = VacationType::find($value)?->with_attach;

                if ($with_attach && count($this->attachments) == 0) {
                    $fail('Attachment is required for this Vacation Type');
                }
            }, function ($attribute, $value, $fail) use ($user) {
                $with_balance = VacationType::find($value)?->with_balance;
                if ($with_balance) {
                    $vacationBalance = VacationBalance::where('user_id', $user->id)
                        ->where('vacation_type_id', $value)
                        ->whereYear('from_date', Carbon::now())
                        ->first();
                    $shifts = $this->shift_id ? [$this->shift_id] : [1, 2];
                    $countVacationDays = $this->getVacationsDatePerPeriod(
                        $user,
                        Carbon::now()->firstOfYear()->toDateString(),
                        Carbon::now()->endOfYear()->toDateString(),
                        $shifts,
                        $value,
                    );
                    if ($countVacationDays > $vacationBalance->balance)
                        $fail('Exceeded Limit Balance for this Vacation Type');
                    // throw new CrmException($vacationBalance);

                }
            },],
            'shift_id'          => ['nullable'],
            'notes'          => [Rule::requiredIf(fn() => $notesSetting == 'Yes')],
            'full_day'          => [function ($attribute, $value, $fail) {
                if (isNullable($this->shift_id) && $value == 0) {
                    $fail('Vacation Can\'t be without Shift or FullDay');
                }
            }, function ($attribute, $value, $fail) {
                if (!isNullable($this->shift_id) && $value == 1) {
                    $fail('Please choose full day or Shift can\'t choose both');
                }
            },],
            'file'              => ['nullable', 'mimes:' . $attachment_allowed_extensions->value],
        ];
    }

    public function feedback()
    {
        return $this->morphMany('App\Models\RequestFeedback', 'requestable');
    }
    public function getVacationsDatePerPeriod(User $user, $from, $to, array $shifts, $typeId, $line_id = null)
    {
        $vacations = Vacation::select('id', 'user_id', 'from_date', 'to_date', 'full_day', 'shift_id')
            ->where('user_id', $user->id)
            ->where(fn($q) => $q->whereIntegerInRaw('shift_id', $shifts)->orWhereNull('shift_id'))
            ->whereBetween('vacations.from_date', [$from, $to])
            ->whereBetween('vacations.to_date', [$from, $to])
            ->whereHas('details', function ($q) {
                $q->where('approval', 1);
            })->where('vacation_type_id', $typeId);

        $vacations = $vacations->get();
        $count = 0.0;
        $shifts = count($shifts) > 1 ? [1, 2] : $shifts;
        foreach ($vacations as $vacation) {
            $period = CarbonPeriod::create($vacation->from_date, $vacation->to_date);
            foreach ($period as $date) {
                foreach ($shifts as $shift) {
                    if (
                        !OffDay::isOffDay($vacation->from_date, $vacation->to_date, $date, $shift, $line_id) &&
                        !PublicHoliday::isPublicHoliday($from, $to, $date, $line_id)
                    ) {
                        $count += 0.5;
                    }
                }
            }
        }
        return $count;
    }
}
