<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\ErrorMessages;
use Illuminate\Validation\Rule;

class VacationTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function store()
    {
        return [
            'name'      => ['required','string','max:191','unique:vacation_types,name'],
            'startDate'  => ['required'],
        ];
    }
    public function update()
    {
        return [
            'name'      => ['required','string','max:191',Rule::unique('vacation_types')->ignore($this->id, 'id')],
            'startDate'  => ['nullable'],
        ];
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {
            return $this->store();
        }
        if ($this->isMethod('PUT')) {
            return $this->update();
        }
    }
}
