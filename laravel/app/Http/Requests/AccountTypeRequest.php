<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\ErrorMessages;
use Illuminate\Validation\Rule;

class AccountTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function store(){
        return [
            'shift_id'  => ['required','integer','exists_not_soft_deleted:shifts,id'],
            'name'      => ['required','string','unique:account_types,name'],
            'notes'     => ['nullable','string'],
            'parent_id' => ['required','integer','min:0'],
        ];
    }
    public function update(){
        return [
            'shift_id'  => ['required','integer','exists_not_soft_deleted:shifts,id'],
            'name'      => ['required','string',Rule::unique('account_types')->ignore($this->id,'id')],
            'notes'     => ['nullable','string'],
            'parent_id' => ['required','integer','min:0'],
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {
            return $this->store();
        }
        if ($this->isMethod('PUT')) {
            return $this->update();
        }
    }
}
