<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BricksRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function store()
    {
        return [
            'name'      => ['required', 'string', 'max:191', 'unique:bricks,name'],
            'notes'     => ['nullable', 'string', 'max:191'],
            'unified_brick.*.percentage' => ['nullable', 'numeric', 'max:100'],
            'unified_brick.*.id' => ['nullable', 'numeric']
        ];
    }
    public function update()
    {
        return [
            'name'      => ['required', 'string', 'max:191', Rule::unique('bricks')->ignore($this->id, 'id')],
            'notes'     => ['nullable', 'string', 'max:191'],
            'unified_brick.*.percentage' => ['nullable', 'numeric', 'max:100'],
            'unified_brick.*.id' => ['nullable', 'numeric'],
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {
            return $this->store();
        }
        if ($this->isMethod('PUT')) {
            return $this->update();
        }
    }
}
