<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\ErrorMessages;
use App\Helpers\CrmExcelDate;
use Illuminate\Validation\Rule;

class UserStartPlanDayRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get data to be validated from the request.
     *
     * @return array
     */
    public function update(){
        return [
            'line_id' => ['required','integer', 'exists_not_soft_deleted:lines,id'],
            'user_id' => ['required','integer',Rule::unique('user_start_plan_days')->ignore($this->id,'id')],
            'date' => ['required','string','date_format:'.CrmExcelDate::OFFICIAL_FORMAT]
        ];
    }

    public function store(){
        return [
            'line_id' => ['required','integer', 'exists_not_soft_deleted:lines,id'],
            'user_id' => ['required','integer', 'unique:user_start_plan_days,user_id'],
            'date' => ['required','string','date_format:'.CrmExcelDate::OFFICIAL_FORMAT],
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {
            return $this->store();
        }
        if ($this->isMethod('PUT')) {
            return $this->update();
        }
    }
}
