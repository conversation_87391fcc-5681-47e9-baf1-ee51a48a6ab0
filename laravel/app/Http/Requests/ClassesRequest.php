<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\ErrorMessages;
use Illuminate\Validation\Rule;

class ClassesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function store()
    {
        return [
            'name'      => ['required','string','max:191',Rule::unique('classes')->whereNull('deleted_at')],
            'notes'     => ['nullable','string','max:191'],
        ];
    }
    public function update()
    {
        return [
            'name'      => ['required','string','max:191',Rule::unique('classes')->ignore($this->id, 'id')->whereNull('deleted_at')],
            'notes'     => ['nullable','string','max:191'],
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {
            return $this->store();
        }
        if ($this->isMethod('PUT')) {
            return $this->update();
        }
    }
}
