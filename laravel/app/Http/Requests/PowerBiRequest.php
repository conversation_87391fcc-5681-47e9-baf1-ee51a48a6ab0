<?php

namespace App\Http\Requests;

use App\Exceptions\CrmException;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;
class PowerBiRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        return [
            'user_name' => ['required', 'unique:power_bi,user_name'],
            'password' => ['required', 'min:8'],
        ];
    }

    public function prepareForValidation()
    {
        $this->SuffixUserName();

        parent::prepareForValidation();
    }

    protected function SuffixUserName()
    {
        $db = env('DB_DATABASE');
        if ($this->has('user_name')) {
            $this->merge(['user_name' => Str::slug($this->user_name,"_") . '_' . $db]);
        }
    }
}
