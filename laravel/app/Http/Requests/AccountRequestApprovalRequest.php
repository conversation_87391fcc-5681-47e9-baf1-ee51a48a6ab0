<?php

namespace App\Http\Requests;

use App\Models\Expenses\Expense;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AccountRequestApprovalRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'accounts.*.visitable_id' => ['required', 'exists_not_soft_deleted:accounts,id'],
            'accounts.*.visitable_type' => ['required', Rule::in([AccountRequest::class])],
            'accounts.*.reason_id' => ['nullable'],
        ];
    }
}
