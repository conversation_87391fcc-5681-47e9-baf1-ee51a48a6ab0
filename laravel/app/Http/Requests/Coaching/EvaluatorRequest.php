<?php

namespace App\Http\Requests\Coaching;

use Illuminate\Foundation\Http\FormRequest;

class EvaluatorRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {

            return [
                'name'  => 'required|string',
                'evaluatorable_id' => 'required|integer',
                'evaluatorable_type' => 'required|string',
            ];
        }

        if ($this->isMethod('PUT')) {
            return [
                'name' => [
                    'sometimes',
                    'string',
                ],
                'evaluatorable_id' => [
                    'sometimes',
                    'integer',
                ],
                'evaluatorable_type' => [
                    'sometimes',
                    'string',
                ],
            ];
        }
    }
}
