<?php

namespace App\Http\Requests\Coaching;

use Illuminate\Foundation\Http\FormRequest;

class CategoryQuestionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {
            return [
                'category_id' => 'required | integer | exists_not_soft_deleted:coaching_categories,id',
                'question_id' => 'required | integer | exists_not_soft_deleted:coaching_questions,id',

            ];
        }

        if ($this->isMethod('PUT')) {
            return [
                'category_id' => 'sometimes | integer | exists_not_soft_deleted:coaching_categories,id',
                'question_id' => 'sometimes | integer | exists_not_soft_deleted:coaching_questions,id',

            ];
        }
    }
}
