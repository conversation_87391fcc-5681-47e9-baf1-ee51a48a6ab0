<?php

namespace App\Http\Requests\Coaching;


use App\Models\Coaching\Category;
use App\Models\Coaching\CategoryEvaluator;
use App\Models\Coaching\Evaluator;
use App\Traits\Sortable;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CategoryRequest extends FormRequest
{

    /**
     * using Sortable trait to handle max_sort parameter through 
     * handleSortParameter(Model $model)
     */
    use Sortable;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        $category = new Category();
        $this->handleSortParameter($category);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        if ($this->isMethod('POST')) {
            return [
                'name'  => 'required|string|unique:coaching_categories,name',
                'notes' => 'nullable|string',
                'sort' => 'required|integer|min:100|unique:coaching_categories,sort',
                'type_id' => 'required | integer | exists_not_soft_deleted:coaching_types,id',
                'evaluator_ids' => 'required | array',
                'evaluator_ids.*' => 'required | integer | exists_not_soft_deleted:coaching_evaluators,id'
            ];
        }

        if ($this->isMethod('PUT')) {
            return [
                'name' => [
                    'sometimes',
                    'string',
                    Rule::unique('coaching_categories', 'name')->ignore($this->category),
                ],
                'notes' => [
                    'nullable',
                    'string',
                ],
                'sort' => [
                    'sometimes',
                    'integer',
                    'min:100',
                    Rule::unique('coaching_categories', 'sort')->ignore($this->category),
                ],
                'type_id' => ['sometimes', 'integer', 'exists_not_soft_deleted:coaching_types,id'],

                'evaluator_ids' => ['sometimes', 'array'],
                'evaluator_ids.*' => ['integer', 'exists_not_soft_deleted:coaching_evaluators,id'],
            ];
        }
    }
}
