<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\ErrorMessages;
use App\Exceptions\CrmException;

class VacationSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function updateRules(){
        return [
            'name'  => 'required|string|max:191',
            'key'   => 'required|string|max:191',
            'value' => $this->request->get('type') == 'date' ? '' : 'required',
            'type'  => 'required|string|max:191',
        ];
    }

    public function createRules(){
        return [
            // 'key' => ['required|string'],
            // 'name' => ['required|string'],
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {
            return $this->createRules();
        }
        if ($this->isMethod('PUT')) {
            return $this->updateRules();
        }
    }
}
