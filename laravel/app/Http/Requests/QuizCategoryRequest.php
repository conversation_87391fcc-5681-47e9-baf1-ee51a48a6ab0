<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class QuizCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

   

    public function store(){
        return [
            'name' => ['required','unique:quiz_categories,name','string'],
            'sort' => ['nullable','numeric'],

        ];
    }
    public function update(){
        return [
            'name' => ['required',Rule::unique('quiz_categories')->ignore($this->id,'id'),'string'],
            'sort' => ['numeric'],

        ];
    }




    public function rules()
    {
        if ($this->isMethod('POST')) {
            return $this->store();
        }
        if ($this->isMethod('PUT')) {
            return $this->update();
        }
    }
}
