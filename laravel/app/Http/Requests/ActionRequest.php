<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\ErrorMessages;
use Illuminate\Validation\Rule;

class ActionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return false;
    }

    public function store(){
        return [
            'action'      => ['required','string','unique:actions,action'],
        ];
    }
    public function update(){
        return [
            'action'      => ['required','string',Rule::unique('actions')->ignore($this->id,'id')],
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {
            return $this->store();
        }
        if ($this->isMethod('PUT')) {
            return $this->update();
        }
    }
}
