<?php

namespace App\Http\Requests;

use App\Exceptions\CrmException;
use App\Helpers\CrmExcelDate;
use App\LineProduct;
use App\Models\CommercialPharmacySetting;
use App\Models\PaymentMethod;
use App\RequestType;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CommercialRequestRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }


    public function store()
    {
        if (!empty($this->categoriesCosts)) {
            foreach ($this->categoriesCosts as $categoriesCost) {
                $payment = PaymentMethod::find($categoriesCost['payment_id']);
                $isNullableCategories = isNullable($categoriesCost['cat_id'])
                    || isNullable($categoriesCost['type_id']) || isNullable($categoriesCost['sub_type_id']) || isNullable($categoriesCost['quantity']);
                if (
                    count($payment->categories) > 0 && $isNullableCategories
                ) throw new Exception('Category , Type , Sub Type and Audience  are required');
                if (!$payment) throw new Exception('Payment Method is required');
                $payment_method_with_users = $payment->with_users;
                if ($payment_method_with_users && isNullable($categoriesCost['user_id'])) {
                    throw new Exception('Custody Employee is required');
                }
            }
        }
        $isWithoutDoctors = RequestType::where('id', $this->request_type)->value('value') == 'no doctors';
        $withPharmacies = CommercialPharmacySetting::first()?->appearance;
        $withUnits = CommercialPharmacySetting::first()?->show_units;
        $lineProducts = LineProduct::whereIn('line_id', $this->lines)->exists();
        return [
            'request_type' => ['required', 'exists_not_soft_deleted:request_types,id'],
            'description' => ['required', 'string'],
            'from_date' => ['required', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'to_date' => ['required', 'after_or_equal:from_date', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'doctors' => [Rule::requiredIf(fn() => (!$isWithoutDoctors)), 'array'],
            'products' => $lineProducts ? ['required', 'array', 'min:1'] : [],
            'products.*' => $lineProducts ? ['required'] : [],
            'lines' => ['required', 'array', 'min:1'],
            'lines.*' => ['required'],
            'types.*' => empty($this->categoriesCosts) ? ['required'] : [],
            'products.*.pharmacies' => $withPharmacies == 1 ? ['required', 'min:2'] : [],
            'products.*.units' => $withUnits == 1 ? ['required', 'gt:0'] : [],
            'products.*.ratio' => $lineProducts ? ['required', function ($attribute, $value, $fail) {
                $ratioSum = collect($this->products)->pluck('ratio')->sum();
                if ($ratioSum < 99.9 || $ratioSum > 100.1)
                    $fail('Products Ratio must be equal 100');
            }] : [],
            'types.*.amount' =>  empty($this->categoriesCosts) ? ['required', 'gt:0'] : []
        ];
    }
    public function update()
    {
        return [
            'description' => ['required', 'string'],
            'from_date' => ['nullable', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'to_date' => ['nullable', 'after_or_equal:from_date', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'products.*.ratio' => ['required', function ($attribute, $value, $fail) {
                $ratioSum = collect($this->products)->pluck('ratio')->sum();
                if ($ratioSum < 99.9 || $ratioSum > 100.1)
                    $fail('Products Ratio must be equal 100');
            }],
        ];
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {
            return $this->store();
        }
        if ($this->isMethod('PUT')) {
            return $this->update();
        }
    }
}
