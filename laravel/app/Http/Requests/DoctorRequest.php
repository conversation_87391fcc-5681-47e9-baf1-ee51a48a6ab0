<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\ErrorMessages;
use App\Exceptions\CrmException;
use App\Helpers\CrmExcelDate;
use Illuminate\Validation\Rule;

class DoctorRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function store(){
        return [
            'speciality_id'         => ['required','integer','exists_not_soft_deleted:specialities,id'],
            'sub_speciality_id'     => ['nullable','integer','exists_not_soft_deleted:specialities,id'],
            'class_id'              => ['required','integer','exists_not_soft_deleted:classes,id'],
            'personality_type_id'   => ['nullable','integer','exists_not_soft_deleted:personality_types,id'],
            'level_id'              => ['nullable','integer','exists_not_soft_deleted:levels,id'],
            'gender'                => ['required','string','max:191'],
            'ucode'                 => ['required','string','unique:doctors,ucode'],
            'name'                  => ['required','string'],
            'tel'                   => ['nullable','string'],
            'mobile'                => ['nullable','string'],
            'email'                 => ['nullable','email'],
            'dob'                   => ['nullable','string','max:191','date_format:'.CrmExcelDate::OFFICIAL_FORMAT],
            'dom'                   => ['nullable','string','max:191','date_format:'.CrmExcelDate::OFFICIAL_FORMAT],
            'active_date'           => ['required','string','max:191','date_format:'.CrmExcelDate::OFFICIAL_FORMAT],
            'inactive_date'         => ['nullable','string','max:191','after_or_equal:active_date','date_format:'.CrmExcelDate::OFFICIAL_FORMAT],
        ];
    }
    public function update(){
        return [
            'speciality_id'         => ['required','integer','exists_not_soft_deleted:specialities,id'],
            'sub_speciality_id'     => ['nullable','integer','exists_not_soft_deleted:specialities,id'],
            'class_id'              => ['required','integer','exists_not_soft_deleted:classes,id'],
            'personality_type_id'   => ['nullable','integer','exists_not_soft_deleted:personality_types,id'],
            'level_id'              => ['nullable','integer','exists_not_soft_deleted:levels,id'],
            'gender'                => ['required','string','max:191'],
            'ucode'                 => ['required','string',Rule::unique('doctors')->ignore($this->id,'id')],
            'name'                  => ['required','string'],
            'tel'                   => ['nullable','string'],
            'mobile'                => ['nullable','string'],
            'email'                 => ['nullable','email'],
            'dob'                   => ['nullable','string','max:191','date_format:'.CrmExcelDate::OFFICIAL_FORMAT],
            'dom'                   => ['nullable','string','max:191','date_format:'.CrmExcelDate::OFFICIAL_FORMAT],
            'active_date'           => ['required','string','max:191','date_format:'.CrmExcelDate::OFFICIAL_FORMAT],
            'inactive_date'         => ['nullable','string','max:191','after_or_equal:active_date','date_format:'.CrmExcelDate::OFFICIAL_FORMAT],
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {
            return $this->store();
        }
        if ($this->isMethod('PUT')) {
            return $this->update();
        }
    }
}
