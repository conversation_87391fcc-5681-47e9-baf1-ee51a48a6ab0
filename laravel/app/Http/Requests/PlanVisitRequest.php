<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\ErrorMessages;
use App\Helpers\CrmExcelDate;

class PlanVisitRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'line'          => ['required','integer','exists_not_soft_deleted:lines,id'],
            'div_id'        => ['required'],
            'shift_id'      => ['nullable','integer'],
            'accountTypes'  => ['required','exists_not_soft_deleted:account_types,id',],
            'specialities'  => ['required','exists_not_soft_deleted:specialities,id',],
            'bricks'        => ['required','exists_not_soft_deleted:bricks,id',],
            'from_date'     => ['required', 'string', 'date'],
            'to_date'       => ['nullable', 'string', 'after_or_equal:from_date', 'date'],
        ];
    }
}
