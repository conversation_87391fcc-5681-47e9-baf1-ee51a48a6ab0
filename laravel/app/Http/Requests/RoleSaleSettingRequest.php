<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RoleSaleSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $validations = [
            'role_id' => 'required|exists:roles,id',
            'date' => 'required|date',
        ];
        if ($this->method() == 'POST') {
            $validations['role_id'] .= '|unique:role_sale_settings,role_id';
        }
        return $validations;
    }
}
