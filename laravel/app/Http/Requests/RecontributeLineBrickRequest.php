<?php

namespace App\Http\Requests;

use App\Rules\FirstOfMonth;
use Illuminate\Foundation\Http\FormRequest;

class RecontributeLineBrickRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "brick_id"=>['required','exists:bricks,id'],
            "line_id"=>['required','exists:lines,id'],
            "copy_list"=>['required','boolean'],
            "move_sales"=>['required','boolean'],
            "effect_date"=>['required','date',new FirstOfMonth],
            "divisions.*.id"=>['required',"exists:line_divisions,id"],
            "divisions.*.ratio"=>['required'],
        ];
    }
}
