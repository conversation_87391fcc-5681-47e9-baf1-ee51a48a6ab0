<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\ErrorMessages;
use App\Helpers\CrmExcelDate;
use App\ProductManufacturers;
use Illuminate\Validation\Rule;

class ProductManufacturerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function createRules()
    {
        return [
            'product_id'        => ['required', 'integer', 'exists_not_soft_deleted:products,id'],
            'manufacturer_id'   => ['required', 'integer', 'exists_not_soft_deleted:manufacturers,id', function ($attribute, $value, $fail) {
                $exists = ProductManufacturers::where("manufacturer_id", $value)->where("product_id", $this->product_id)->exists();
                if ($exists)
                    $fail("The " . $attribute . " " . ErrorMessages::where("slug", "unique")->value("message_en"));
            }],
            'from_date'         => ['required', 'string', 'date'],
            'to_date'           => ['nullable', 'string', 'after_or_equal:from_date', 'date'],
        ];
    }
    public function updateRules()
    {
        return [
            'product_id'        => ['required', 'integer', 'exists_not_soft_deleted:products,id'],
            'manufacturer_id'   => ['required', 'integer', Rule::unique('product_manufacturers')->ignore($this->id)->where("product_id", $this->product_id)],
            'from_date'         => ['required', 'string', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'to_date'           => ['nullable', 'string', 'after_or_equal:from_date', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {
            return $this->createRules();
        }
        if ($this->isMethod('PUT')) {
            return $this->updateRules();
        }
    }
}
