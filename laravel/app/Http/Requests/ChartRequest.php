<?php

namespace App\Http\Requests;

use App\ErrorMessages;
use Illuminate\Foundation\Http\FormRequest;

class ChartRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function validationData()
    {
        return array_map(function($value) {
            if(!is_array($value) && !is_file($value)){
                return trim($value);
            }
            return $value;
        }, $this->all());
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
            $id = $this->request->get('id');

        $rules = [
            'name'  => 'required|string|unique:charts,name,'.$id,
            'widget_id'   => 'required|integer|exists_not_soft_deleted:widgets,id',

        ];

        if($id==null){
            $rules['name'] = 'required|string|unique:charts,name';
        }

        return $rules;
    }

    // public function messages()
    // {
    //     $required_field = ErrorMessages::where('slug','required_field')->first();
    //     $string_field = ErrorMessages::where('slug','string_field')->first();
    //     $integer_field = ErrorMessages::where('slug','integer_field')->first();
    //     $min_field = ErrorMessages::where('slug','min_field')->first();
    //     $unique_field = ErrorMessages::where('slug','unique_field')->first();
    //     $exists_field = ErrorMessages::where('slug','exists_field')->first();


    //     return [
    //         'name.required' => 'Error Code: '. $required_field->code .' Name '.$required_field->message,
    //         'name.string'   => 'Error Code: '. $string_field->code .' Name '.$string_field->message,
    //         'name.min:3'    => 'Error Code: '. $min_field->code .' Name '.$min_field->message. ' 3',
    //         'name.unique'   => 'Error Code: '. $unique_field->code .' Name '.$unique_field->message,

    //         'widget_id.integer'  => 'Error Code: '. $integer_field->code .' Widget '.$integer_field->message,
    //         'widget_id.min:1'    => 'Error Code: '. $min_field->code .' Widget '.$min_field->message. ' 1',
    //         'widget_id.exists_not_soft_deleted:widgets,id'    => 'Error Code: '. $exists_field->code .' Widget ID'.$exists_field->message. ' in Widgets Table.',
    //     ];
    // }
}
