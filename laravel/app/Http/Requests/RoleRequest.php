<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\ErrorMessages;
use Illuminate\Validation\Rule;

class RoleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function createRules()
    {
        return [
            'name'  => ['required','string','unique:roles,name'],
            'guard_name'     => ['nullable','string','max:191'],
        ];
    }
    public function updateRules()
    {
        return [
            'name'      => ['required','string','max:191',Rule::unique('roles')->ignore($this->id, 'id')],
            'notes'     => ['nullable','string','max:191'],
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {
            return $this->createRules();
        }
        if ($this->isMethod('PUT')) {
            return $this->updateRules();
        }
    }
}
