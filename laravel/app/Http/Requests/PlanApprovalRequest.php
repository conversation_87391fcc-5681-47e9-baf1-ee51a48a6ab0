<?php

namespace App\Http\Requests;

use App\Exceptions\CrmException;
use Illuminate\Foundation\Http\FormRequest;

class PlanApprovalRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ($this->flag == 1) {
            return [
                'user' => ['required', 'exists_not_soft_deleted:users,id'],
            ];
        } else {
            return [
                'users_id.*' => ['required', 'exists_not_soft_deleted:users,id'],
                'from_date' => ['required', 'date'],
                'to_date' => ['required', 'date']
            ];
        }
    }
}
