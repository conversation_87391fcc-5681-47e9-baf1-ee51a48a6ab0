<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\ErrorMessages;
use App\Exceptions\CrmException;
use App\Helpers\CrmExcelDate;
use App\LineUser;
use Illuminate\Validation\Rule;

class LineUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function createRules()
    {
        return [
            'line_id'           => ['required', 'integer', 'exists_not_soft_deleted:lines,id'],
            'user_id.*'          => ['required', 'integer', 'exists_not_soft_deleted:users,id', 
            // function ($attribute, $value, $fail) {
            //     $exists = LineUser::where("user_id", $value)->where("line_id", $this->line_id)->exists();
            //     if ($exists)
            //         $fail("The " . $attribute . " " . ErrorMessages::where("slug", "unique")->value("message_en"));
            // }
        ],
            'from_date'         => ['required', 'string', 'max:191', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'to_date'           => ['nullable', 'string', 'max:191', 'after_or_equal:from_date', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
        ];
    }
    public function updateRules()
    {
        return [
            'line_id'           => ['required', 'integer', 'exists_not_soft_deleted:lines,id'],
            'user_id'          => ['required', 'integer', 'exists_not_soft_deleted:users,id'],
            'from_date'         => ['required', 'string', 'max:191', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'to_date'           => ['nullable', 'string', 'max:191', 'after_or_equal:from_date', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {
            return $this->createRules();
        }
        if ($this->isMethod('PUT')) {
            return $this->updateRules();
        }
    }
}
