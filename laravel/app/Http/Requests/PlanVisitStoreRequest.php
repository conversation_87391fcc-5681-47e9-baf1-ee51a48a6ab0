<?php

namespace App\Http\Requests;

use App\Account;
use App\ActualVisitSetting;
use App\Exceptions\CrmException;
use App\Line;
use App\Models\ChangePlan;
use App\Models\LinkedPharmacy;
use App\Models\PlanLevel;
use App\Models\ScheduledSetting;
use App\PlanSetting;
use App\PlanVisit;
use App\Services\Enums\ScheduleModuleTypes;
use App\StartPlanDay;
use App\User;
use App\UserStartPlanDay;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class PlanVisitStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        /**@var User $user */
        $user = Auth::user();
        $line = Line::find($this->planVisits[0]['line_id']);
        $planTime = $this->planTime($line);
        // throw new CrmException($planTime);
        $this->checkSchedules($planTime, $user, $line);
        $curentYear = Carbon::parse($this->fromDate)->format('Y');
        $min = Carbon::parse($planTime['final_user_plan_start_day'])->format('Y-m-d');
        foreach ($this->planVisits as $plan) {
            $planDate = Carbon::parse($plan['visit_date'])->format($curentYear . '-m-d');
            if ($planDate < $min && $planTime['changePlan'] == 0) {
                throw new Exception('There are Plans at Invalid Date  ' . $planDate);
            }
            // throw new CrmException(Carbon::parse($plan['visit_date'])->format($curentYear . '-m-d'));
            $exists = PlanVisit::where('user_id', Auth::id())
                ->whereHas('details', function ($q) {
                    $q->where(fn($q) => $q->whereNull('approval')->orWhere('approval', 1));
                })
                ->where('account_id', $plan['account_id'])
                ->where('account_dr_id', $plan['account_dr_id'])
                ->where(DB::raw("(DATE_FORMAT(visit_date,'%Y-%m-%d'))"), $planDate)
                ->exists();

            if ($exists)
                throw new Exception('this plan is created before at  ' . $planDate);
            $offices = $user->officeWork()->whereDate('date', Carbon::parse($plan['visit_date'])->format($curentYear . '-m-d'));
            if ($offices->exists()) {
                $offices->get()->each(function ($office) use ($plan, $curentYear) {
                    $account = Account::find($plan['account_id']);
                    // throw new CrmException($account->type->shift);
                    if ($office->shift_id == $account->type->shift->id || isNullable($office->shift_id)) {
                        throw new Exception('This Plan not valid because user has office work at ' . Carbon::parse($plan['visit_date'])->format($curentYear . '-m-d'));
                    }
                });
            }
        }
        $plan_shift = PlanSetting::where('key', 'plan_shift')->value('value');
        // $plan_time = PlanSetting::where('key', 'plan_time')->value('value');
        $plan_level = PlanLevel::where(fn($q) => $q->whereNull('line_id')->orWhere('line_id', $line->id))->first()->level;

        return [
            'planVisits.*.line_id' => ['required', 'exists_not_soft_deleted:lines,id'],
            'planVisits.*.div_id' => ['required'],
            'planVisits.*.account_id' => ['required'],
            'planVisits.*.account_dr_id' => [Rule::requiredIf(fn() => $plan_level == 'Doctor')],
            'planVisits.*.shift_id' => [Rule::requiredIf(fn() => ($plan_shift == 'yes'))],
            'planVisits.*.visit_date' => [
                'required',
                function ($attribute, $value, $fail) use ($curentYear) {
                    /**@var User $user */
                    $user = Auth::user();
                    $accept_visits_within_vacations = ActualVisitSetting::where('key', 'accept_visits_within_vacations')->value('value');

                    if ($accept_visits_within_vacations == 'No') {
                        $dates = $user->vacationDates();
                        foreach ($dates as $date) {
                            if ($date == Carbon::parse($value)->format($curentYear . '-m-d')) {
                                $fail("There is vacation on " . $attribute . " " . ",so you can't make any plans");
                            }
                        }
                    }
                },
                function ($attribute, $value, $fail) use ($curentYear) {
                    if (Carbon::parse($value)->format($curentYear . '-m-d') >= Carbon::today()->addMonth(2)->toDateString()) {
                        $fail("This " . $attribute . " " . ",not available in plans");
                    }
                },
            ],
        ];
    }


    public function planTime(Line $line)
    {
        if (!is_null($line->id)) {
            $authUser = Auth::user();
            $changePlans = ChangePlan::where('user_id', $authUser->id)
                ->whereHas('details', fn($q) => $q->where('approval', 1))
                ->whereDate('from', '>=', Carbon::now()->toDateString())
                ->get();
            foreach ($changePlans as $changePlan) {
                if (
                    !ChangePlan::getPlans($authUser, $changePlan->from, $changePlan->to)
                ) {
                    // throw new CrmException($changePlan);
                    $final_user_plan_start_day = Carbon::parse($changePlan->from)->toDateString();
                    $final_user_plan_end_day = Carbon::parse($changePlan->to)->toDateString();
                    return array(
                        'final_user_plan_start_day' => $final_user_plan_start_day ?? "",
                        'final_user_plan_end_day' => $final_user_plan_end_day ?? "",
                        'changePlan' => 1
                    );
                }
            }
            $user_plan_start_day_record = UserStartPlanDay::where('line_id', $line->id)->get();
            if ($user_plan_start_day_record->count() > 0) {
                foreach ($user_plan_start_day_record as $record) {
                    if ($record->user_id == 0 || ($record->user_id == Auth::id())) {
                        $user_plan_start_day = $record->date;
                        $final_user_plan_start_day = Carbon::parse($user_plan_start_day)->toDateString();
                    }
                }
            } else {
                $specific_plan_start_day = PlanSetting::where('key', 'specific_plan_start_day')->value('value');
                if ($specific_plan_start_day) {
                    $final_user_plan_start_day = Carbon::parse($specific_plan_start_day)->toDateString();
                } else {
                    $specific_plan_start_day_value = PlanSetting::where('key', 'start_plan_day')->first();
                    $specific_plan_start_day_days = StartPlanDay::where('name', $specific_plan_start_day_value->value)->first();
                    $final_user_plan_start_day = Carbon::now()->addDays($specific_plan_start_day_days->day)->toDateString();
                }
            }
        }
        return array(
            'final_user_plan_start_day' => $final_user_plan_start_day ?? "",
            'changePlan' => 0
        );
    }

    private function checkSchedules($planTime, $user, $line)
    {
        $checkUserStartDay = UserStartPlanDay::where('line_id', $line->id)->where('user_id', $user->id)->exists();
        if (!$planTime['changePlan'] && !$checkUserStartDay) {
            $getScedule = ScheduledSetting::where(fn($q) => $q->whereNull('line_id')->orWhere('line_id', $line->id))
                ->where('role_id', $user->roles?->first()?->id)
                ->where('module', ScheduleModuleTypes::PLAN)
                ->first();
            if ($getScedule) {
                $now = Carbon::now();

                // Get current week dates
                $startOfWeek = $now->copy()->startOfWeek(); // Typically Monday

                // Find next date for the start and end days
                $startDateTime = $startOfWeek->copy()->modify($getScedule['start'])->setTimeFromTimeString($getScedule['start_time']);
                $endDateTime = $startOfWeek->copy()->modify($getScedule['end'])->setTimeFromTimeString($getScedule['end_time']);

                // If the end day is before the start day in the week (e.g., Thursday to Saturday is okay, but Saturday to Thursday is next week)
                if ($endDateTime->lt($startDateTime)) {
                    $endDateTime->addWeek();
                }

                // Now check if current time is within range
                $isInRange = $now->between($startDateTime, $endDateTime);
                if (!$isInRange) {
                    $user->revokePermissionTo([
                        "create_plan_visits",
                    ]);
                    throw new Exception('Plan Time Invalid');
                }
            }
        }
    }
}
