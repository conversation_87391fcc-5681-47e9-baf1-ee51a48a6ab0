<?php

namespace App\Http\Requests;

use App\Exceptions\CrmException;
use Illuminate\Foundation\Http\FormRequest;
use App\Helpers\CrmExcelDate;
use Illuminate\Validation\Rule;

class AccountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get data to be validated from the request.
     *
     * @return array
     */

    public function store()
    {
        return [
            'type_id'                 => ['required', 'integer', 'exists_not_soft_deleted:account_types,id'],
            'classification_id'       => ['nullable', 'integer', 'exists:account_classifications,id'],
            'sub_type_id'             => ['nullable', 'integer', 'exists_not_soft_deleted:account_types,id'],
            'code'                    => ['required', 'string', 'unique:accounts,code'],
            'name'                    => ['required', 'string', 'unique:accounts,name'],
            'address'                 => ['nullable', 'string'],
            'tel'                     => ['nullable', 'string'],
            'mobile'                  => ['nullable'],
            'email'                   => ['nullable', 'email'],
            'website_link'            => ['nullable', 'url'],
            'notes'                   => ['nullable', 'string'],
            'active_date'             => ['required', 'string', 'max:191', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'inactive_date'           => ['nullable', 'string', 'max:191', 'after_or_equal:active_date', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
        ];
    }
    public function update()
    {
        return [
            'type_id'       => ['required', 'integer', 'exists_not_soft_deleted:account_types,id'],
            'sub_type_id'   => ['nullable', 'integer', 'exists:account_classifications,id'],
            'sub_type_id'   => ['nullable', 'integer', 'exists_not_soft_deleted:account_types,id'],
            'name'          => [
                'required',
                'string',
                // Rule::unique('accounts')->ignore($this->id)
            ],
            'code'          => [
                'required',
                'string',
                // Rule::unique('accounts')->ignore($this->id)
            ],
            'address'       => ['nullable', 'string'],
            'tel'           => ['nullable', 'string'],
            'mobile'        => ['nullable'],
            'email'         => ['nullable', 'email'],
            'website_link'  => ['nullable', 'url'],
            'notes'         => ['nullable', 'string'],
            'active_date'   => ['required', 'date', 'string', 'max:191', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'inactive_date' => ['nullable', 'date', 'string', 'max:191', 'after_or_equal:active_date', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {
            // throw new CrmException($this->)
            return $this->store();
        }
        if ($this->isMethod('PUT')) {
            return $this->update();
        }
    }
}
