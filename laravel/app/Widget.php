<?php

namespace App;

use App\Models\WidgetType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Widget extends Model
{
    use SoftDeletes;

    protected $guard_name = 'api';

    protected $table = 'widgets';
        /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'widget_module_id','type_id','name','cols','sort','created_at','updated_at','widgetable_type'
    ];
    protected $hidden=['pivot'];
    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function module(){
        return $this->belongsTo(WidgetModule::class,'widget_module_id');
    }
    public function users(){
        return $this->belongsToMany(User::class,'user_widget');
    }

    public static function latestSort(){
        return Widget::orderBy('sort','DESC')->get()->first()->sort;
    }

    public static function sortExists($sort)
    {
        return Widget::where('sort',120)->exists();
    }

    public function type(): BelongsTo
    {
        return $this->belongsTo(WidgetType::class);
    }
}
