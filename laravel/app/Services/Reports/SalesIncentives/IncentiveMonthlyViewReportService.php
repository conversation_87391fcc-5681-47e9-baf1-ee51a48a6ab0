<?php

namespace App\Services\Reports\SalesIncentives;

use App\Line;
use App\LineDivision;
use App\Models\IncentiveMapping;
use App\Models\PostVisitKpi;
use App\Product;
use App\Services\Enums\KPITypes;
use App\Services\Reports\SalesIncentives\Traits\BaseSalesTargets;
use App\Services\Reports\SalesIncentives\Traits\CacheUtils;
use App\Services\Reports\SalesIncentives\Traits\IncentiveCalculations;
use App\Services\Reports\SalesIncentives\Traits\ServiceConfigurable;
use App\Services\Sales\SalesIncentiveHolder;
use Carbon\CarbonPeriod;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class IncentiveMonthlyViewReportService
{
    use CacheUtils;
    use BaseSalesTargets;
    use ServiceConfigurable;

    const TOTAL_PRODUCT_VALUE_PERCENTAGE = 0.8;
    const MR_PRODUCT_VALUE_PERCENTAGE = 0.2;
    const MR_KPIS_PERCENTAGE = 0.05;
    const ONE_HUNDRED_PERCENTAGE = 100;
    const SEVENTY_FIVE_RATIO = 0.75;
    const TWENTY_FIVE_RATIO = 0.25;
    const ZERO_DEFAULT = 0.0;
    const TWO_DECIMAL_ROUND = 2;

    public function __construct(private readonly SalesIncentiveHolder $salesIncentiveHolder)
    {
    }

    private int $cacheTimeout = 60;
    private int $perDivOrUserFilter;
    private Carbon $from;
    private Carbon $to;
    private bool $isChecked;
    private array $fromMonth;
    private string $fromYear;
    private array $years = [];
    private array $months = [];
    private CarbonPeriod $period;
    private int $divisionType;
    private mixed $mappingType;


    public function setMappingType(mixed $mappingType): self
    {
        $this->mappingType = $mappingType;
        return $this;
    }

    public function setDivisionType(int $divisionType): self
    {
        $this->divisionType = $divisionType;
        return $this;
    }

    public function setPeriod(CarbonPeriod $period): self
    {
        $this->period = $period;
        return $this;
    }

    public function setMonths(array $months): self
    {
        $this->months = $months;
        return $this;
    }

    public function setYears(array $years): self
    {
        $this->years = $years;
        return $this;
    }

    public function setFromYear(string $fromYear): self
    {
        $this->fromYear = $fromYear;
        return $this;
    }

    public function setFromMonth(array $fromMonth): self
    {
        $this->fromMonth = $fromMonth;
        return $this;
    }

    public function setIsChecked(bool $isChecked): self
    {
        $this->isChecked = $isChecked;
        return $this;
    }

    public function setTo(Carbon $to): self
    {
        $this->to = $to;
        return $this;
    }

    public function setFrom(Carbon $from): self
    {
        $this->from = $from;
        return $this;
    }

    public function setPerDivOrUserFilter(int $perDivOrUserFilter): self
    {
        $this->perDivOrUserFilter = $perDivOrUserFilter;
        return $this;
    }

    public function setCacheTimeout(int $cacheTimeout): self
    {
        $this->cacheTimeout = $cacheTimeout;
        return $this;
    }


    public function monthView($object, Line $line, Collection $products): Collection
    {
        $belowDivisions = $this->getBelowDivisions($object, $line);

        [$productData, $totalIncentives, $user] = $this->getProductDataProcessing($products, $line, $object, $belowDivisions);
        $roleId = $user?->roles->first()?->id;

        $coverage = $this->salesIncentiveHolder->calculateKpiValue(KPITypes::COVERAGE, $roleId, $user?->id);
        $frequency = $this->salesIncentiveHolder->calculateKpiValue(KPITypes::FREQUENCY, $roleId, $user?->id);
        $callRate = $this->salesIncentiveHolder->calculateKpiValue(KPITypes::CALL_RATE, $roleId, $user?->id);
        $coachingRatio = $this->salesIncentiveHolder->calculateKpiValue(KPITypes::COACHING_RATIO, $roleId, $user?->id);
        $coveredCoaching = $this->salesIncentiveHolder->calculateKpiValue(KPITypes::COVERED_COACHING, $roleId, $user?->id);
        $vacantRatio = $this->salesIncentiveHolder->calculateKpiValue(KPITypes::VACANT_RATIO, $roleId, $user?->id);
        $managerCoverage = $this->salesIncentiveHolder->calculateKpiValue(KPITypes::MANAGER_COVERAGE, $roleId, $user?->id);
        $mkRatio = $this->salesIncentiveHolder->calculateKpiValue(KPITypes::M_K, $roleId, $user?->id);

        $inCharge_date = $user?->pivot?->from_date ?? '';
        $excused_date = $user?->pivot?->to_date ?? '';

        $charge_dates = $inCharge_date . ' ' . $excused_date;

        $userRelatedFields = compact(
            'coverage',
            'roleId',
            'frequency',
            'callRate',
            'totalIncentives',
            'object',
            'charge_dates',
            'coachingRatio',
            'coveredCoaching',
            'vacantRatio',
            "managerCoverage",
            "mkRatio",
        );

        $total = $this->calculateTotal(
            $productData,
            $object,
            $line,
            $userRelatedFields
        );

        return $productData->add($total);
    }


    private function getBelowDivisions($object, $line)
    {
        $cacheKey = "below_divisions_{$this->perDivOrUserFilter}_{$object->id}_{$line->id}_{$this->divisionType}";
        return Cache::remember($cacheKey, $this->cacheTimeout, function () use ($object, $line) {
            return $object->getBelowDivisions($this->from, $this->to)->where('division_type_id', '=', $this->divisionType)->unique('id')->pluck('id')->toArray();

        });
    }

    private function getProductDataProcessing(Collection $lineProducts, Line $line, $object, $belowDivisions)
    {
        $months = implode(',', $this->fromMonth);
        $cacheKey = "product_data_{$this->perDivOrUserFilter}_{$object->id}_{$line->id}_{$months}_{$this->fromYear}";
        $totalIncentive = self::ZERO_DEFAULT;
        return Cache::remember($cacheKey, $this->cacheTimeout, function () use (
            $lineProducts,
            $line,
            $object,
            $belowDivisions,
            &$totalIncentive,
        ) {

            $sales = $this->sales(
                $this->from->format('Y-m-d'),
                $this->to->format('Y-m-d'),
                $belowDivisions,
                $lineProducts->pluck('product_id')->toArray(),
                $line->id,
                mappingTypeId: $this->mappingType,
            );

            $targets = $this->targets(
                $belowDivisions,
                $lineProducts->pluck('product_id')->pluck("id")->toArray(),
                $this->months,
                $this->years
            );
            $employees = $object->users($this->from, $this->to)
                ->with("roles")?->where('line_users_divisions.line_id', $line->id)->get();

            $role = $employees->first()?->roles?->first();

            $user = $employees->first();

            $userRelatedFields = compact('role');

            $firstIncentive = $this->salesIncentiveHolder->getFirstIncentive($role?->id);

            return [$lineProducts->map(function ($productData) use (
                &$totalIncentive,
                $userRelatedFields,
                $object,
                $employees,
                $role,
                $line,
                $sales,
                $firstIncentive,
                $targets
            ) {
                $product = $productData->product;
                $data = $this->initializeProductData($object, $employees, $line, $product);

                $sumSalesUnits = self::ZERO_DEFAULT;
                $sumSalesValues = self::ZERO_DEFAULT;
                $sumTargetUnits = self::ZERO_DEFAULT;
                $sumTargetValues = self::ZERO_DEFAULT;


                foreach ($this->period as $date) {

                    [$sale, $target] = $this->getProductTargetsSales($sales, $targets, $product, $date);

                    $this->updateProductDataForDate($data, $date, $sale, $target, $object);

                    $sumSalesUnits += $sale['salesUnit'];
                    $sumSalesValues += $sale['salesValue'];
                    $sumTargetUnits += $target['targetUnit'];
                    $sumTargetValues += $target['targetValue'];
                }

                $this->updateProductTotals(
                    $product, $totalIncentive,
                    $data, $userRelatedFields,
                    $firstIncentive, $sumSalesUnits,
                    $sumSalesValues, $sumTargetUnits, $sumTargetValues
                );

                return $data;
            }), $totalIncentive, $user];
        });
    }

    public function getProductDataPerProduct(Collection $collection, $product): Collection
    {
        $productExists = $collection->has($product->id);
        return $productExists ? $collection->get($product->id) : collect();
    }

    public function getProductDataPerDate(Collection $collection, $date)
    {
        return $collection->where("date", $date)->first();
    }

    public function getProductTargetsSales(Collection $sales, Collection $targets, $product, $date = null): array
    {
        $salesCollection = $this->getProductDataPerProduct($sales, $product);
        $targetsCollection = $this->getProductDataPerProduct($targets, $product);

        if (is_null($date)) {
            $salesUnit = $salesCollection->sum("quantity");
            //            $salesValue = $salesCollection->sum("value");
            $salesValue = $salesCollection->sum("sales_value");

            $targetsUnit = $targetsCollection->sum("target");
            $targetsValue = $targetsCollection->sum("value");
            $targetsValue = $targetsValue != 0 ? $targetsValue : $targetsCollection->sum("target_value");


            return [
                ['salesUnit' => round($salesUnit), 'salesValue' => round($salesValue, self::TWO_DECIMAL_ROUND)],
                ['targetUnit' => round($targetsUnit), 'targetValue' => round($targetsValue, self::TWO_DECIMAL_ROUND)]
            ];
        }

        $date = $date->format('Y-m');
        $sale = $this->getProductDataPerDate($salesCollection, $date);


        $salesUnit = $sale ? $sale->quantity : self::ZERO_DEFAULT;

        if ($sale) {
            $salesValue = $sale->value != 0 ? $sale->value : $sale->sales_value;
        } else {
            $salesValue = self::ZERO_DEFAULT;
        }

        $target = $this->getProductDataPerDate($targetsCollection, $date);
        $targetsUnit = $target ? $target->target : self::ZERO_DEFAULT;
        if ($target) {
            $targetsValue = $target->value != 0 ? $target->value : $target->target_value;
        } else {
            $targetsValue = self::ZERO_DEFAULT;
        }

        return [
            ['salesUnit' => round($salesUnit ), 'salesValue' => round($salesValue, self::TWO_DECIMAL_ROUND)],
            ['targetUnit' => round($targetsUnit), 'targetValue' => round($targetsValue, self::TWO_DECIMAL_ROUND)]
        ];
    }

    private function initializeProductData($object, $employees, $line, $product): Collection
    {

        return collect([
            'id' => $object->id,
            'line' => $line->name,
            'line_id' => $line->id,
            "product_id" => $product->id,
            'division' => $object?->name,
            'employee' => $employees->pluck('fullname')->implode(','),
            'emp_code' => $employees->pluck('emp_code')->implode(','),
            'product' => $product->name,
            'p_w' => number_format($product->p_w, self::TWO_DECIMAL_ROUND),
            'brand' => $product->brands()?->first()?->name ?? '',
        ]);
    }

    private function updateProductDataForDate(Collection &$data, $date, $sales, $target, $object): void
    {
        $monthAchieveUnits = $target['targetUnit'] ? (round($sales['salesUnit'] / $target['targetUnit'] * self::ONE_HUNDRED_PERCENTAGE, self::TWO_DECIMAL_ROUND)) : self::ZERO_DEFAULT;
        $monthAchieveValues = $target['targetValue'] ? (round($sales['salesValue'] / $target['targetValue'] * self::ONE_HUNDRED_PERCENTAGE, self::TWO_DECIMAL_ROUND)) : self::ZERO_DEFAULT;

        $first = Carbon::parse($date)->firstOfMonth();
        $end = Carbon::parse($date)->endOfMonth();

        $data = $data->put($date->format('M') . ' emp', $this->perDivOrUserFilter == 1 ? $object->users($first, $end)->first()?->fullname : '');
        $data = $data->put($date->format('M') . ' SU', $sales['salesUnit']);
        $data = $data->put($date->format('M') . ' SV', $sales['salesValue']);
        $data = $data->put($date->format('M') . ' TU', $target['targetUnit']);
        $data = $data->put($date->format('M') . ' TV', $target['targetValue']);
        $data = $data->put($date->format('M') . ' AU', $monthAchieveUnits . '%');
        $data = $data->put($date->format('M') . ' AV', number_format($monthAchieveValues) . '%');

    }

    private function updateProductTotals(
        Product           $product,
        float             &$totalIncentive,
        Collection        &$data,
        array             $userRelatedFields,
        ?IncentiveMapping $firstIncentive,
                          $sumSalesUnits,
                          $sumSalesValues,
                          $sumTargetUnits,
                          $sumTargetValues
    ): void
    {
        $achievementUnits = $sumTargetUnits
            ? (round($sumSalesUnits / $sumTargetUnits * self::ONE_HUNDRED_PERCENTAGE, self::TWO_DECIMAL_ROUND))
            : self::ZERO_DEFAULT;
        $achievementValues = $sumTargetValues
            ? (round($sumSalesValues / $sumTargetValues * self::ONE_HUNDRED_PERCENTAGE, self::TWO_DECIMAL_ROUND))
            : self::ZERO_DEFAULT;
        $product_value = $this->salesIncentiveHolder->calculateProductValue(
            $userRelatedFields['role'],
            $achievementValues,
            $firstIncentive,
            $product->p_w
        );
        $totalIncentive += $product_value;

        $data = $data->put('Tot SU', $sumSalesUnits);
        $data = $data->put('Tot SV', $sumSalesValues);
        $data = $data->put('Tot TU', $sumTargetUnits);
        $data = $data->put('Tot TV', $sumTargetValues);
        $data = $data->put('Ach U', $achievementUnits . '%');
        $data = $data->put('Ach V', $achievementValues . '%');
        $data = $data->put('product_value', $product_value);
        $data = $data->put('is_total', false);

    }

    private function calculateTotal($result, $object, $line, array $userRelatedFields): array
    {
        $total = $this->initializeTotalData($object, $line);

        $totalSalesUnits = self::ZERO_DEFAULT;
        $totalSalesValues = self::ZERO_DEFAULT;
        $totalTargetUnits = self::ZERO_DEFAULT;
        $totalTargetValues = self::ZERO_DEFAULT;

        foreach ($this->period as $date) {
            $this->updateTotalForDate(
                $total,
                $result,
                $date,
                $totalSalesUnits,
                $totalSalesValues,
                $totalTargetUnits,
                $totalTargetValues
            );
        }

        $this->updateTotalTotals(
            $total,
            $totalSalesUnits,
            $totalSalesValues,
            $totalTargetUnits,
            $totalTargetValues,
            $userRelatedFields
        );

        return $total;
    }

    private function initializeTotalData($object, $line): array
    {
        return [
            'id' => $object->id,
            'line' => $line->name,
            'division' => $object?->name,
            'employee' => $object->users($this->from, $this->to)?->where('line_users_divisions.line_id', $line->id)->pluck('fullname')->implode(','),
            'emp_code' => $object->users($this->from, $this->to)?->where('line_users_divisions.line_id', $line->id)->pluck('emp_code')->implode(','),
            'product' => 'Total',
            'brand' => '',
            'color' => $object?->DivisionType->color,
        ];
    }

    private function updateTotalForDate(
        array &$total,
              $result,
              $date,
              &$totalSalesUnits,
              &$totalSalesValues,
              &$totalTargetUnits,
              &$totalTargetValues
    ): void
    {
        $saleU = $result->sum($date->format('M') . ' SU');
        $saleV = $result->sum($date->format('M') . ' SV');
        $targetU = $result->sum($date->format('M') . ' TU');
        $targetV = $result->sum($date->format('M') . ' TV');

        $totalSalesUnits += $saleU;
        $totalSalesValues += $saleV;
        $totalTargetUnits += $targetU;
        $totalTargetValues += $targetV;

        $achPerDateU = $targetU ? (round($saleU / $targetU * self::ONE_HUNDRED_PERCENTAGE, self::TWO_DECIMAL_ROUND)) : self::ZERO_DEFAULT;
        $achPerDateV = $targetV ? (round($saleV / $targetV * self::ONE_HUNDRED_PERCENTAGE, self::TWO_DECIMAL_ROUND)) : self::ZERO_DEFAULT;

        $total[$date->format('M') . ' emp'] = '';
        $total[$date->format('M') . ' emp'] = '';
        $total[$date->format('M') . ' SU'] = $saleU;
        $total[$date->format('M') . ' SV'] = round($saleV, self::TWO_DECIMAL_ROUND);
        $total[$date->format('M') . ' TU'] = $targetU;
        $total[$date->format('M') . ' TV'] = round($targetV, self::TWO_DECIMAL_ROUND);
        $total[$date->format('M') . ' AU'] = $achPerDateU . '%';
        $total[$date->format('M') . ' AV'] = $achPerDateV . '%';
    }

    private function updateTotalTotals(
        array &$total,
              $totalSalesUnits,
              $totalSalesValues,
              $totalTargetUnits,
              $totalTargetValues,
        array $userRelatedFields
    ): void
    {
        $achievement_value = $totalTargetValues ? $totalSalesValues / $totalTargetValues * self::ONE_HUNDRED_PERCENTAGE : self::ZERO_DEFAULT;

        $VALUE = $this->salesIncentiveHolder->getFirstIncentive($userRelatedFields['roleId'])?->achievement_rule ?? INF;

        $coverage = $userRelatedFields['coverage'];
        $frequency = $userRelatedFields['frequency'];
        $callRate = $userRelatedFields['callRate'];

        $vacantRatio = $userRelatedFields['vacantRatio'];
        $coachingRatio = $userRelatedFields['coachingRatio'];
        $coveredCoaching = $userRelatedFields['coveredCoaching'];
        $managerCoverage = $userRelatedFields['managerCoverage'];
        $mkRatio = $userRelatedFields["mkRatio"];

        $total_product_values = $userRelatedFields['totalIncentives'];

        [$coverage, $frequency, $callRate, $total_product_values] = $achievement_value > $VALUE
            ? [$coverage, $frequency, $callRate, $total_product_values] : array_fill(0, 4, self::ZERO_DEFAULT);

        $kpis = $coverage + $frequency + $callRate;
        $districtKPIS = $coachingRatio + $coveredCoaching + $vacantRatio;

        $eighty_total_percent_product_value = $total_product_values * self::TOTAL_PRODUCT_VALUE_PERCENTAGE;

        $parentEmployees = LineDivision::find($userRelatedFields['object']->parent_id)
            ?->users($this->from, $this->to)
            ?->with("roles")
            ?->where('line_users_divisions.line_id', $userRelatedFields['object']->line_id)
            ?->get() ?? collect();

        $parentRole = $parentEmployees->first()?->roles?->first();;

        $this->salesIncentiveHolder->trackManagerIncentive(
            $userRelatedFields['object']->parent_id,
            $parentRole?->id,
            $eighty_total_percent_product_value
        );

        $incentiveValue = $this->salesIncentiveHolder->getIncentivePerAchievement($achievement_value, $userRelatedFields['roleId']);

        $kpi_value = (($kpis * self::MR_KPIS_PERCENTAGE) * (self::MR_PRODUCT_VALUE_PERCENTAGE * $incentiveValue)); // MR

        $total_incentive = $eighty_total_percent_product_value + $kpi_value;


        $achievementUnitsPerUser = $totalTargetUnits ? (round($totalSalesUnits / $totalTargetUnits * self::ONE_HUNDRED_PERCENTAGE, self::TWO_DECIMAL_ROUND)) : self::ZERO_DEFAULT;

        $achievementValuesPerUser = round($achievement_value, self::TWO_DECIMAL_ROUND) . '%';


        $total['Tot SU'] = $totalSalesUnits;
        $total['Tot SV'] = $totalSalesValues;
        $total['Tot TU'] = $totalTargetUnits;
        $total['Tot TV'] = $totalTargetValues;
        $total['Ach U'] = $achievementUnitsPerUser . '%';
        $total['Ach V'] = $achievementValuesPerUser;
        $total['product_value'] = round($eighty_total_percent_product_value, self::TWO_DECIMAL_ROUND);
        $total['cov'] = $userRelatedFields['coverage'];
        $total['freq'] = $userRelatedFields['frequency'];
        $total['call_r'] = $userRelatedFields['callRate'];
        $total['kpis_value'] = round($kpi_value, self::TWO_DECIMAL_ROUND);

        $total['coaching_ratio'] = $coachingRatio;
        $total['covered_coaching'] = $coveredCoaching;
        $total['vacant_ratio'] = $vacantRatio;
        $total["manager_coverage"] = $managerCoverage;
        $total["m_k_ratio"] = $mkRatio;
        $total["district_kpis"] = $districtKPIS;

        $total['total_incentive'] = round($total_incentive, self::TWO_DECIMAL_ROUND);
        $total['75%'] = round($total_incentive * self::SEVENTY_FIVE_RATIO, self::TWO_DECIMAL_ROUND);
        $total['25%'] = round($total_incentive * self::TWENTY_FIVE_RATIO, self::TWO_DECIMAL_ROUND);
        $total['is_total'] = true;
        $total['in_charge_date'] = $userRelatedFields['charge_dates'];

    }


}
