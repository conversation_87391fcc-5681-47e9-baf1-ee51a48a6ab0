<?php

namespace App\Services\Enums;


use App\Traits\EnumTrait;
use Illuminate\Support\Carbon;

enum AlertPeriodPattern: string
{
    use EnumTrait;
    case ONCE = 'Once';
    case DAILY = 'Daily';
    case MONTHLY = 'Monthly';
    case WEEKLY = 'Weekly';
    case QUARTERLY = 'Quarter';

    public function getDates(?string $date = null): array
    {
        // If no date provided, use current date
        $baseDate = $date ? Carbon::parse($date) : Carbon::now();

        // Calculate start and end dates based on pattern
        [$startDate, $endDate] = match ($this) {
            AlertPeriodPattern::DAILY => [
                $baseDate->copy()->subDay()->startOfDay(),
                $baseDate->copy()->subDay()->endOfDay()
            ],
            AlertPeriodPattern::WEEKLY => [
                $baseDate->copy()->subWeek()->startOfDay(),
                $baseDate->copy()->subDay()->endOfDay()
            ],
            AlertPeriodPattern::MONTHLY => [
                $baseDate->copy()->subMonth()->startOfDay(),
                $baseDate->copy()->subDay()->endOfDay()
            ],
            AlertPeriodPattern::QUARTERLY => [
                $baseDate->copy()->subQuarter()->startOfDay(),
                $baseDate->copy()->subDay()->endOfDay()
            ],
            AlertPeriodPattern::ONCE => [
                $baseDate->copy()->startOfDay(),
                $baseDate->copy()->endOfDay()
            ],
        };

        return [
            $startDate->format('Y-m-d H:i:s'),
            $endDate->format('Y-m-d H:i:s')
        ];
    }


}
