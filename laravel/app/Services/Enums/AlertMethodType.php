<?php

namespace App\Services\Enums;


enum AlertMethodType: string
{

    case SEND_WITH_INTERNAL_MESSAGING = 'Internal Messaging';
    case SEND_WITH_WIDGET = 'Widget';
    case SEND_WITH_MAIL = 'Email';

    public function getCommand(): string
    {
        return match ($this) {
            AlertMethodType::SEND_WITH_INTERNAL_MESSAGING => "app:send-alert-with-internal-messaging",
            AlertMethodType::SEND_WITH_MAIL => "app:send-alert-with-mail",
            AlertMethodType::SEND_WITH_WIDGET => "app:send-alert-with-widget"
        };
    }
}
