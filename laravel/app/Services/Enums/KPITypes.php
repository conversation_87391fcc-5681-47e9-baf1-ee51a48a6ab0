<?php

namespace App\Services\Enums;


use App\Traits\EnumTrait;

enum KPITypes: string
{
    use EnumTrait;

    case CHANGE_PLAN = 'Change Plan';
    case PLAN_ACHIEVEMENT = 'Plan Achievement';
    case COVERAGE = 'Coverage';
    case AM_COVERAGE = 'AM Coverage';
    case PM_COVERAGE = 'PM Coverage';
    case PH_COVERAGE = 'PH Coverage';
    case CALL_RATE = 'Call Rate';
    case AM_CALL_RATE = 'AM Call Rate';
    case PM_CALL_RATE = 'PM Call Rate';
    case PH_CALL_RATE = 'PH Call Rate';
    case FREQUENCY = 'Frequency';
    case AM_FREQUENCY = 'AM Frequency';
    case PM_FREQUENCY = 'PM Frequency';
    case PH_FREQUENCY = 'PH Frequency';
    case VACANT_RATIO = 'Vacant Ratio';
    case COACHING_RATIO = 'Coaching Ratio';
    case COVERED_COACHING = 'Covered Coaching';
    case MANAGER_COVERAGE = 'Manager Coverage';
    case HEAD_RATIO = 'Head Ratio';
    case D_V_MENTION_RATIO = 'D V Mention Ratio';
    case MR_EXCELLENT = 'Mr Excellent';
    case M_K = 'M K';


    public function getPercentageKeyName(): string
    {
        return match ($this) {
            self::COVERAGE => 'coverage',
            self::AM_COVERAGE => 'am_coverage',
            self::PM_COVERAGE => 'pm_coverage',
            self::PH_COVERAGE => 'ph_coverage',
            self::CALL_RATE => 'call_rate',
            self::AM_CALL_RATE => 'am_call_rate',
            self::PM_CALL_RATE => 'pm_call_rate',
            self::PH_CALL_RATE => 'ph_call_rate',
            self::CHANGE_PLAN => 'change_plans',
            self::PLAN_ACHIEVEMENT => 'achievement',
            self::FREQUENCY => 'ratio(meet)',
            self::AM_FREQUENCY => 'am_frequency',
            self::PM_FREQUENCY => 'pm_frequency',
            self::PH_FREQUENCY => 'ph_frequency',
            self::VACANT_RATIO => 'vacant_ratio',
            self::COACHING_RATIO => 'coaching_ratio',
            self::COVERED_COACHING => 'covered_coaching',
            self::MANAGER_COVERAGE => 'manager_coverage',
            self::HEAD_RATIO => 'head_ratio',
            self::MR_EXCELLENT => 'mr_excellent',
            self::D_V_MENTION_RATIO => 'd_v_mention_ratio',
            self::M_K => 'm_k',
        };
    }
}
