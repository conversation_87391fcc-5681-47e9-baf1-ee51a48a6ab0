<?php

namespace App\Services\Enums;

use Carbon\CarbonInterface;

enum WeekDays: int 
{
     case SUNDAY = 0;
     case MONDAY = 1;
     case TUESDAY = 2;
     case WEDNESDAY = 3;
     case THURSDAY = 4;
     case FRIDAY = 5;
     case SATURDAY = 6;
 

    public function getName(): string
    {
        return match ($this) {
            self::SUNDAY => 'Sunday',
            self::MONDAY => 'Monday',
            self::TUESDAY => 'Tuesday',
            self::WEDNESDAY => 'Wednesday',
            self::THURSDAY => 'Thursday',
            self::FRIDAY => 'Friday',
            self::SATURDAY => 'Saturday',
        };
    }

    public static function fromName(string $name): ?self
    {
        return match ($name) {
            'SUNDAY' => self::SUNDAY,
            'MONDAY' => self::MONDAY,
            'TUESDAY' => self::TUESDAY,
            'WEDNESDAY' => self::WEDNESDAY,
            'THURSDAY' => self::THURSDAY,
            'FRIDAY' => self::FRIDAY,
            'SATURDAY' => self::SATURDAY,
            default => null, // Return null if the name doesn't match
        };
    }
    public function toCarbonConstant(): int
    {
        return match ($this) {
            self::SUNDAY => CarbonInterface::SUNDAY,
            self::MONDAY => CarbonInterface::MONDAY,
            self::TUESDAY => CarbonInterface::TUESDAY,
            self::WEDNESDAY => CarbonInterface::WEDNESDAY,
            self::THURSDAY => CarbonInterface::THURSDAY,
            self::FRIDAY => CarbonInterface::FRIDAY,
            self::SATURDAY => CarbonInterface::SATURDAY,
        };
    }
}




