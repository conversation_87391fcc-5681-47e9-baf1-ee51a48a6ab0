<?php

namespace App\Services\Analyzers;

use App\Exceptions\CrmException;
use App\Services\Enums\AnalyzerCallRate;
use Illuminate\Support\Collection;

class AnalyzerCallRateService
{

    private Collection $callRate;
    private const Actual = "Actual";
    public const Callrate = "Callrate";
    public function __construct()
    {
        $this->callRate = collect();
        $this->init();
    }

    private function init(): void
    {
        foreach (AnalyzerCallRate::cases() as $case) {
            $name = explode("_", $case->name)[0];
            $this->addNewCallRate(AnalyzerCallRate::from($case->value), $name);
        }
    }

    private function addNewCallRate(AnalyzerCallRate $key, string $name)
    {
        // $this->callRate->push(
        //     collect([
        //         "name" => $key->value,
        //         "value" => 0,
        //         "size" => 0
        //     ])
        // );
        $valueKeyCallRate = $this->getKeyName($key);

        $this->callRate->push(
            collect([
                "name" => $key->value,
                "value" => collect([
                    "name" => $name,
                    "data" => collect([
                        ["name" => $valueKeyCallRate, "value" => 0],
                        ["name" => self::Callrate, "value" => 0],
                    ]),
                ]),
            ])
        );
    }

    private function setCallRate(AnalyzerCallRate $key, Collection $value): void
    {
        $this->callRate->transform(function ($item) use ($key, $value) {
            if ($item["name"] == $key->value) {
                $item = $value;
            }
            return $item;
        });
    }

    public function getKeyName(AnalyzerCallRate $key): string
    {
        return self::Actual;
    }

    public function set(AnalyzerCallRate $needle, float $callRate, int $count): void
    {
        // $data = $this->get($needle);
        // $name = $this->getKeyName($needle);
        // $data["value"] = $callRate;
        // $data["size"] = 0;


        // $this->setCallRate($needle, $data);

        $data = $this->get($needle);
        $name = $this->getKeyName($needle);

        $data['value']['data']->transform(function ($item) use ($name, $callRate, $count) {
            if ($item['name'] == self::Callrate) {
                $item['value'] = $callRate;
            }

            if ($item['name'] == $name) {
                $item['value'] = $count;
            }
            return $item;
        });

        $this->setCallRate($needle, $data);
    }

    public function addTo(AnalyzerCallRate $needle, float $callRate, int $count): void
    {
        // $data = $this->get($needle);

        // $data["value"] += $callRate;
        // $data["size"] += 1;

        // $this->setCallRate($needle, $data);

        $data = $this->get($needle);
        $name = $this->getKeyName($needle);

        $data['value']['data']->transform(function ($item) use ($name, $callRate, $count) {
            if ($item['name'] == self::Callrate) {
                $item['value'] += $callRate;
            }

            if ($item['name'] == $name) {
                $item['value'] += $count;
            }
            return $item;
        });

        $this->setCallRate($needle, $data);
    }

    private function get(AnalyzerCallRate $needle): Collection
    {
        return $this->callRate->firstWhere("name", $needle->value);
    }

    public function all()
    {
        return $this->callRate->values()->pluck("value");
    }
}
