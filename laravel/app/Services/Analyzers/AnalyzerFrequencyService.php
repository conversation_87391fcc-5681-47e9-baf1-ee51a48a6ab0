<?php

namespace App\Services\Analyzers;

use App\Services\Enums\AnalyzerFrequency;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class AnalyzerFrequencyService
{
    /**
     * @var array<string, array<string, mixed>>
     */
    private array $frequency = [];

    public function __construct()
    {
        $this->init();
    }

    /**
     * Initialize frequency data for all enum cases
     */
    private function init(): void
    {
        foreach (AnalyzerFrequency::cases() as $case) {
            $this->frequency[$case->value] = [
                "name" => $case->value,
                "value" => 0,
                "size" => 0
            ];
        }
    }

    /**
     * Set frequency value for a specific analyzer
     */
    public function set(AnalyzerFrequency $frequency, float $value): void
    {
        $this->frequency[$frequency->value]["value"] = $value;
        $this->frequency[$frequency->value]["size"] = 0;
    }

    /**
     * Add to existing frequency value and increment size counter
     */
    public function addTo(AnalyzerFrequency $frequency, float $value): void
    {
        $this->frequency[$frequency->value]["value"] += $value;
        $this->frequency[$frequency->value]["size"] += 1;
    }

    /**
     * Get data for a specific analyzer frequency
     *
     * @return array<string, mixed>
     */
    public function get(AnalyzerFrequency $frequency): array
    {
        return $this->frequency[$frequency->value];
    }

    /**
     * Get all frequency data without size information
     *
     * @return Collection
     */
    public function all(): Collection
    {
        return collect($this->frequency)->map(function ($item) {
            return [
                'name' => $item['name'],
                'value' => $item['value']
            ];
        })->values();
    }

}
