<?php

namespace App\Services\Analyzers;

use App\Sale;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use PHPUnit\TestRunner\TestResult\Collector;

class AnalyzerSaleService
{

    public Collection $analysis;

    public function __construct()
    {
        $this->analysis = new Collection();
    }

    public function addDistributors(Collection $distributors): void
    {
        foreach ($distributors as $distributor) {
            if (!$this->isDistributorExists($distributor->name))
                $this->addElement($distributor->name, true);
        }
    }

    public function addProducts(Collection $products): void
    {
        foreach ($products as $product) {
            if (!$this->isProductExists($product->name))
                $this->addElement($product->name, false);
        }
    }


    private function get(string $name, bool $isDistributor): array
    {
        return $this->analysis->where("name", $name)
            ->where("isDistributor", $isDistributor)->first();
    }


    public function set(array $analysis): void
    {
        $this->analysis->transform(function ($item) use ($analysis) {
            if ($item["name"] == $analysis["name"] && $item['isDistributor'] == $analysis['isDistributor']) {
                $item = $analysis;
            }
            return $item;
        });
    }

    public function incrementDistributor(string $name, float $value): void
    {
        $data = $this->get($name, true);
        $data['value'] += $value;
        $this->set($data);
    }

    public function incrementProduct(string $name, float $value): void
    {
        $data = $this->get($name, false);
        $data['value'] += $value;
        $this->set($data);
    }

    private function isDistributorExists($name): bool
    {
        return !$this->analysis->where("name", $name)
            ->where("isDistributor", true)
            ->isEmpty();
    }

    private function isProductExists($name): bool
    {
        return !$this->analysis->where("name", $name)
            ->where("isDistributor", false)
            ->isEmpty();
    }

    private function addElement(string $name, bool $isDistributor): void
    {
        $this->analysis->push([
            'name' => $name,
            'isDistributor' => $isDistributor,
            'value' => 0,
        ]);
    }

    private function getPer(bool $flag): Collection
    {
        $data = $this->analysis->where('isDistributor', $flag)->transform(function ($item) {
            unset($item['isDistributor']);
            return $item;
        });
        $total = $data->sum("value");


        return $data->transform(function ($item) use ($total) {
            $ratio = $total != 0 ? ($item['value'] / $total) * 100 : 0;
            $item['name'] = $item["name"] . " ( " . round($ratio, 2) . "% )";
            return $item;
        });
    }


    public function getPerDistributor(): Collection
    {
        return $this->getPer(true)->values();
    }

    public function getPerProduct(): Collection
    {
        return $this->getPer(false)->values();
    }

    public function getSales($validPrice, $distId, $productId, $lineIds, $divisionIds, $month, $year, $from, $to): Collection
    {
        return Sale::select('sales_details.id as detail_id','mappings.distributor_id as distributor_id','line_divisions.name as name', 'line_divisions.id as div_id','products.id as product_id')
            ->selectRaw('(crm_product_prices.avg_price * crm_sales_details.quantity)  as sale_value')
            ->selectRaw('(crm_product_prices.avg_price * crm_target_details.target)  as target_value')
            ->leftJoin('mapping_sale', 'sales.id', 'mapping_sale.sale_id')
            ->leftJoin('mappings', 'mapping_sale.mapping_id', 'mappings.id')
            ->leftJoin('sales_details', 'sales.id', 'sales_details.sale_id')

            ->leftJoin('products', 'sales.product_id', 'products.id')
            ->leftJoin('line_divisions', 'sales_details.div_id', 'line_divisions.id')
            ->leftJoin(
                'product_prices',
                fn($join) => $join->on('products.id', 'product_prices.product_id')
                    ->where('product_prices.from_date', '<=', $month)
                    ->where(fn($q) => $q->where('product_prices.to_date', '>', (string)Carbon::parse($month)->endOfMonth()->toDateString())
                        ->orWhere('product_prices.to_date', null))
            )
            // ->leftJoin(
            //     'product_prices',
            //     function ($join) use ($validPrice) {
            //         $join->on('products.id', '=', 'product_prices.product_id');
            //         $join
            //             ->where('product_prices.distributor_id', $validPrice?->distributor_id)
            //             ->where('product_prices.avg_price', $validPrice?->avg_price);
            //     }
            // )
            ->leftJoin(
                'target_details',
                function ($join) use ($month, $year, $divisionIds) {
                    $join->on('products.id', '=', 'target_details.product_id');
                    $join->on('line_divisions.id', '=', 'target_details.div_id');
                    $join
                        ->whereIntegerInRaw('target_details.div_id', $divisionIds)
                        ->where(DB::raw("(DATE_FORMAT(crm_target_details.date,'%m'))"), $month->format('m'))
                        ->whereYear('target_details.date', $year);
                }
            )
            ->where('sales.product_id', $productId)
            ->where(fn ($q) => $q->whereIntegerInRaw('mappings.line_id', $lineIds)->orWhere('mappings.line_id', null))
            ->where(fn ($q) => $q->where('mappings.distributor_id', $distId)->orWhere('mappings.distributor_id', null))
            ->whereIntegerInRaw('sales_details.div_id', $divisionIds)
            ->where(DB::raw("(DATE_FORMAT(crm_sales_details.date,'%m'))"), $month->format('m'))
            ->whereYear('sales_details.date', $year)
            ->groupBy('sales_details.id', 'line_divisions.name','products.id', 'mappings.distributor_id','product_prices.avg_price', 'target_details.id')
            ->get()->values();
    }
}
