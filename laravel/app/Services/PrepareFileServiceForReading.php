<?php

namespace App\Services;

use App\Helpers\FastExcelPlus\FastExcelPlus;
use App\Import;
use App\Services\Enums\TemporaryUploadCase;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Storage;
use OpenSpout\Common\Exception\IOException;
use OpenSpout\Common\Exception\UnsupportedTypeException;
use OpenSpout\Reader\Exception\ReaderNotOpenedException;

class PrepareFileServiceForReading
{

    private int $threshold;

    private QueueLoadBalancingService $balancingService;

    public function setThreshold(int $threshold): self
    {
        $this->threshold = $threshold;
        return $this;
    }

    public function __construct(private readonly Import $import,private readonly bool $update)
    {

        $this->balancingService = new QueueLoadBalancingService();

        $this->import->update(
            [
                'job_batch_id' => $this->balancingService->currentBatchId(),
                'case' => $this->update ? TemporaryUploadCase::UPDATE : TemporaryUploadCase::NORMAL,
                'read_from_date' => now(),
                'save_from_date' => now(),
            ]
        );
    }



    /**
     * @throws IOException
     * @throws UnsupportedTypeException
     * @throws ReaderNotOpenedException
     */
    private function read(): \Generator
    {
        return (new FastExcelPlus())
            ->importIterated(Storage::path($this->import->path));
    }


    /**
     * @throws IOException
     * @throws UnsupportedTypeException
     * @throws ReaderNotOpenedException
     */
    public function loop(callable $mapper, callable $callback): void
    {
        $collections = [];
        $iterator = $this->read();
        foreach ($iterator as $collection) {
            $collections [] = $mapper($collection);
            if (count($collections) === $this->threshold) {
                $callback($collections, $this->balancingService->currentBatchId(), $this->balancingService->currentQueue());
                $this->balancingService->next();
                $collections = [];
            }
        }
        if (count($collections) > 0) {
            $callback($collections,$this->balancingService->currentBatchId(), $this->balancingService->currentQueue());
            $this->balancingService->next();
            $collections = [];
        }

        $this->import->update([
            'read_to_date' => now()
        ]);
    }


}
