<?php


namespace App\Services;

use App\ActualVisit;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Line;
use App\Models\CommercialRequest\CommercialRequest;
use App\Models\ListType;
use App\PlanVisit;
use App\Sale;
use App\SaleDetail;
use App\Services\Enums\Ceiling;
use App\User;
use App\UserPosition;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class UserStatus
{

    private $user;

    private $cacheTimeout = 24;
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    public function activeUsers()
    {
        return User::select('id')->whereDoesntHave('positions')->where('status', 'active')->whereHas('divisions')->count()
            + UserPosition::select('id')->whereHas('divisions')->where("from_date", "<=", now())
            ->where(fn($q) => $q->where('to_date', '>=', (string)Carbon::now())
                ->orWhere('to_date', null))->count()
            + User::select('id')->whereDoesntHave('divisions')->whereDoesntHave('positions')->where('menuroles', 'admin')->count();
    }

    public function nonWorkingUsers()
    {
        return User::select('id')->where('status', 'active')->whereDoesntHave('divisions')->count() + UserPosition::select('id')->whereDoesntHave('divisions')->count();
    }

    public function todayPlans()
    {
        return PlanVisit::whereDate("visit_date", now())->where('user_id', $this->user->id)->whereHas('details', function ($q) {
            $q->where('approval', 1);
        })->count();
    }

    public function todayActualPlans()
    {
        return ActualVisit::whereDate("visit_date", now())->where('user_id', $this->user->id)->count();
    }

    public function doctors()
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = $user->lines->pluck('id')->toArray();
        $count = 0;
        if (count($lines) > 3) {
            $count = 0;
        } else {
            $division_type = DivisionType::where('last_level', '=', 1)->get('id');
            $divisions = $user->allBelowDivisions()->where('is_kol', 0)
                ->where('division_type_id', '=', $division_type->first()->id)->unique('id')->pluck('id')->toArray();
            if (ListType::first()->favourite_type == 'Account' ? true : false) {
                $count = (new AccountService)->getAccounts($lines, $divisions,  null, null)->count();
            } else {
                $count = (new DoctorService)->getDoctors($lines, $divisions,  null, null)->count();
            }
        }

        return $count;
    }
    //monthly
    public function coverage()
    {
        /**@var User $user */
        $user = Auth::user();
        $division_type = DivisionType::where('last_level', '=', 1)->get('id');
        $lines = $user->lines->pluck('id')->toArray();
        if (count($lines) > 3) {
            $count = 0;
            return $count;
        } else {
            $divisions = $user->allBelowDivisions()->where('is_kol', 0)
                ->where('division_type_id', '=', $division_type->first()->id)->unique('id')->pluck('id')->toArray();
            $doctors = (new DoctorService)->getDoctors($lines, $divisions,  null, null)->pluck('doctor_id')->unique()->toArray();
            return (ActualVisit::inMonth('visit_date')->where('user_id', $this->user->id)
                ->whereIntegerInRaw('account_dr_id', $doctors)->get()->unique('account_dr_id')
                ->count());
        }

        // return (ActualVisit::inMonth('visit_date')->where('user_id', $this->user->id)->get()->unique('account_dr_id')->count());
    }

    public function requests()
    {
        return CommercialRequest::whereBetween('created_at', [Carbon::now()->firstOfYear(), Carbon::now()])->sum('amount');
    }
    public function target($month)
    {
        return DB::table('target_details')->select([
            DB::raw(
                'sum((
                    SELECT avg_price
                        FROM crm_product_prices
                        WHERE crm_product_prices.product_id = crm_target_details.product_id
                        AND crm_product_prices.deleted_at IS NULL
                        AND crm_product_prices.from_date <= crm_target_details.date
                        AND (crm_product_prices.to_date IS NULL OR crm_product_prices.to_date >= crm_target_details.date)
                        LIMIT 1
                    ) * crm_target_details.target) as target_value'
            ),
        ])

            ->whereRaw('DATE_FORMAT(crm_target_details.date, "%Y-%m") = ?', [$month])
            ->first()->target_value;
    }
    public function sales()
    {
        $maxMonthYear = DB::table('sales')
            ->selectRaw('DATE_FORMAT(MAX(date), "%Y-%m") as max_month_year')
            ->value('max_month_year');
        $value = DB::table('sales')->select([
            DB::raw(
                "
                CASE
                    WHEN SUM(crm_sales.value) = 0 THEN
                        SUM(
                            COALESCE(
                                (
                                    SELECT avg_price
                                    FROM crm_product_prices
                                    WHERE crm_product_prices.product_id = crm_sales.product_id
                                    AND crm_product_prices.deleted_at IS NULL
                                    AND crm_product_prices.distributor_id = crm_sales.distributor_id
                                    AND crm_product_prices.from_date <= crm_sales.date
                                    AND (crm_product_prices.to_date IS NULL OR crm_product_prices.to_date >= crm_sales.date)
                                    LIMIT 1
                                ),
                                (
                                    SELECT avg_price
                                    FROM crm_product_prices
                                    WHERE crm_product_prices.product_id = crm_sales.product_id
                                    AND crm_product_prices.deleted_at IS NULL
                                    AND crm_product_prices.distributor_id IS NULL
                                    AND crm_product_prices.from_date <= crm_sales.date
                                    AND (crm_product_prices.to_date IS NULL OR crm_product_prices.to_date >= crm_sales.date)
                                    LIMIT 1
                                )
                            ) * crm_sales.quantity
                        )
                    ELSE
                        SUM(crm_sales.value)
                END as sales_value"
            ),
        ])
            ->whereRaw('DATE_FORMAT(crm_sales.date, "%Y-%m") = ?', [$maxMonthYear])
            ->whereIn("sales.ceiling", [Ceiling::BELOW, Ceiling::ABOVE])
            ->first()->sales_value;

        return  [
            $maxMonthYear,
            $value
        ];
    }

    private function getLineCurrency()
    {
        return Line::first()?->currency?->name;
    }

    public function getWidgets()
    {
        if ($this->user->hasRole('admin') || $this->user->hasRole('sub admin') || $this->user->hasRole('Gemstone Admin')) {
            $activeUsers = Cache::remember('activeUsers', $this->cacheTimeout, fn() => $this->activeUsers());
            $currency = Cache::remember('currency', $this->cacheTimeout, fn() => $this->getLineCurrency() ?? 'EGP');
            [$month, $sales_value] = Cache::remember('sales', $this->cacheTimeout, fn() => $this->sales());
            $target_value = Cache::remember('targets', $this->cacheTimeout, fn() => $this->target($month));
            $requests = Cache::remember('requests', $this->cacheTimeout, fn() => $this->requests());
            $widgets = collect([
                ['name' => 'Active Employees', 'color' => '#5856d6', 'data' => $activeUsers, 'description' => 'Employees which are linked to divisions'],
                ['name' => 'Request Amount', 'data' => number_format($requests, 2) . ' ' . $currency, 'color' => '#3399FF', 'description' => 'Yearly Requests Amount at ' . Carbon::now()->format('Y')],
                ['name' => 'Target Values', 'color' => 'green', 'data' => number_format($target_value, 2) . ' ' . $currency, 'description' => 'Latest Month Total Target at ' . $month ?? ''],
                ['name' => 'Sales Values', 'data' => number_format($sales_value, 2) . ' ' . $currency, 'color' => '#EE06D6', 'description' => 'Latest Month Total Sales at ' . $month ?? ''],
            ]);
        } else {
            $isAccount = Cache::remember('isAccount', 24, fn() => ListType::first()->favourite_type == 'Account');
            $widgets = collect(
                [
                    ['name' => 'today_approved_plans', 'data' => $this->todayPlans(), 'color' => '#5856d6', 'description' => 'Daily Approved Plans Count'],
                    ['name' => 'today_actuals', 'data' => $this->todayActualPlans(), 'color' => '#3399FF', 'description' => 'Daily Actual Visits Count'],
                    ['name' => $isAccount ? 'accounts' : 'doctors', 'data' => $this->doctors(), 'color' => 'green', 'description' => 'Monthly List Count'],
                    ['name' => 'coverage', 'data' => $this->coverage(), 'color' => '#FFD119', 'description' => 'Monthly Covered List'],

                ]
            );
        }

        return $widgets;
    }
}
