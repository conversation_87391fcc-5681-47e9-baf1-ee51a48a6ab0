<?php

namespace App\Services;


use App\Interfaces\Alerts\Fetchable;
use App\User;
use App\Traits\Reports\UserPerShiftReport;

class UsersWithoutAmReportService implements Fetchable
{
    use UserPerShiftReport;

    private const time = '10:31:00';

    public function fetch(User $user, string $from, string $to): array
    {
        $allBelowUserIds = $this->getAllBelowUsersIds(
            $this->getLines($user, $from, $to),
            $user,
            $from,
            $to
        );

        $withoutVisits = $this->getUsersWithoutVisits($allBelowUserIds, $from, $to);

        $vacationUsers = $this->getUsersOnVacation($from, $to);

        $withoutVisitsAndVacations = array_diff($withoutVisits, $vacationUsers);

        $officeWorkUsers = $this->getOfficeWorkUsers($from, $to);

        $userPositions = $this->getUsersOnPositions();

        $userOfHighLevel = $this->getUsersOnHighLevel();

        return array_diff($withoutVisitsAndVacations, $officeWorkUsers,$userPositions,$userOfHighLevel);

    }

}
