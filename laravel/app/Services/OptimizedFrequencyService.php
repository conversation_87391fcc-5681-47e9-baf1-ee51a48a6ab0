<?php

namespace App\Services;

use App\ClassFrequency;
use App\DoctorFrequency;
use App\Models\OtherSetting;
use App\Models\SpecialityClassFrequency;
use App\SpecialityFrequency;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class OptimizedFrequencyService
{
    const CACHE_TTL = 3600; // 1 hour cache

    /**
     * Get frequencies for multiple doctors in bulk to avoid N+1 queries
     */
    public function getBulkFrequencies(array $doctorData, $month, $year)
    {
        $frequencyType = $this->getFrequencyType();
        
        switch ($frequencyType) {
            case 1:
                return $this->getBulkClassFrequencies($doctorData, $month, $year);
            case 2:
                return $this->getBulkDoctorFrequencies($doctorData, $month, $year);
            case 3:
                return $this->getBulkSpecialityFrequencies($doctorData, $month, $year);
            default:
                return $this->getBulkSpecialityClassFrequencies($doctorData, $month, $year);
        }
    }

    /**
     * Get frequency type with caching
     */
    private function getFrequencyType()
    {
        return Cache::remember('frequency_type_setting', self::CACHE_TTL, function () {
            return OtherSetting::value('value') ?? 4;
        });
    }

    /**
     * Bulk fetch class frequencies
     */
    private function getBulkClassFrequencies(array $doctorData, $month, $year)
    {
        $classIds = collect($doctorData)->pluck('class_id')->unique()->filter()->toArray();
        $lineIds = collect($doctorData)->pluck('line_id')->unique()->filter()->toArray();

        if (empty($classIds) || empty($lineIds)) {
            return collect();
        }

        $frequencies = ClassFrequency::whereIn('class_id', $classIds)
            ->whereIn('line_id', $lineIds)
            ->where('month', $month)
            ->where('year', $year)
            ->whereNull('deleted_at')
            ->get()
            ->keyBy(function ($item) {
                return $item->class_id . '_' . $item->line_id;
            });

        return collect($doctorData)->mapWithKeys(function ($doctor) use ($frequencies) {
            $key = $doctor['class_id'] . '_' . $doctor['line_id'];
            $frequency = $frequencies->get($key);
            return [$doctor['doctor_id'] => $frequency ? $frequency->frequency : null];
        });
    }

    /**
     * Bulk fetch doctor frequencies - optimized for 3M records
     */
    private function getBulkDoctorFrequencies(array $doctorData, $month, $year)
    {
        $doctorIds = collect($doctorData)->pluck('doctor_id')->unique()->toArray();
        $accountIds = collect($doctorData)->pluck('account_id')->unique()->toArray();
        $lineIds = collect($doctorData)->pluck('line_id')->unique()->toArray();

        if (empty($doctorIds)) {
            return collect();
        }

        // Use optimized query with computed columns and proper indexing
        $frequencies = DoctorFrequency::select('doctor_id', 'account_id', 'line_id', 'frequency')
            ->whereIn('doctor_id', $doctorIds)
            ->whereIn('account_id', $accountIds)
            ->whereIn('line_id', $lineIds)
            ->where('month', $month)
            ->where('year', $year)
            ->whereNull('deleted_at')
            ->get()
            ->keyBy(function ($item) {
                return $item->doctor_id . '_' . $item->account_id . '_' . $item->line_id;
            });

        return collect($doctorData)->mapWithKeys(function ($doctor) use ($frequencies) {
            $key = $doctor['doctor_id'] . '_' . $doctor['account_id'] . '_' . $doctor['line_id'];
            $frequency = $frequencies->get($key);
            return [$doctor['doctor_id'] => $frequency ? $frequency->frequency : null];
        });
    }

    /**
     * Bulk fetch speciality frequencies
     */
    private function getBulkSpecialityFrequencies(array $doctorData, $month, $year)
    {
        $specialityIds = collect($doctorData)->pluck('speciality_id')->unique()->filter()->toArray();
        $lineIds = collect($doctorData)->pluck('line_id')->unique()->filter()->toArray();

        if (empty($specialityIds) || empty($lineIds)) {
            return collect();
        }

        $frequencies = SpecialityFrequency::whereIn('speciality_id', $specialityIds)
            ->whereIn('line_id', $lineIds)
            ->where('month', $month)
            ->where('year', $year)
            ->whereNull('deleted_at')
            ->get()
            ->keyBy(function ($item) {
                return $item->speciality_id . '_' . $item->line_id;
            });

        return collect($doctorData)->mapWithKeys(function ($doctor) use ($frequencies) {
            $key = $doctor['speciality_id'] . '_' . $doctor['line_id'];
            $frequency = $frequencies->get($key);
            return [$doctor['doctor_id'] => $frequency ? $frequency->frequency : null];
        });
    }

    /**
     * Bulk fetch speciality class frequencies
     */
    private function getBulkSpecialityClassFrequencies(array $doctorData, $month, $year)
    {
        $specialityIds = collect($doctorData)->pluck('speciality_id')->unique()->filter()->toArray();
        $classIds = collect($doctorData)->pluck('class_id')->unique()->filter()->toArray();
        $lineIds = collect($doctorData)->pluck('line_id')->unique()->filter()->toArray();

        if (empty($specialityIds) || empty($classIds) || empty($lineIds)) {
            return collect();
        }

        $frequencies = SpecialityClassFrequency::whereIn('speciality_id', $specialityIds)
            ->whereIn('class_id', $classIds)
            ->whereIn('line_id', $lineIds)
            ->where('month', $month)
            ->where('year', $year)
            ->whereNull('deleted_at')
            ->get()
            ->keyBy(function ($item) {
                return $item->speciality_id . '_' . $item->class_id . '_' . $item->line_id;
            });

        return collect($doctorData)->mapWithKeys(function ($doctor) use ($frequencies) {
            $key = $doctor['speciality_id'] . '_' . $doctor['class_id'] . '_' . $doctor['line_id'];
            $frequency = $frequencies->get($key);
            return [$doctor['doctor_id'] => $frequency ? $frequency->frequency : null];
        });
    }

    /**
     * Optimized method for getting frequencies with date range
     */
    public function getFrequenciesForDateRange($doctorData, Carbon $fromDate, Carbon $toDate)
    {
        $results = collect();
        
        // Group by month/year to minimize queries
        $dateGroups = collect();
        $current = $fromDate->copy()->startOfMonth();
        
        while ($current <= $toDate) {
            $dateGroups->push([
                'month' => $current->month,
                'year' => $current->year
            ]);
            $current->addMonth();
        }

        foreach ($dateGroups as $dateGroup) {
            $monthlyFrequencies = $this->getBulkFrequencies(
                $doctorData, 
                $dateGroup['month'], 
                $dateGroup['year']
            );
            
            $results->put($dateGroup['year'] . '-' . $dateGroup['month'], $monthlyFrequencies);
        }

        return $results;
    }

    /**
     * Clear frequency cache
     */
    public function clearCache()
    {
        Cache::forget('frequency_type_setting');
    }
}
