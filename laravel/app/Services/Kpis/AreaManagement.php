<?php


namespace App\Services\Kpis;

use App\Exceptions\CrmException;
use App\Models\FieldDayKpi;
use App\Services\ActualService;
use App\User;
use Illuminate\Support\Facades\Log;

class AreaManagement
{
    public function getAreaManagement($objects, $table, $from, $to, ?array $shifts = [], ?array $lines = [], ?array $specialities = [], ?array $accountTypes = [])
    {
        $total = 0;
        // throw new CrmException($objects);
        foreach ($objects as $object) {
            $user = User::find($object);
            $actualDatesCount = (new ActualService)->getActuals($user, $table, $from, $to, $shifts, $lines, $specialities, $accountTypes)
                ->where('visit_type_id', 2)->unique('date')->values()->pluck('date')->count();
            // Log::info($actualDatesCount);
            if ($actualDatesCount >= 2) {
                $total += $actualDatesCount;
            } else {
                continue;
            }
        }
        Log::info($total);
        return $total;
        // if ($total >= $fieldDays->minimum) return $total;
        // else return 0;
    }
    // public function getTeamFieldDays($directUsers, $table, $from, $to, ?array $shifts = [], ?array $lines = [], ?array $divisions = [], ?array $specialities = [], ?array $accountTypes = [])
    // {
    //     $actuals = (new ActualService)->teamActuals($from, $to, $directUsers, $table,  $shifts, $lines, $specialities, $accountTypes);
    //     $days = $actuals->unique('date')->values()->pluck('date')->count();
    //     return $days;
    // }
}
