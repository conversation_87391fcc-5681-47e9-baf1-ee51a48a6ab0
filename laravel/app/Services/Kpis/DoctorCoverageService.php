<?php


namespace App\Services\Kpis;

use App\Services\ActualService;
use App\Services\DoctorService;
use Illuminate\Support\Facades\Log;

class DoctorCoverageService
{
    public function getCoveredDoctors($object, $table, $from, $to, ?array $shifts = [], ?array $lines = [], ?array $divisions = [], ?array $specialities = [], ?array $accountTypes = [])
    {
        $doctors = (new DoctorService)->getDoctorsِCoverage(
            lines: $lines,
            divisions: $divisions,
            from: $from,
            to: $to
        );
        $doctorIds = $doctors->pluck('id')->toArray();
        $actuals = (new ActualService)->getActuals($object, $table, $from, $to, $shifts, $lines, $specialities, $accountTypes, null, $doctorIds);
        $visitedDoctors = $actuals->unique('account_dr_id')->count();
        $covered = count($doctors) ? round(($visitedDoctors / count($doctors)) * 100, 2) : 0;
        return $covered;
    }
}
