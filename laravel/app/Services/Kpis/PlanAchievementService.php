<?php


namespace App\Services\Kpis;

use App\Services\ActualService;
use App\Services\PlanService;


class PlanAchievementService
{

    public function getPlanAchievement($object, $table, $from, $to, ?array $shifts = [], ?array $lines = [], ?array $divisions = [], ?array $specialities = [], ?array $accountTypes = [])
    {
        $plans = (new PlanService)->getPlans($object, $table, $from, $to, $shifts, $lines);
        $actuals = (new ActualService)->getActuals($object, $table, $from, $to, $shifts, $lines, $specialities, $accountTypes);
        $planConverted = $actuals->whereNotNull('plan_id')->count();
        $achievement = count($plans) ? round(($planConverted / count($plans)) * 100, 2) : 0;
        return $achievement;
    }
}
