<?php


namespace App\Services\Kpis;

use App\Exceptions\CrmException;
use App\Services\ActualService;
use App\Services\DoctorService;
use Illuminate\Support\Facades\Log;

class TeamDoctorCoverageService
{
    public function getTeamCoveredDoctors($from, $to, $objects, $table, ?array $shifts = [], ?array $lines = [], ?array $divisions = [], ?array $specialities = [], ?array $accountTypes = [])
    {
        // throw new CrmException($divisions);
        $doctors = (new DoctorService)->getDoctorsِCoverage($lines, $divisions, $from, $to);
        $doctorIds = $doctors->pluck('id')->toArray();
        $actuals = (new ActualService)->teamActuals($from, $to, $objects, $table, $shifts, $lines, $specialities, $accountTypes, $doctorIds);
        $visitedDoctors = $actuals->unique('doctor_id')->count();
        $covered = count($doctors) ? round(($visitedDoctors / count($doctors)) * 100, 2) : 0;
        return $covered;
    }
}
