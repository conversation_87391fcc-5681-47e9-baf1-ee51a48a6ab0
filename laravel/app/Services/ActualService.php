<?php


namespace App\Services;

use App\Account;
use App\ActualVisit;
use App\Doctor;
use App\Exceptions\CrmException;
use App\PlanVisit;
use App\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ActualService
{

    public function getactuals($object, $column, $from, $to, ?array $shifts = [], ?array $lines = [], ?array $specialities = [], ?array $accountTypes = [], $visitTypeId = null, ?array $doctorIds = [], ?array $accountIds = [], ?array $products = [])
    {
        $actuals = ActualVisit::select([
            'actual_visits.id as id',
            'actual_visits.line_id',
            'actual_visits.plan_id',
            'actual_visits.div_id',
            'actual_visits.user_id',
            'actual_visits.ll',
            'actual_visits.lg',
            'actual_visits.acc_type_id',
            'actual_visits.account_id',
            'actual_visits.account_dr_id',
            'actual_visits.created_at as insertion',
            // 'actual_visits.visit_date as date',
            DB::raw('DATE_FORMAT(crm_actual_visits.visit_date,"%Y-%m-%d") as date'),
            'actual_visits.visit_date',
            'bricks.name as brick',
            'users.fullname as employee',
            'lines.name as line',
            'line_divisions.name as division',
            'accounts.name as account',
            'accounts.id as account_id',
            'doctors.id as doctor_id',
            'doctors.name as doctor',
            'account_types.name as acc_type',
            'account_types.shift_id as acc_shift_id',
            'shifts.name as shift',
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            DB::raw('IFNULL(crm_specialities.name,"") as speciality'),
            'visit_types.name as type',
            'actual_visits.visit_type_id as visit_type_id',
            'specialities.id as speciality_id',
            DB::raw('IFNULL(group_concat(distinct crm_products.name),"") as product'),
        ])
            ->leftJoin('lines', 'actual_visits.line_id', 'lines.id')
            ->leftJoin('users', 'actual_visits.user_id', 'users.id')
            ->leftJoin('line_divisions', 'actual_visits.div_id', 'line_divisions.id')
            ->leftJoin('accounts', 'actual_visits.account_id', 'accounts.id')
            ->leftJoin('bricks', 'actual_visits.brick_id', 'bricks.id')
            ->leftJoin('account_types', 'actual_visits.acc_type_id', 'account_types.id')
            ->leftJoin('shifts', 'account_types.shift_id', 'shifts.id')
            ->leftJoin('doctors', 'actual_visits.account_dr_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('visit_types', 'actual_visits.visit_type_id', 'visit_types.id')
            ->leftJoin('actual_visit_products', 'actual_visits.id', 'actual_visit_products.visit_id')
            ->leftJoin('products', 'actual_visit_products.product_id', 'products.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('actual_visits.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', 'App\ActualVisit');
                }
            )
            ->where($column, $object->id)
            ->orderBy('actual_visits.visit_date', 'asc')
            ->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null))
            ->whereBetween('visit_date', [Carbon::parse($from)->startOfDay(), Carbon::parse($to)->endOfDay()]);
        if (!empty($lines)) {
            $actuals = $actuals->whereIntegerInRaw('actual_visits.line_id', $lines);
        }
        if (!empty($accountTypes)) {
            $actuals = $actuals->whereIntegerInRaw('actual_visits.acc_type_id', $accountTypes);
        }
        if (!empty($specialities)) {
            $actuals = $actuals->whereIntegerInRaw('specialities.id', $specialities);
        }
        if (!empty($shifts)) {
            $actuals = $actuals->whereIntegerInRaw('account_types.shift_id', $shifts);
        }
        if (!empty($doctorIds)) {
            $actuals = $actuals->whereIntegerInRaw('actual_visits.account_dr_id', $doctorIds);
        }
        if (!empty($accountIds)) {
            $actuals = $actuals->whereIntegerInRaw('actual_visits.account_id', $accountIds);
        }
        if (!empty($products)) {
            $actuals = $actuals->whereIntegerInRaw('actual_visit_products.product_id', $products);
        }
        if (!isNullable($visitTypeId)) {
            $actuals = $actuals->where('actual_visits.visit_type_id', $visitTypeId);
        }
        $actuals = $actuals
            ->groupBy(
                "actual_visits.id"
            )
            ->get();
        return $actuals;
    }

    public function teamActuals($from, $to, $users, $table, ?array $shifts = [], ?array $lines = [], ?array $specialities = [], ?array $accountTypes = [], ?array $doctorIds = [])
    {
        $actuals = ActualVisit::select(
            [
                'actual_visits.id as id',
                'actual_visits.line_id',
                'actual_visits.plan_id',
                'actual_visits.div_id',
                'actual_visits.user_id',
                'actual_visits.ll',
                'actual_visits.lg',
                'actual_visits.acc_type_id',
                'actual_visits.account_id',
                'actual_visits.account_dr_id',
                'actual_visits.created_at as insertion',
                // 'actual_visits.visit_date as date',
                DB::raw('DATE_FORMAT(crm_actual_visits.visit_date,"%Y-%m-%d") as date'),
                'bricks.name as brick',
                'users.fullname as employee',
                'lines.name as line',
                'line_divisions.name as division',
                'accounts.name as account',
                'accounts.id as account_id',
                'doctors.id as doctor_id',
                'doctors.name as doctor',
                'account_types.name as acc_type',
                'account_types.shift_id as acc_shift_id',
                DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
                DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
                DB::raw('IFNULL(crm_specialities.name,"") as speciality'),
                'visit_types.name as type',
                'specialities.id as speciality_id',
            ]
        )
            ->leftJoin('lines', 'actual_visits.line_id', 'lines.id')
            ->leftJoin('users', 'actual_visits.user_id', 'users.id')
            ->leftJoin('line_divisions', 'actual_visits.div_id', 'line_divisions.id')
            ->leftJoin('accounts', 'actual_visits.account_id', 'accounts.id')
            ->leftJoin('bricks', 'actual_visits.brick_id', 'bricks.id')
            ->leftJoin('account_types', 'actual_visits.acc_type_id', 'account_types.id')
            ->leftJoin('doctors', 'actual_visits.account_dr_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('visit_types', 'actual_visits.visit_type_id', 'visit_types.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('actual_visits.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', 'App\ActualVisit');
                }
            )
            ->whereIntegerInRaw($table, $users)
            ->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null))
            ->whereBetween('visit_date', [$from, $to]);
        if (!empty($lines)) {
            $actuals = $actuals->whereIntegerInRaw('actual_visits.line_id', $lines);
        }
        if (!empty($types)) {
            $actuals = $actuals->whereIntegerInRaw('actual_visits.acc_type_id', $types);
        }
        if (!empty($specialities)) {
            $actuals = $actuals->whereIntegerInRaw('specialities.id', $specialities);
        }
        if (!empty($specialities)) {
            $actuals = $actuals->whereIntegerInRaw('account_types.id', $accountTypes);
        }
        if (!empty($shifts)) {
            $actuals = $actuals->whereIntegerInRaw('account_types.shift_id', $shifts);
        }
        if (!empty($doctorIds)) {
            $actuals = $actuals->whereIntegerInRaw('actual_visits.account_dr_id', $doctorIds);
        }
        $actuals = $actuals
            ->groupBy(
                "actual_visits.id"
            )
            ->get();
        return $actuals;
    }
}
