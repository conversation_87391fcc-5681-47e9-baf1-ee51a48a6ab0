<?php

namespace App\Services;

use App\ClassFrequency;
use App\Doctor;
use App\DoctorFrequency;
use App\Exceptions\CrmException;
use App\Models\OtherSetting;
use App\Models\SpecialityClassFrequency;
use App\SpecialityFrequency;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;

class FrequencyTypeValidationService
{
    // Cache TTL in seconds (1 hour)
    const CACHE_TTL = 3600;

    // Static caches for request-level caching
    private static $doctorCache = [];
    private static $settingCache = null;
    private static $frequencyCache = [];

    /**
     * Get frequency for a specific doctor with optimized caching and queries
     */
    public function classType($account_id, $doctor_id, $line_id, $date)
    {
        $fromMonth = (int) Carbon::parse($date)->format('m');
        $fromYear = (int) Carbon::parse($date)->format('Y');

        // Create cache key for this specific request
        $cacheKey = "freq_{$account_id}_{$doctor_id}_{$line_id}_{$fromMonth}_{$fromYear}";

        // Check if we already have this frequency cached
        if (isset(self::$frequencyCache[$cacheKey])) {
            return self::$frequencyCache[$cacheKey];
        }

        // Get doctor data with caching
        $doctor = $this->getCachedDoctor($doctor_id);
        if (!$doctor) {
            return self::$frequencyCache[$cacheKey] = null;
        }

        // Get frequency type setting with caching
        $frequencyType = $this->getCachedFrequencyType();

        // Get frequency based on type using optimized queries
        $frequency = match ($frequencyType) {
            1 => $this->getClassFrequency($doctor->class_id, $line_id, $fromMonth, $fromYear),
            2 => $this->getDoctorFrequency($doctor->id, $account_id, $line_id, $fromMonth, $fromYear),
            3 => $this->getSpecialityFrequency($doctor->speciality_id, $line_id, $fromMonth, $fromYear),
            default => $this->getSpecialityClassFrequency($doctor->speciality_id, $doctor->class_id, $line_id, $fromMonth, $fromYear)
        };

        // throw new CrmException([$frequencyType , $frequency]);

        // Cache the result for this request
        return self::$frequencyCache[$cacheKey] = $frequency;
    }

    /**
     * Get cached doctor data
     */
    private function getCachedDoctor($doctor_id)
    {
        if (!isset(self::$doctorCache[$doctor_id])) {
            self::$doctorCache[$doctor_id] = Doctor::select('id', 'class_id', 'speciality_id')
                ->where('id', $doctor_id)
                ->first();
        }

        return self::$doctorCache[$doctor_id];
    }

    /**
     * Get cached frequency type setting
     */
    private function getCachedFrequencyType()
    {
        if (self::$settingCache === null) {
            self::$settingCache = Cache::remember('frequency_type_setting', self::CACHE_TTL, function () {
                return (int) OtherSetting::value('value') ?? 4;
            });
        }

        return self::$settingCache;
    }

    /**
     * Optimized method to get class frequency using computed columns
     */
    private function getClassFrequency($class_id, $line_id, $month, $year)
    {
        return ClassFrequency::where('class_id', $class_id)
            ->where('line_id', $line_id)
            ->where('month', $month)
            ->where('year', $year)
            ->value('frequency');
    }

    /**
     * Optimized method to get doctor frequency using computed columns
     */
    private function getDoctorFrequency($doctor_id, $account_id, $line_id, $month, $year)
    {
        return DoctorFrequency::where('doctor_id', $doctor_id)
            ->where('account_id', $account_id)
            ->where('line_id', $line_id)
            ->where('month', $month)
            ->where('year', $year)
            ->value('frequency');
    }

    /**
     * Optimized method to get speciality frequency using computed columns
     */
    private function getSpecialityFrequency($speciality_id, $line_id, $month, $year)
    {
        return SpecialityFrequency::where('speciality_id', $speciality_id)
            ->where('line_id', $line_id)
            ->where('month', $month)
            ->where('year', $year)
            ->value('frequency');
    }

    /**
     * Optimized method to get speciality class frequency using computed columns
     */
    private function getSpecialityClassFrequency($speciality_id, $class_id, $line_id, $month, $year)
    {
        return SpecialityClassFrequency::where('speciality_id', $speciality_id)
            ->where('class_id', $class_id)
            ->where('line_id', $line_id)
            ->where('month', $month)
            ->where('year', $year)
            ->value('frequency');
    }

    /**
     * Bulk method to get frequencies for multiple doctors - eliminates N+1 queries
     */
    public function getBulkFrequencies(array $doctorData, $month, $year)
    {
        $frequencyType = $this->getCachedFrequencyType();

        switch ($frequencyType) {
            case 1:
                return $this->getBulkClassFrequencies($doctorData, $month, $year);
            case 2:
                return $this->getBulkDoctorFrequencies($doctorData, $month, $year);
            case 3:
                return $this->getBulkSpecialityFrequencies($doctorData, $month, $year);
            default:
                return $this->getBulkSpecialityClassFrequencies($doctorData, $month, $year);
        }
    }

    /**
     * Bulk fetch class frequencies
     */
    private function getBulkClassFrequencies(array $doctorData, $month, $year)
    {
        $classIds = collect($doctorData)->pluck('class_id')->unique()->filter()->toArray();
        $lineIds = collect($doctorData)->pluck('line_id')->unique()->filter()->toArray();

        if (empty($classIds) || empty($lineIds)) {
            return collect();
        }

        $frequencies = ClassFrequency::whereIn('class_id', $classIds)
            ->whereIn('line_id', $lineIds)
            ->where('month', $month)
            ->where('year', $year)
            ->get()
            ->keyBy(function ($item) {
                return $item->class_id . '_' . $item->line_id;
            });

        return collect($doctorData)->mapWithKeys(function ($doctor) use ($frequencies) {
            $key = $doctor['class_id'] . '_' . $doctor['line_id'];
            $frequency = $frequencies->get($key);
            return [$doctor['doctor_id'] => $frequency ? $frequency->frequency : null];
        });
    }

    /**
     * Bulk fetch doctor frequencies - optimized for 3M records
     */
    private function getBulkDoctorFrequencies(array $doctorData, $month, $year)
    {
        $doctorIds = collect($doctorData)->pluck('doctor_id')->unique()->toArray();
        $accountIds = collect($doctorData)->pluck('account_id')->unique()->toArray();
        $lineIds = collect($doctorData)->pluck('line_id')->unique()->toArray();

        if (empty($doctorIds)) {
            return collect();
        }

        // Use optimized query with computed columns and proper indexing
        $frequencies = DoctorFrequency::select('doctor_id', 'account_id', 'line_id', 'frequency')
            ->whereIn('doctor_id', $doctorIds)
            ->whereIn('account_id', $accountIds)
            ->whereIn('line_id', $lineIds)
            ->where('month', $month)
            ->where('year', $year)
            ->get()
            ->keyBy(function ($item) {
                return $item->doctor_id . '_' . $item->account_id . '_' . $item->line_id;
            });

        return collect($doctorData)->mapWithKeys(function ($doctor) use ($frequencies) {
            $key = $doctor['doctor_id'] . '_' . $doctor['account_id'] . '_' . $doctor['line_id'];
            $frequency = $frequencies->get($key);
            return [$doctor['doctor_id'] => $frequency ? $frequency->frequency : null];
        });
    }

    /**
     * Bulk fetch speciality frequencies
     */
    private function getBulkSpecialityFrequencies(array $doctorData, $month, $year)
    {
        $specialityIds = collect($doctorData)->pluck('speciality_id')->unique()->filter()->toArray();
        $lineIds = collect($doctorData)->pluck('line_id')->unique()->filter()->toArray();

        if (empty($specialityIds) || empty($lineIds)) {
            return collect();
        }

        $frequencies = SpecialityFrequency::whereIn('speciality_id', $specialityIds)
            ->whereIn('line_id', $lineIds)
            ->where('month', $month)
            ->where('year', $year)
            ->get()
            ->keyBy(function ($item) {
                return $item->speciality_id . '_' . $item->line_id;
            });

        return collect($doctorData)->mapWithKeys(function ($doctor) use ($frequencies) {
            $key = $doctor['speciality_id'] . '_' . $doctor['line_id'];
            $frequency = $frequencies->get($key);
            return [$doctor['doctor_id'] => $frequency ? $frequency->frequency : null];
        });
    }

    /**
     * Bulk fetch speciality class frequencies
     */
    private function getBulkSpecialityClassFrequencies(array $doctorData, $month, $year)
    {
        $specialityIds = collect($doctorData)->pluck('speciality_id')->unique()->filter()->toArray();
        $classIds = collect($doctorData)->pluck('class_id')->unique()->filter()->toArray();
        $lineIds = collect($doctorData)->pluck('line_id')->unique()->filter()->toArray();

        if (empty($specialityIds) || empty($classIds) || empty($lineIds)) {
            return collect();
        }

        $frequencies = SpecialityClassFrequency::whereIn('speciality_id', $specialityIds)
            ->whereIn('class_id', $classIds)
            ->whereIn('line_id', $lineIds)
            ->where('month', $month)
            ->where('year', $year)
            ->get()
            ->keyBy(function ($item) {
                return $item->speciality_id . '_' . $item->class_id . '_' . $item->line_id;
            });

        return collect($doctorData)->mapWithKeys(function ($doctor) use ($frequencies) {
            $key = $doctor['speciality_id'] . '_' . $doctor['class_id'] . '_' . $doctor['line_id'];
            $frequency = $frequencies->get($key);
            return [$doctor['doctor_id'] => $frequency ? $frequency->frequency : null];
        });
    }

    /**
     * Clear all caches
     */
    public function clearCache()
    {
        self::$doctorCache = [];
        self::$settingCache = null;
        self::$frequencyCache = [];
        Cache::forget('frequency_type_setting');
    }



    // public function classType($account_id, $doctor_id, $line_id, $date)
    // {
    //     $fromMonth = Carbon::parse($date)->format('m');
    //     $fromYear = Carbon::parse($date)->format('Y');
    //     $doctor = Doctor::where('id', $doctor_id)->first();
    //     $other = OtherSetting::value('value');
    //     if ($other == 1) {
    //         return ClassFrequency::where('class_id', $doctor?->class_id)->where('line_id', $line_id)
    //             ->whereMonth('date', $fromMonth)
    //             ->whereYear('date', $fromYear)->value('frequency');
    //     } elseif ($other == 2) {
    //         return DoctorFrequency::where('doctor_id', $doctor?->id)->where('account_id', $account_id)
    //             ->where('line_id', $line_id)
    //             ->where('month', $fromMonth)
    //             ->where('year', $fromYear)
    //             ->whereNull('deleted_at')
    //             ->value('frequency');
    //     } elseif ($other == 3) {
    //         return SpecialityFrequency::where('speciality_id', $doctor->speciality_id)
    //             ->where('line_id', $line_id)->whereMonth('date', $fromMonth)
    //             ->whereYear('date', $fromYear)->value('frequency');
    //     } else {
    //         return SpecialityClassFrequency::where('speciality_id', $doctor->speciality_id)
    //             ->where('class_id', $doctor?->class_id)
    //             ->where('line_id', $line_id)->whereMonth('date', $fromMonth)
    //             ->whereYear('date', $fromYear)->value('frequency');
    //     }
    // }
}
