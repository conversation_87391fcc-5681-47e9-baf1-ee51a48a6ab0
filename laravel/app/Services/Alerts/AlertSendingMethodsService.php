<?php

namespace App\Services\Alerts;


use App\Models\OffDay;
use App\Models\Widgets\AlertWithoutAMVisits;
use App\Models\Widgets\AlertWithoutPMVisits;
use App\PublicHoliday;
use App\User;
use App\Models\Alert;
use App\Widget;
use App\Models\Attachment;
use App\Models\Message;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Helpers\Notifications\NotificationHelper;
use App\Notifications\MessageNotification;
use Illuminate\Support\Carbon;
use App\Exports\Alerts\WithoutAMVisitsAlertsExport;
use App\Exports\Alerts\WithoutPMVisitsAlertsExport;
use App\Line;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use App\Setting;
use Illuminate\Support\Facades\Mail;
use App\Mail\Alerts;
use App\Role;
use Maatwebsite\Excel\Excel as BaseExcel;


class AlertSendingMethodsService
{


    public function __construct(private AlertReportsService $alertReportsService)
    {
    }

    private function getAlerts()
    {
        return Alert::with('users')
            ->where("time", Carbon::now()->format("H:i"))
            ->get()
            ->map(function (Alert $alert) {
                return [
                    'id' => $alert->id,
                    'type' => $alert->type,
                    'employee' => $alert->users->pluck('fullname')->implode(' , '),
                    'user_id' => $alert->users->pluck('id'),
                    'period' => $alert->period,
                    'time' => $alert->time,
                    'role' => $alert->role_filter,
                    'format' => $alert->format,
                    'isOnce' => $alert->isOnce,
                    'by' => $alert->by->pluck('sending_method')->implode(' , '),
                ];
            });
    }

    private function using(string $byMethod, callable $callback, bool $checkOnce): void
    {
        $alerts = $this->getAlerts();
        foreach ($alerts as $alert) {
            if ($alert['period'] !== 'Once' && $checkOnce) continue;

            $methods = explode(' , ', $alert['by']);
            foreach ($methods as $method) {
                if ($method !== $byMethod) continue;
                $callback($alert, $method);
            }
        }
    }

    private function usingMail(callable $callback, $checkOnce = false): void
    {
        $this->using('Email', $callback, $checkOnce);
    }

    private function usingWidget(callable $callback, $checkOnce = false): void
    {
        $this->using('Widget', $callback, $checkOnce);
    }

    private function usingInternalMessage(callable $callback, $checkOnce = false): void
    {
        $this->using('Internal Messaging', $callback, $checkOnce);
    }

    private function checkOffDay(string $period): array
    {

        $periodicTimeFrom = match ($period) {
            'Daily' => Carbon::yesterday(),
            'Weekly' => Carbon::now()->subDays(7),
            'Monthly' => Carbon::now()->subDays(),
            'Quarter' => Carbon::now()->subMonths(3),
            default => Carbon::now()->subMonths(12),
        };


        $formattedDate = (clone $periodicTimeFrom)->format('Y-m-d');

        $isToDayAmVacation = OffDay::isOffDay(
            (clone $periodicTimeFrom)->firstOfMonth(),
            (clone $periodicTimeFrom)->lastOfMonth(),
            (clone $periodicTimeFrom),
            1
        );

        $isToDayPMVacation = OffDay::isOffDay(
            (clone $periodicTimeFrom)->firstOfMonth(),
            (clone $periodicTimeFrom)->lastOfMonth(),
            (clone $periodicTimeFrom),
            2
        );

        if (!$isToDayPMVacation || !$isToDayAmVacation) {
            $isPublicHoliday = PublicHoliday::isPublicHoliday((clone $periodicTimeFrom)->firstOfMonth(),
                (clone $periodicTimeFrom)->lastOfMonth(),
                (clone $periodicTimeFrom)
            );

            if ($isPublicHoliday) {
                $isToDayAmVacation = $isPublicHoliday;
                $isToDayPMVacation = $isPublicHoliday;
            }
        }

        return [
            $formattedDate,
            $isToDayAmVacation,
            $isToDayPMVacation

        ];
    }

    public function sendMailOnce(): void
    {

        $this->usingMail(function ($alert) {
            $usersArray = explode(" , ", $alert['employee']);
            $emails = [];

            foreach ($usersArray as $user) {
                $getEmail = User::select('email')->where('fullname', $user)->get();
                $emails[] = $getEmail[0]->email;
            }

            $by = $alert['by'];
            $period = $alert['period'];
            $type = $alert['type'];
            $time = $alert['time'];
            $role = $alert['role'];

            $reportType = $this->reportTypeMail($type, $period, $time, $by, $alert, $role);
            $this->mailContent($reportType, $alert, $emails);
        }, true);
    }

    public function sendmail(): void
    {

        $this->usingMail(function ($alert) {
            $usersArray = explode(" , ", $alert['employee']);
            $emails = [];

            foreach ($usersArray as $user) {
                $getEmail = User::select('email')->where('fullname', $user)->get();
                $emails[] = $getEmail[0]->email;
            }

            $by = $alert['by'];
            $period = $alert['period'];
            $type = $alert['type'];
            $time = $alert['time'];
            $role = $alert['role'];


            $reportType = match ($period) {
                'Daily' => $this->reportTypeMail($type, $period, $time, $by, $alert, $role),
                'Weekly' => carbon::now()->format('l') == 'Thursday' ?
                    $this->reportTypeMail($type, $period, $time, $by, $alert, $role)
                    : [],
                'Monthly' => carbon::now()->isLastOfMonth() ?
                    $this->reportTypeMail($type, $period, $time, $by, $alert, $role)
                    : [],
                'Quarter' => (new Carbon("-3 months"))
                    ->lastOfQuarter()
                    ->equalTo(Carbon::now()) ?
                    $this->reportTypeMail($type, $period, $time, $by, $alert, $role)
                    : []
            };
            if ($reportType['shouldSent'])
                $this->mailContent($reportType, $alert, $emails);
        });
    }
    private function getLines($user)
    {
        return $user->hasRole('admin') ?
            Line::where('from_date', '<=', now())
            ->where(fn($q) => $q->where('to_date', '>=', (string)Carbon::now())->orWhere('to_date', null))->get()
            : $user->lines()->where('from_date', '<=', now())
            ->where(fn($q) => $q->where('to_date', '>=', (string)Carbon::now())->orWhere('to_date', null))->get();
    }
    private function reportTypeMail($type, $period, $time, $by, $alert, $role): array
    {
        // to make the report with data and desired file type: PDF / Excel / CSV
        $fileName = null;
        $attachment = ['fileName' => $fileName, 'attachment' => null];
        $shouldSend = false;
        [
            $formattedDate,
            $isToDayAmVacation,
            $isToDayPMVacation
        ] = $this->checkOffDay($period);

        foreach ($alert['user_id'] as $user) {
            $currentUser = User::find($user);
            $lines = $this->getLines($currentUser);
            $roleName = Role::where('id', $role)->value('name') ?? '';
            if ($type === 'Without PM Visits' && !$isToDayPMVacation) { // for the report: Users without PM Visits
                $shouldSend = !$isToDayPMVacation;
                $reportData = $this->alertReportsService->withoutVisits($period, 'PM', $time, $by, '', '', '', $currentUser, $roleName, $lines);
                $report_name = 'Without PM Visits';

                $attachment = $this->createAttachment($reportData, $alert['format'], $report_name, $formattedDate);
            }

            if ($type === 'Without AM Visits' && !$isToDayAmVacation) {  // for the report: Users without AM Visits
                $period = $alert['period'];
                $time = $alert['time'];
                $shouldSend = !$isToDayAmVacation;
                $reportData = $this->alertReportsService->withoutVisits($period, 'AM', $time, $by, '', '', '', $currentUser, $roleName, $lines);
                $report_name = 'Without AM Visits';

                $attachment = $this->createAttachment($reportData, $alert['format'], $report_name, $formattedDate);
            }
        }

        return [...$attachment, 'shouldSent' => $shouldSend];
    }

    private function createAttachment($reportData, string $fileType, string $report_name, string $date): array
    {
        [$fileName, $extension] = $this->format($fileType, $report_name, $date);

        $file = json_decode(json_encode($reportData), true);

        $attachment = Excel::raw(
            new WithoutPMVisitsAlertsExport($file),
            $extension
        );
        return ['fileName' => $fileName, 'attachment' => $attachment];
    }

    private function format(string $fileType, string $report_name, string $date): array
    {
        return match ($fileType) {
            'Excel' => [
                $report_name . $date . '.xlsx',
                BaseExcel::XLSX
            ],
            'CSV' => [
                $report_name . $date . '.csv',
                BaseExcel::CSV
            ],
            'PDF' => [
                $report_name . $date . '.pdf',
                BaseExcel::MPDF
            ],
        };
    }

    public function mailContent($reportType, $alert, $emails): void
    {

        // to get the required data for email
        $fileName = $reportType['fileName'];
        $attachment = $reportType['attachment'];
        $name = $fileName;
        $subject = 'Gemstone Alert Center: Alert of Users ' . $alert['type'] . ' ' . date("d-m-Y");
        $from = '<EMAIL>';

        // to loop over each email to send each mail
        foreach ($emails as $email) {

            $data["email"] = $email;
            $data["subject"] = $subject;
            $data["from"] = $from;
            $data["text"] = ['title' => 'A periodic ' . $name . ' ' . 'Report'];
            Mail::to($data["email"])->send(new Alerts($attachment, $name, $subject), $data);
        }
    }

    public function sendWidgetOnce(): void
    {
        $this->usingWidget(function ($alert) {
            $to_users = $alert['user_id'];
            $this->widgetContent($alert, $to_users);
        }, true);
    }

    public function sendWidget(): void
    {

        $this->usingWidget(function ($alert) {
            $to_users = $alert['user_id'];
            $period = $alert['period'];

            match ($period) {
                'Daily' => $this->widgetContent($alert, $to_users),
                'Weekly' => carbon::now()->format('l') == 'Thursday' ?
                    $this->widgetContent($alert, $to_users)
                    : [],
                'Monthly' => carbon::now()->isLastOfMonth() ?
                    $this->widgetContent($alert, $to_users)
                    : [],
                'Quarter' => (new Carbon("-3 months"))
                    ->lastOfQuarter()
                    ->equalTo(Carbon::now()) ?
                    $this->widgetContent($alert, $to_users)
                    : []
            };
        });
    }

    public function widgetContent($alert, $to_users): int
    {

        $Alert_widget_type = match ($alert['type']) {
            'Without AM Visits' => AlertWithoutAMVisits::class,
            'Without PM Visits' => AlertWithoutPMVisits::class,
            default => ''
        };


        $Alert_widget_id = Widget::select('id')
            ->where('widgetable_type', $Alert_widget_type)
            ->get()->pluck('id')->toArray();

        foreach ($to_users as $element) { // to send widget

            $user_widget_exist = DB::table('user_widget')
                ->where('user_id', '=', $element)
                ->where('widget_id', '=', $Alert_widget_id[0])
                ->count();

            if ($user_widget_exist < 1) { // to prevent to fill the user_widget table
                DB::table('user_widget')->insert([ // if the user has the widget already
                    'user_id' => $element,
                    'widget_id' => $Alert_widget_id[0],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        return 0;
    }

    public function sendInternalMessagingOnce(): void
    {
        $this->usingInternalMessage(function ($alert, $method) {
            $this->internalMessagingContent($alert, $method);
        }, true);
    }

    public function sendInternalMessaging(): void
    {
        $this->usingInternalMessage(function ($alert, $method) {
            $period = $alert['period'];
            match ($period) {
                'Daily' => $this->internalMessagingContent($alert, $method),
                'Weekly' => carbon::now()->format('l') == 'Thursday' ?
                    $this->internalMessagingContent($alert, $method)
                    : [],
                'Monthly' => carbon::now()->isLastOfMonth() ?
                    $this->internalMessagingContent($alert, $method)
                    : [],
                'Quarter' => (new Carbon("-3 months"))
                    ->lastOfQuarter()
                    ->equalTo(Carbon::now()) ?
                    $this->internalMessagingContent($alert, $method)
                    : []
            };
        });
    }

    public function internalMessagingContent($alert, $method): int
    {

        [
            $formattedDate,
            $isToDayAmVacation,
            $isToDayPMVacation
        ] = $this->checkOffDay($alert['period']);

        $attachment = null;
        $shouldSend = false;

        $users = $alert['user_id'];
        $period = $alert['period'];
        $role = $alert['role'];
        $time = $alert['time'];
        $alert_id = $alert['id'];
        $type = $alert['type'];

        foreach ($users as $user) {
            $currentUser = User::find($user);
            $lines = $this->getLines($currentUser);
            $roleName = Role::where('id', $role)->value('name') ?? '';
            if ($type === 'Without AM Visits' && !$isToDayAmVacation) { // for the report: Users without AM Visits
                $shouldSend = !$isToDayAmVacation;
                $reportData = $this->alertReportsService->withoutVisits($period, 'AM', $time, $method, $alert_id, '', '', $user, $role, $lines);
                $report_name = 'Without AM Visits';

                if ($shouldSend)
                    $attachment = $this->saveAttachment(
                        WithoutAMVisitsAlertsExport::class,
                        $reportData,
                        $alert['format'],
                        $report_name,
                        $formattedDate
                    );
            }

            if ($type === 'Without PM Visits' && !$isToDayPMVacation) {
                $shouldSend = !$isToDayPMVacation;
                $reportData = $this->alertReportsService->withoutVisits($period, 'PM', $time, $method, $alert_id, '', '', $user, $role, $lines);
                $report_name = 'Without PM Visits';
                if ($shouldSend)
                    $attachment = $this->saveAttachment(
                        WithoutPMVisitsAlertsExport::class,
                        $reportData,
                        $alert['format'],
                        $report_name,
                        $formattedDate
                    );
            }
        }
        $alert['attachments'] = $attachment;

        if ($shouldSend)
            $this->saveInternalMessage($alert);


        return 0;
    }

    private function saveInternalMessage($alert): void
    {
        DB::transaction(function () use ($alert) { // to send internal message to all users at once
            /** @var Message $message */
            $message = Message::create([
                'from' => 1,
                'subject' => 'Alert of Users ' . $alert['type'],
                'body' => $alert['attachments'],
            ]);

            $attachment = new Attachment();
            $attachment->path = $alert['attachments'];
            $message->attachments()->save($attachment);

            $users_ids_array = explode(' , ', $alert['employee']);
            $users_ids = User::whereIn("fullname", $users_ids_array)->get()->pluck('id');
            $users = User::whereIn("fullname", $users_ids_array)->get();

            $message->toUsers()->sync($users_ids);

            $notification = new MessageNotification($message->load(["sender"]));
            NotificationHelper::send($users, $notification);
        });
    }

    private function saveAttachment(string $alertExportClass, $reportData, string $fileType, string $report_name, string $date): string
    {
        $file = json_decode(json_encode($reportData), true);

        [$fileName, $extension] = $this->format($fileType, $report_name, $date);
        $path = 'public/alerts/Alert ' . $fileName;

        Excel::store(new $alertExportClass($file), $path, 'local', $extension);

        return Storage::url($path);
    }
}
