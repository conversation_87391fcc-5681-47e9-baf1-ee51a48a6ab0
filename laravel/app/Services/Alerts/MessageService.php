<?php

namespace App\Services\Alerts;

use App\Jobs\SendMessageNotificationJob;
use App\Models\Attachment;
use App\Models\Message;
use App\Services\Alerts\Concerns\MessageServiceInterface;
use App\Services\QueueLoadBalancingService;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class MessageService implements MessageServiceInterface
{

    /**
     * @param QueueLoadBalancingService $loadBalancingService
     */
    public function __construct(
        private readonly QueueLoadBalancingService $loadBalancingService
    ) {
    }

    /**
     * Send a message with an attachment to a user
     *
     * @param mixed $user
     * @param string $subject
     * @param string $body
     * @param string $attachmentUrl
     * @return void
     */
    public function sendWithAttachment($user, string $subject, string $body, string $attachmentUrl): void
    {
        DB::transaction(function () use ($user, $subject, $body, $attachmentUrl) {
            $message = Message::create([
                'from' => Config::get('alert.system_user_id', 1),  // Use config instead of hardcoded value
                'subject' => $subject,
                'body' => $body,
            ]);

            $attachment = new Attachment();
            $attachment->path = $attachmentUrl;
            $message->attachments()->save($attachment);

            $message->toUsers()->attach($user->id);

            SendMessageNotificationJob::dispatch($message, collect([$user]))
                ->onQueue($this->loadBalancingService->currentQueueThenAdvance());
        });
    }
}
