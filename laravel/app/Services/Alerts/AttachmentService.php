<?php

namespace App\Services\Alerts;

use App\Services\Alerts\Concerns\AttachmentServiceInterface;
use App\Services\Enums\AlertAttachmentExtention;
use App\Traits\Alerts\AlertAttachable;

class AttachmentService implements AttachmentServiceInterface
{

    use AlertAttachable;

    /**
     * Save an attachment and return its URL
     *
     * @param mixed $export
     * @param AlertAttachmentExtention $extension
     * @param string $filename
     * @return string URL to the saved attachment
     */
    public function save(mixed $export, AlertAttachmentExtention $extension, string $filename,string $date): string
    {
        return $this->saveAttachment($export, $extension, $filename, $date);
    }
}
