<?php

namespace App\Services\Alerts;

use App\Interfaces\Alerts\AlertRepositoryInterface;
use App\Interfaces\Alerts\Sendable;
use App\Models\Alert;
use App\Services\Alerts\Concerns\AttachmentServiceInterface;
use App\Services\Alerts\Concerns\ExportGeneratorInterface;
use App\Services\Alerts\Concerns\MessageServiceInterface;
use App\Services\Enums\AlertTypeReport;
use App\User;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;

class AlertWithInternalMessagingService implements Sendable
{

    public function __construct(
        private readonly AlertRepositoryInterface   $alertRepository,
        private readonly ExportGeneratorInterface   $exportGenerator,
        private readonly AttachmentServiceInterface $attachmentService,
        private readonly MessageServiceInterface    $messageService
    )
    {
    }

    /**
     * Send alerts to users for the given time period
     *
     * @param int $id Alert ID
     * @param string $from Start date
     * @param string $to End date
     * @return void
     * @throws ModelNotFoundException
     */
    public function send(int $id, string $from, string $to): void
    {
        $alert = $this->alertRepository->findWithUsers($id);

        foreach ($alert->users as $user) {
            try {
                // Get data from the alert type service
                $alertData = $this->fetchAlertData($alert, $user, $from, $to);

                // Skip if no data to report
                if (empty($alertData)) {
                    continue;
                }

                // Generate and save attachment
                $attachmentUrl = $this->createAttachment($alertData, $alert, $user, $from);

                // Create and send message with attachment
                $this->sendMessage($user, $alert->type, $attachmentUrl);

            } catch (\Exception $e) {
                // Log the error but continue processing other users
                logger()->error('Failed to process alert for user', [
                    'alert_id' => $id,
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Update the last run time
        $this->alertRepository->updateLastRun($id, Carbon::now());
    }

    /**
     * Fetch data for the alert
     *
     * @param Alert $alert
     * @param mixed $user
     * @param string $from
     * @param string $to
     * @return array
     */
    private function fetchAlertData(Alert $alert, User $user, string $from, string $to): array
    {
        return $alert->type->getService()->fetch($user, $from, $to);
    }

    /**
     * Create an attachment from the alert data
     *
     * @param mixed $data
     * @param Alert $alert
     * @param mixed $user
     * @param string $from
     * @return string Attachment URL
     */
    private function createAttachment(mixed $data, Alert $alert, User $user, string $from): string
    {
        $export = $this->exportGenerator->generate($data, $alert->type);

        $filename = $this->generateFilename($user, $alert->type, $from);

        return $this->attachmentService->save($export, $alert->format, $filename, $from);
    }

    /**
     * Generate a filename for the attachment
     *
     * @param mixed $user
     * @param AlertTypeReport $type
     * @param string $from
     * @return string
     */
    private function generateFilename(User $user, AlertTypeReport $type, string $from): string
    {
        $date = Carbon::parse($from)->format('Y-m-d');
        return str($user->fullname . '_' . $type->value . '_' . $date)->slug('_');
    }

    /**
     * Send a message with the attachment to the user
     *
     * @param mixed $user
     * @param AlertTypeReport $type
     * @param string $attachmentUrl
     * @return void
     */
    private function sendMessage(User $user, AlertTypeReport $type, string $attachmentUrl): void
    {
        $subject = $this->generateSubject($type);
        $body = $this->generateMessageBody($type);

        $this->messageService->sendWithAttachment($user, $subject, $body, $attachmentUrl);
    }

    /**
     * Generate a subject line for the alert message
     *
     * @param AlertTypeReport $type
     * @return string
     */
    private function generateSubject(AlertTypeReport $type): string
    {
        return replacePlaceholders(
            Config::get('alert.templates.subject'),
            [
                'type' => $type->value,
                'date' => Carbon::now()->format('Y-m-d H:i:s')
            ]
        );
    }

    /**
     * Generate a message body for the alert
     *
     * @param AlertTypeReport $type
     * @return string
     */
    private function generateMessageBody(AlertTypeReport $type): string
    {

        return replacePlaceholders(
            Config::get('alert.templates.body'),
            [
                'type' => $type->value,
                'date' => Carbon::now()->format('Y-m-d H:i:s')
            ]
        );

    }


}
