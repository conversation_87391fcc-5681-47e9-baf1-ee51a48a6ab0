<?php


namespace App\Services;

use App\Account;
use App\Exceptions\CrmException;
use App\Mapping;
use App\MappingUnifiedCode;
use App\Models\LinkedPharmacy;
use App\Models\ListType;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LinkedPharmaciesService
{

    public function linkedList(?array $lines = [], ?array $divisions = [], $setting = null,?array $accounts= [])
    {

        $linked = LinkedPharmacy::select(
            DB::raw('IFNULL(group_concat(distinct crm_linked_pharmacies.ratio),"") as ratio'),
            DB::raw('IFNULL(group_concat(distinct crm_mappings.name),"") as pharmacies'),
            DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
            DB::raw('IFNULL(crm_line_divisions.name,"") as division'),
            DB::raw('IFNULL(crm_accounts.notes,"") as notes'),
            'accounts.name as account',
            'accounts.code as account_code',
            'accounts.id as account_id',
            'account_types.name as account_type',
            'division_types.color'
        )
            ->leftJoin('accounts', 'linked_pharmacies.account_id', 'accounts.id')
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
            ->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->where('account_lines.from_date', '<=', (string)Carbon::now())
            ->whereNull('accounts.deleted_at')
            ->where(fn ($q) => $q->where('account_lines.to_date', '=', null)->orWhere('account_lines.to_date', '>=', (string)Carbon::now()));
        if ($setting == 'mapping') {
            $linked = $linked->leftJoin(
                'mappings',
                function ($join) {
                    $join->on('mappings.id', '=', 'linked_pharmacies.pharmable_id');
                    $join->where('linked_pharmacies.pharmable_type', Mapping::class);
                }
            );
        }
        if ($setting == 'unified') {
            $linked = $linked->leftJoin(
                'Mapping_unified_codes',
                function ($join) {
                    $join->on('Mapping_unified_codes.id', '=', 'linked_pharmacies.pharmable_id');
                    $join->where('linked_pharmacies.pharmable_type', MappingUnifiedCode::class);
                }
            );
        }
        if (!empty($accounts)) {
            $linked = $linked->whereIntegerInRaw('linked_pharmacies.account_id', $accounts);
        }

        if (!empty($lines)) {
            $linked = $linked->whereIntegerInRaw('account_lines.line_id', $lines);
        }
        if (!empty($divisions)) {
            $linked = $linked->whereIntegerInRaw('line_divisions.id', $divisions);
        }
        $linked = $linked->groupBy('account_id', "line_divisions.id")->get();
        return $linked;
    }
}
