<?php

namespace App\Services\Sales\Strategies\ProductFiltering;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

/**
 * Class VisibleBrandProductFilteringStrategy
 *
 * Filters products to include only those that are visible (not hidden) and belong to specified brands.
 */
class VisibleBrandProductFilteringStrategy implements ProductFilteringStrategy
{
    private array $brandIds; // Changed from productIds to brandIds for clarity
    private string $fromYear;
    private array $months;

    /**
     * Constructs the VisibleBrandProductFilteringStrategy.
     *
     * @param array $brandIds List of brand IDs to filter products by.
     *                        If empty, only the visibility filter is applied.
     */
    public function __construct(array $brandIds, string $fromYear, array $months)
    {
        $this->brandIds = $brandIds;
        $this->fromYear = $fromYear;
        foreach ($months as $key => $month) {
            $months[$key] = (int)$month;
        }
        $this->months = $months;
    }

    /**
     * Filters products to get visible ones that belong to specified brands.
     *
     * The method applies the following filters:
     * 1. Products must have 'is_hidden' set to 0.
     * 2. If $this->brandIds (from constructor) is not empty, products must belong to one of these brands.
     * It also eager loads 'product.brands'.
     *
     * @param Builder $query The base query for line products (LineProduct query typically).
     * @return Collection A collection of Product models that are visible and match the brand criteria.
     */
    public function filter(Builder $query): Collection
    {
        // Logic from SalesIncentiveCalculationService::getVisibleProductsByBrand
        $query = $query->whereHas('product', fn($q) => $q->where('products.is_hidden', 0));

        if (!empty($this->brandIds)) {
            // Ensure that we are querying the 'id' column of the 'brands' table.
            $query->whereHas('product.brands', fn($brandQuery) => $brandQuery->whereIntegerInRaw("brands.id", $this->brandIds));
        }

        return $query->with('product.brands') // Ensure brands are loaded for context or further operations.
            ->withAvg(
                [
                    // Alias for the average calculation
                    "weights as p_w" => function ($avgQuery) {
                        $avgQuery->where("year", $this->fromYear)
                            ->whereIn("month_id", $this->months);
                    }
                ],
                "value" // The column to average on the weights table
            )
            ->get()
            ->unique('id')
            ->values();
    }
}
