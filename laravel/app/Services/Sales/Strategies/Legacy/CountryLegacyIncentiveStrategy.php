<?php

namespace App\Services\Sales\Strategies\Legacy;

use App\Services\Sales\SalesIncentiveHolder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

/**
 * Class CountryLegacyIncentiveStrategy
 *
 * Implements the legacy incentive calculation logic specifically for "country" level managers.
 * This strategy is applied as the third and typically final pass in a series of legacy calculations.
 * It updates items in the main data collection if their 'product_value' is still zero, implying
 * they haven't been fully processed by lower-level (district/area) calculations.
 */
class CountryLegacyIncentiveStrategy implements LegacyIncentiveStrategy
{
    // Constants from SalesIncentiveCalculationService relevant to this strategy.
    // These might be specific to Country level or shared; original code used DISTRICT_ for these.
    private const COUNTRY_KPIS_PERCENTAGE = 0.05; // Assuming DISTRICT_KPIS_PERCENTAGE was a stand-in
    private const COUNTRY_PRODUCT_VALUE_PERCENTAGE = 0.25; // Assuming DISTRICT_PRODUCT_VALUE_PERCENTAGE was a stand-in

    private const TWO_DECIMAL_ROUND = 2;
    private const SEVENTY_FIVE_RATIO = 0.75;
    private const TWENTY_FIVE_RATIO = 0.25;

    /**
     * Processes the "country" level legacy incentive for a specific manager's incentive entry.
     *
     * It transforms the main data collection by:
     * - Identifying items that match the manager ID, are marked as 'is_total', AND
     *   currently have a 'product_value' of 0 (or not set).
     * - Calculating product value and KPI-based values (using 'manager_coverage').
     * - Updating the item with these calculated values and derived totals (75%/25% splits).
     * - Unlike district/area strategies, this typically does not propagate further up.
     *
     * @param Collection $data The main data collection being transformed (passed by reference).
     * @param SalesIncentiveHolder $incentiveHolder Utility to access incentive configurations. (Not directly used in calculation here but part of interface compliance).
     * @param array $managersIncentiveEntry The specific manager's incentive data being processed.
     *                                      Expected keys: 'value', 'ratio', 'size'.
     * @param int $managerId The ID of the manager whose incentive is being processed.
     * @return void
     */
    public function process(
        Collection &$data,
        SalesIncentiveHolder $incentiveHolder,
        array $managersIncentiveEntry,
        int $managerId
    ): void {
        if (($managersIncentiveEntry['value'] ?? 0.0) == 0.0) { // Ensure value exists and is not zero
            return;
        }

        // Ensure 'size' is not zero to prevent division by zero errors.
        $size = $managersIncentiveEntry['size'] ?? 0;
        if ($size == 0) {
            return;
        }

        $data->transform(function (array $item) use ($managersIncentiveEntry, $managerId, $size) {
            if (
                isset($item['is_total']) && $item['is_total'] &&
                isset($item['id']) && $item['id'] === $managerId &&
                isset($item['level']) && $item['level'] === 1
            ) {
//                Log::info("Processing legacy incentives for Country $managerId");
                // Logic from the third loop of processLegacyIncentives
                $product_value = ($managersIncentiveEntry['ratio'] ?? 0.0) * ($managersIncentiveEntry['value'] / $size);

//                Log::info("Country $managerId has product value of $product_value");

                $managerCoverage = $item['manager_coverage'] ?? 0.0; // Ensure manager_coverage exists
                // Using COUNTRY_ specific constants if they differ
                $kpis_value = (
                    ($managerCoverage * self::COUNTRY_KPIS_PERCENTAGE)
                    *
                    ($product_value * self::COUNTRY_PRODUCT_VALUE_PERCENTAGE)
                );

                $item['product_value'] = round($product_value, self::TWO_DECIMAL_ROUND);
                $item['kpis_value'] = round($kpis_value, self::TWO_DECIMAL_ROUND);
                $total_incentive = $product_value + $kpis_value;
                $item["total_incentive"] = round($total_incentive, self::TWO_DECIMAL_ROUND);
                $item['75%'] = round($total_incentive * self::SEVENTY_FIVE_RATIO, self::TWO_DECIMAL_ROUND);
                $item['25%'] = round($total_incentive * self::TWENTY_FIVE_RATIO, self::TWO_DECIMAL_ROUND);

                // No further incentiveHolder calls (decrease/add) in the original third loop's logic.
            }
            return $item;
        });
    }
}
