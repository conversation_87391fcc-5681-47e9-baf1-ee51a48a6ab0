<?php

namespace App\Services\Sales\Strategies\Legacy;

use App\Services\Sales\SalesIncentiveHolder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

/**
 * Class AreaLegacyIncentiveStrategy
 *
 * Implements the legacy incentive calculation logic specifically for "area" level managers.
 * This strategy is applied as the second pass in a series of legacy calculations.
 * It updates the main data collection based on tracked manager incentives and may influence
 * parent-level incentives, similar to the district strategy but with potentially different
 * KPI considerations (e.g., 'manager_coverage').
 */
class AreaLegacyIncentiveStrategy implements LegacyIncentiveStrategy
{
    // Constants from SalesIncentiveCalculationService relevant to this strategy.
    // These might be specific to Area level or shared; original code used DISTRICT_ for these.
    private const AREA_KPIS_PERCENTAGE = 0.05; // Assuming DISTRICT_KPIS_PERCENTAGE was a stand-in for a potentially area-specific value
    private const AREA_PRODUCT_VALUE_PERCENTAGE = 0.25; // Assuming DISTRICT_PRODUCT_VALUE_PERCENTAGE was a stand-in

    private const TWO_DECIMAL_ROUND = 2;
    private const SEVENTY_FIVE_RATIO = 0.75;
    private const TWENTY_FIVE_RATIO = 0.25;

    /**
     * Processes the "area" level legacy incentive for a specific manager's incentive entry.
     *
     * It transforms the main data collection by:
     * - Identifying items that match the manager ID and are marked as 'is_total'.
     * - Calculating product value and KPI-based values (using 'manager_coverage').
     * - Updating the item with these calculated values and derived totals (75%/25% splits).
     * - Triggering updates in the SalesIncentiveHolder for the current manager's size and parent manager incentives.
     *
     * @param Collection $data The main data collection being transformed (passed by reference).
     * @param SalesIncentiveHolder $incentiveHolder Utility to access incentive configurations and update tracked data.
     * @param array $managersIncentiveEntry The specific manager's incentive data being processed.
     *                                      Expected keys: 'value', 'ratio', 'size'.
     * @param int $managerId The ID of the manager whose incentive is being processed.
     * @return void
     */
    public function process(
        Collection           &$data,
        SalesIncentiveHolder $incentiveHolder,
        array                $managersIncentiveEntry,
        int                  $managerId
    ): void
    {
        if (($managersIncentiveEntry['value'] ?? 0.0) == 0.0) { // Ensure value exists and is not zero
            return;
        }

        // Ensure 'size' is not zero to prevent division by zero errors.
        $size = $managersIncentiveEntry['size'] ?? 0;
        if ($size == 0) {
            return;
        }

        $data->transform(function (array $item) use ($incentiveHolder, $managersIncentiveEntry, $managerId, $size) {
            if (
                isset($item['is_total']) && $item['is_total'] &&
                isset($item['id']) && $item['id'] === $managerId &&
                isset($item['level']) && $item['level'] === 2
            ) {
//                Log::info("Processing legacy incentives for Area $managerId");
                // Logic from the second loop of processLegacyIncentives
                $product_value = ($managersIncentiveEntry['ratio'] ?? 0.0) * ($managersIncentiveEntry['value'] / $size);

                $managerCoverage = $item['manager_coverage'] ?? 0.0; // Ensure manager_coverage exists
                // Using AREA_ specific constants if they differ, otherwise, original DISTRICT_ constants were used.
                $kpis_value = (
                    ($managerCoverage * self::AREA_KPIS_PERCENTAGE)
                    *
                    ($product_value * self::AREA_PRODUCT_VALUE_PERCENTAGE)
                );

                $item['product_value'] = round($product_value, self::TWO_DECIMAL_ROUND);
                $item['kpis_value'] = round($kpis_value, self::TWO_DECIMAL_ROUND);
                $total_incentive = $product_value + $kpis_value;
                $item["total_incentive"] = round($total_incentive, self::TWO_DECIMAL_ROUND);
                $item['75%'] = round($total_incentive * self::SEVENTY_FIVE_RATIO, self::TWO_DECIMAL_ROUND);
                $item['25%'] = round($total_incentive * self::TWENTY_FIVE_RATIO, self::TWO_DECIMAL_ROUND);

                // Delegate calls back to incentiveHolder
                $incentiveHolder->decreaseTrackedManagerIncentiveSize((int)$item['id']); // Decrease current manager's size
                if (isset($item['parent_id'])) {
                    $incentiveHolder->trackManagerIncentive((int)$item['parent_id'], $item['parent_role_id'], $product_value);
//                    Log::info("push to id: " . $item['parent_id'] . " with roleId: " . $item['parent_role_id'] . " incentive of " . $item['total_incentive']);
                }
            }
            return $item;
        });
    }
}
