<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Contracts;

interface SettingsProviderInterface
{
    /**
     * Get sales contribution settings
     *
     * @return array
     */
    public function getSalesContributionSettings(): array;

    /**
     * Get a specific setting value
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getSetting(string $key, $default = null);
}
