<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Contracts;

interface TransactionManagerInterface
{
    /**
     * Begin a database transaction
     *
     * @return bool
     */
    public function beginTransaction(): bool;

    /**
     * Commit the current transaction
     *
     * @return bool
     */
    public function commitTransaction(): bool;

    /**
     * Rollback the current transaction
     *
     * @return bool
     */
    public function rollbackTransaction(): bool;

    /**
     * Execute a callback within a transaction
     *
     * @param callable $callback
     * @return mixed
     * @throws \Throwable
     */
    public function executeInTransaction(callable $callback);
}
