<?php

namespace App\Services\Sales;

/**
 * Class ManagerIncentiveTracker
 *
 * Tracks and manages incentive data for managers. This includes accumulating incentive values,
 * managing participation size (e.g., number of contributing items), and handling ratios.
 */
class ManagerIncentiveTracker
{
    /**
     * Stores the tracked incentive data, keyed by manager ID.
     * Each entry is an array containing details like value, size, ratio, etc.
     *
     * @var array<int, array{id: int, value: float, size: int, ratio: float, roleId: int|null, ratio_is_set: bool}>
     */
    private array $trackedIncentives = [];

    /**
     * Adds or updates a manager's incentive data.
     *
     * If the manager is not already tracked, they are initialized. The value and size
     * for the manager's incentive are incremented. If a default ratio is provided and
     * no ratio has been set yet, it's applied.
     *
     * @param int|null $managerId The ID of the manager.
     * @param int|null $roleId The role ID of the manager, if applicable.
     * @param float $value The incentive value to add.
     * @param float|null $defaultRatio The default ratio to apply if none is set.
     * @return void
     */
    public function add(?int $managerId, ?int $roleId, float $value, ?float $defaultRatio): void
    {
        if (!isset($this->trackedIncentives[$managerId])) {
            $this->trackedIncentives[$managerId] = [
                'id' => $managerId,
                'value' => 0.0, // Initialize value as float
                'size' => 0,
                'ratio' => $defaultRatio ?? 0.0,
                'roleId' => $roleId,
                'ratio_is_set' => $defaultRatio !== null, // If defaultRatio is provided, consider it initially set
            ];
        }

        $this->trackedIncentives[$managerId]['value'] += $value;
        $this->trackedIncentives[$managerId]['size']++;

        // This logic ensures that a ratio, once set (either initially or if $defaultRatio was not null at first add),
        // isn't overridden by subsequent adds unless explicitly managed elsewhere.
        // The original logic for 'ratio_is_set' has been slightly simplified during initialization.
        // If a defaultRatio is provided during the first add, it's considered set.
        if (!$this->trackedIncentives[$managerId]['ratio_is_set'] && $defaultRatio !== null) {
            $this->trackedIncentives[$managerId]['ratio'] = $defaultRatio;
            $this->trackedIncentives[$managerId]['ratio_is_set'] = true;
        }
    }

    /**
     * Removes a manager's incentive data from tracking.
     *
     * @param int $managerId The ID of the manager to remove.
     * @return void
     */
    public function remove(int $managerId): void
    {
        unset($this->trackedIncentives[$managerId]);
    }

    /**
     * Decreases the size (e.g., participation count) for a manager's incentive.
     * If the size drops to zero or below, the manager is removed from tracking.
     *
     * @param int $managerId The ID of the manager whose incentive size is to be decreased.
     * @return void
     */
    public function decreaseSize(int $managerId): void
    {
        if (isset($this->trackedIncentives[$managerId])) {
            $this->trackedIncentives[$managerId]['size']--;
            if ($this->trackedIncentives[$managerId]['size'] <= 0) {
                $this->remove($managerId);
            }
        }
    }

    /**
     * Retrieves the tracked incentive data for a specific manager.
     *
     * @param int $managerId The ID of the manager.
     * @return array{id: int, value: float, size: int, ratio: float, roleId: int|null, ratio_is_set: bool}|null
     *         The manager's incentive data, or null if not found.
     */
    public function get(int $managerId): ?array
    {
        return $this->trackedIncentives[$managerId] ?? null;
    }

    /**
     * Retrieves all tracked incentive data.
     *
     * @return array<int, array{id: int, value: float, size: int, ratio: float, roleId: int|null, ratio_is_set: bool}>
     *         An array of all tracked manager incentives.
     */
    public function getAll(): array
    {
        return $this->trackedIncentives;
    }
}
