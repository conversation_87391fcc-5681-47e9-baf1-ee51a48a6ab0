<?php


namespace App\Services;

use App\Account;
use App\ActualVisit;
use App\Doctor;
use App\Exceptions\CrmException;
use App\PlanVisit;
use App\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PlanService
{

    public function getPlans($object, $table, $from, $to, ?array $shifts = [], ?array $lines = [], ?array $specialities = [], ?array $accountTypes = [],)
    {
        $plans = DB::table('planned_visits')->select(
            'planned_visits.id',
            'planned_visits.account_id as account_id',
            'actual_visits.plan_id as plan_id',
            DB::raw('DATE_FORMAT(crm_planned_visits.visit_date,"%Y-%m-%d") as date'),
            'users.fullname as user',
            'lines.name as line',
            'visit_types.name as type',
            'line_divisions.name as division',
            // DB::raw('IFNULL(crm_accounts.id,"") as account_id'),
            DB::raw('IFNULL(crm_accounts.name,"") as account'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_specialities.name,"") as speciality'),
            DB::raw('IFNULL(crm_account_types.name,"") as acc_type'),
            DB::raw('IFNULL(crm_shifts.name,"") as shift'),
            'plan_visit_details.approval as status',
        )
            ->leftJoin('lines', 'planned_visits.line_id', 'lines.id')
            ->leftJoin('users', 'planned_visits.user_id', 'users.id')
            ->leftJoin('line_divisions', 'planned_visits.div_id', 'line_divisions.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('planned_visits.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', PlanVisit::class);
                }
            )
            ->leftJoin(
                'actual_visits',
                function ($join) {
                    $join->on('planned_visits.id', '=', 'actual_visits.plan_id');
                    $join->on('planned_visits.user_id', '=', 'actual_visits.user_id');
                }
            )
            ->leftJoin('shifts', 'planned_visits.shift_id', 'shifts.id')
            ->leftJoin('accounts', 'planned_visits.account_id', 'accounts.id')
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
            ->leftJoin('doctors', 'planned_visits.account_dr_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('visit_types', 'planned_visits.visit_type', 'visit_types.id')
            ->whereNull('planned_visits.deleted_at')
            ->where($table, $object->id)
            ->whereBetween('planned_visits.visit_date', [$from, $to])
            ->where('plan_visit_details.approval', 1);
        if (!empty($accountTypes)) {
            $plans = $plans->whereIntegerInRaw('account_types.id', $accountTypes);
        }
        if (!empty($lines)) {
            $plans = $plans->whereIntegerInRaw('planned_visits.line_id', $lines);
        }
        if (!empty($shifts)) {
            $plans = $plans->whereIntegerInRaw('account_types.shift_id', $shifts);
        }
        $plans = $plans->get();
        return $plans;
    }
    public function getCalendarPlans($object, $table, $from, $to)
    {
        $plans = DB::table('planned_visits')->select(
            'planned_visits.id',
            'planned_visits.account_id as account_id',
            'actual_visits.plan_id as plan_id',
            DB::raw('DATE_FORMAT(crm_planned_visits.visit_date,"%Y-%m-%d") as plan_date'),
            'actual_visits.visit_date as actual_date',
            'users.fullname as user',
            'lines.name as line',
            'visit_types.name as type',
            'line_divisions.name as division',
            // DB::raw('IFNULL(crm_accounts.id,"") as account_id'),
            DB::raw('IFNULL(crm_accounts.name,"") as account'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_specialities.name,"") as speciality'),
            DB::raw('IFNULL(crm_account_types.name,"") as acc_type'),
            DB::raw('IFNULL(crm_account_types.id,"") as acc_type_id'),
            DB::raw('IFNULL(crm_shifts.name,"") as shift'),
            'plan_visit_details.approval as status',
        )
            ->leftJoin('lines', 'planned_visits.line_id', 'lines.id')
            ->leftJoin('users', 'planned_visits.user_id', 'users.id')
            ->leftJoin('line_divisions', 'planned_visits.div_id', 'line_divisions.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('planned_visits.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', PlanVisit::class);
                }
            )
            ->leftJoin(
                'actual_visits',
                function ($join) {
                    $join->on('planned_visits.id', '=', 'actual_visits.plan_id');
                    $join->on('planned_visits.user_id', '=', 'actual_visits.user_id');
                }
            )
            ->leftJoin('shifts', 'planned_visits.shift_id', 'shifts.id')
            ->leftJoin('accounts', 'planned_visits.account_id', 'accounts.id')
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
            ->leftJoin('doctors', 'planned_visits.account_dr_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('visit_types', 'planned_visits.visit_type', 'visit_types.id')
            ->whereNull('planned_visits.deleted_at')
            ->where($table, $object->id)
            ->whereBetween('planned_visits.visit_date', [$from, $to]);
        $plans = $plans->get();
        return $plans;
    }
    public function getUserPlansPerDate($user, $date)
    {
        $plans = PlanVisit::select(
            'planned_visits.id',
            'planned_visits.account_id as account_id',
            DB::raw('DATE_FORMAT(crm_planned_visits.visit_date,"%Y-%m-%d") as date'),
            'users.fullname as user',
            'lines.name as line',
            'visit_types.name as type',
            'line_divisions.name as division',
            // DB::raw('IFNULL(crm_accounts.id,"") as account_id'),
            DB::raw('IFNULL(crm_accounts.name,"") as account'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_specialities.name,"") as speciality'),
            DB::raw('IFNULL(crm_account_types.name,"") as acc_type'),
            DB::raw('IFNULL(crm_account_types.id,"") as acc_type_id'),
            DB::raw('IFNULL(crm_account_types.shift_id,"") as acc_type_shift_id'),
            DB::raw('IFNULL(crm_shifts.name,"") as shift'),
            DB::raw('IFNULL(crm_shifts.id,"") as shift_id'),
            DB::raw('CONCAT(crm_accounts.name," (", crm_doctors.name ,")") AS account_doctor'),
            'plan_visit_details.approval as status',
            
        )
            ->leftJoin('lines', 'planned_visits.line_id', 'lines.id')
            ->leftJoin('users', 'planned_visits.user_id', 'users.id')
            ->leftJoin('line_divisions', 'planned_visits.div_id', 'line_divisions.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('planned_visits.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', PlanVisit::class);
                }
            )
            ->leftJoin('shifts', 'planned_visits.shift_id', 'shifts.id')
            ->leftJoin('accounts', 'planned_visits.account_id', 'accounts.id')
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
            ->leftJoin('doctors', 'planned_visits.account_dr_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('visit_types', 'planned_visits.visit_type', 'visit_types.id')
            ->whereNull('planned_visits.deleted_at')
            ->where('users.id', $user->id)
            ->whereDate('visit_date', Carbon::parse($date)->toDateString())->get();
        return $plans;
    }
}
