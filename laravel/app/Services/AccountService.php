<?php


namespace App\Services;

use App\Account;
use App\ActualVisit;
use App\Exceptions\CrmException;
use App\Models\ListType;
use App\PlanVisit;
use App\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AccountService
{

    public function getAccounts(?array $lines = [], ?array $divisions = [], ?Carbon $from = null, ?carbon $to = null, ?array $shifts = [], ?array $specialities = [], ?array $accountTypes = [], ?array $bricks = []): Collection
    {
        $setting = ListType::first()->type == 'Default List';
        $accounts = Account::select(
            [
                'accounts.id as account_id',
                'accounts.id as id',
                'lines.name as line',
                'lines.id as line_id',
                'line_divisions.name as division',
                'line_divisions.id as div_id',
                DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                DB::raw('IFNULL(group_concat(distinct crm_bricks.id),"") as brick_id'),
                DB::raw('IFNULL(crm_accounts.code,"") as acc_code'),
                DB::raw('IFNULL(crm_accounts.name,"") as account'),
                DB::raw('IFNULL(group_concat(distinct crm_account_types.shift_id),"") as acc_shift_id'),
                'account_types.name as account_type',
                'account_types.id as account_type_id',
                DB::raw('IFNULL(group_concat(distinct crm_a.id),"") as acc_class_id'),
                DB::raw('IFNULL(group_concat(distinct crm_a.name),"") as class'),
                DB::raw('IFNULL(crm_accounts.tel,"") as tel'),
                DB::raw('IFNULL(crm_accounts.email,"") as email'),
                DB::raw('IFNULL(crm_accounts.address,"") as address'),
                DB::raw('IFNULL(group_concat(distinct crm_doctors.name),"") as doctor'),
                DB::raw('IFNULL(group_concat(distinct crm_doctors.id),"") as doctor_id'),
                DB::raw('IFNULL(group_concat(distinct crm_doctors.ucode),"") as ucode'),
                DB::raw('IFNULL(group_concat(distinct crm_specialities.name),"") as speciality'),
                DB::raw('IFNULL(group_concat(distinct crm_specialities.id),"") as speciality_id'),
                DB::raw('IFNULL(group_concat(DATE_FORMAT(crm_actual_visits.visit_date,"%d-%m-%Y"),"  "),"") as visit_date'),
                'division_types.color'
            ]
        )
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
            ->leftJoin('actual_visits', function ($join) use ($from, $to) {
                $join->on('accounts.id', 'actual_visits.account_id')
                    ->whereBetween('visit_date', [$from, $to]);
            });
        if (!$setting) {
            $accounts = $accounts
                ->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
                ->leftJoin(
                    'new_account_doctors',
                    function ($join) {
                        $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                        $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id');
                    }
                );
        } else {
            $accounts = $accounts->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
                ->leftJoin('new_account_doctors', 'accounts.id', 'new_account_doctors.account_id');
        }
        $accounts = $accounts->leftJoin('doctors', 'new_account_doctors.doctor_id', 'doctors.id')
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('classes as a', 'account_lines.class_id', 'a.id')
            ->leftJoin('line_users_divisions', function ($join) {
                $join->on('line_divisions.id', 'line_users_divisions.line_division_id')
                    ->where('line_users_divisions.from_date', '<=', Carbon::now())
                    ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)
                        ->orWhere('line_users_divisions.to_date', '>=', (string)Carbon::now()));
            })
            // ->orderBy('accounts.code', 'asc')
            ->whereNull('new_account_doctors.deleted_at')
            ->whereNull('account_lines.deleted_at')
            ->whereNull('doctors.deleted_at')
            ->where(fn($q) => $q->where('accounts.inactive_date', '=', null)->orWhere('accounts.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('doctors.inactive_date', '=', null)->orWhere('doctors.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('account_lines.to_date', '=', null)->orWhere('account_lines.to_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)->orWhere('new_account_doctors.to_date', '>=', (string)Carbon::now()))
            ->where('accounts.active_date', '<=', Carbon::now())
            ->where('doctors.active_date', '<=', Carbon::now())
            ->where('new_account_doctors.from_date', '<=', Carbon::now())
            ->whereNull('new_account_doctors.deleted_at')
            ->whereNull('doctors.deleted_at');
        if (!empty($lines)) {
            $accounts = $accounts->whereIntegerInRaw('new_account_doctors.line_id', $lines)
                ->whereIntegerInRaw('account_lines.line_id', $lines);
        }
        if (!empty($divisions)) {
            $accounts = $accounts->whereIntegerInRaw('account_lines.line_division_id', $divisions);
        }
        if (!empty($accountTypes)) {
            $accounts = $accounts->whereIntegerInRaw('accounts.type_id', $accountTypes);
        }
        if (!empty($specialities)) {
            $accounts = $accounts->whereIntegerInRaw('specialities.id', $specialities);
        }
        if (!empty($shifts)) {
            $accounts = $accounts->whereIntegerInRaw('account_types.shift_id', $shifts);
        }
        if (!empty($bricks)) {
            $accounts = $accounts->whereIntegerInRaw('bricks.id', $bricks);
        }
        return $accounts
            ->groupBy('accounts.id', 'account_types.name', 'lines.id', 'line_divisions.id')
            ->get();
    }

    public function getInactiveAccounts(?array $lines = [], ?array $divisions = [], ?Carbon $from = null, ?carbon $to = null, ?array $shifts = [], ?array $specialities = [], ?array $accountTypes = [], ?array $bricks = [])
    {
        $accounts = Account::select(
            [
                'accounts.id as account_id',
                'lines.name as line',
                'lines.id as line_id',
                'line_divisions.name as division',
                'line_divisions.id as div_id',
                DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                DB::raw('IFNULL(group_concat(distinct crm_bricks.id),"") as brick_id'),
                DB::raw('IFNULL(crm_accounts.code,"") as acc_code'),
                DB::raw('IFNULL(crm_accounts.name,"") as account'),
                DB::raw('IFNULL(group_concat(distinct crm_account_types.shift_id),"") as acc_shift_id'),
                'account_types.name as account_type',
                'account_types.id as account_type_id',
                DB::raw('IFNULL(group_concat(distinct crm_a.id),"") as acc_class_id'),
                DB::raw('IFNULL(group_concat(distinct crm_a.name),"") as class'),
                DB::raw('IFNULL(crm_accounts.tel,"") as tel'),
                DB::raw('IFNULL(crm_accounts.email,"") as email'),
                DB::raw('IFNULL(crm_accounts.address,"") as address'),
                DB::raw('IFNULL(group_concat(distinct crm_doctors.name),"") as doctor'),
                DB::raw('IFNULL(group_concat(distinct crm_doctors.id),"") as doctor_id'),
                DB::raw('IFNULL(group_concat(distinct crm_doctors.ucode),"") as ucode'),
                DB::raw('IFNULL(group_concat(distinct crm_specialities.name),"") as speciality'),
                DB::raw('IFNULL(group_concat(distinct crm_specialities.id),"") as speciality_id'),
                DB::raw("DATE_FORMAT(crm_accounts.active_date, '%d-%b-%Y') as active_date"),
                'division_types.color'
            ]
        )
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
            ->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
            ->leftJoin('new_account_doctors', 'accounts.id', 'new_account_doctors.account_id')
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin('doctors', 'new_account_doctors.doctor_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('classes as a', 'account_lines.class_id', 'a.id')
            ->orderBy('accounts.code', 'asc')
            ->whereNull('new_account_doctors.deleted_at')
            ->whereNull('account_lines.deleted_at')
            ->whereNull('doctors.deleted_at')
            ->where(fn($q) => $q->where('new_account_doctors.from_date', '>', (string)Carbon::now())
                ->orWhere('new_account_doctors.to_date', '<', (string)Carbon::now()))
            ->whereNull('account_lines.deleted_at')
            ->whereNull('new_account_doctors.deleted_at')
            ->whereNull('doctors.deleted_at');
        if (!empty($lines)) {
            $accounts = $accounts->whereIntegerInRaw('new_account_doctors.line_id', $lines)->whereIntegerInRaw('account_lines.line_id', $lines);
        }
        if (!empty($divisions)) {
            $accounts = $accounts->whereIntegerInRaw('line_divisions.id', $divisions);
        }
        if (!empty($accountTypes)) {
            $accounts = $accounts->whereIntegerInRaw('accounts.type_id', $accountTypes);
        }
        if (!empty($specialities)) {
            $accounts = $accounts->whereIntegerInRaw('speciality_id', $specialities);
        }
        if (!empty($shifts)) {
            $accounts = $accounts->whereIntegerInRaw('account_types.shift_id', $shifts);
        }
        if (!empty($bricks)) {
            $accounts = $accounts->whereIntegerInRaw('bricks.id', $bricks);
        }
        return $accounts->groupBy('accounts.id', 'account_types.name', 'lines.id', 'line_divisions.id')->get();
    }
    public function getAllAccounts(?array $lines = [], ?array $divisions = [], ?Carbon $from = null, ?carbon $to = null, ?array $shifts = [], ?array $specialities = [], ?array $accountTypes = [], ?array $bricks = [])
    {
        $setting = ListType::first()->type == 'Default List';
        $accounts = Account::select(
            [
                'accounts.id as account_id',
                'accounts.id as id',
                'lines.name as line',
                'lines.id as line_id',
                'line_divisions.name as division',
                'line_divisions.id as div_id',
                DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                DB::raw('IFNULL(group_concat(distinct crm_bricks.id),"") as brick_id'),
                DB::raw('IFNULL(crm_accounts.code,"") as acc_code'),
                DB::raw('IFNULL(crm_accounts.name,"") as account'),
                DB::raw('IFNULL(group_concat(distinct crm_account_types.shift_id),"") as acc_shift_id'),
                'account_types.name as account_type',
                'account_types.id as account_type_id',
                DB::raw('IFNULL(group_concat(distinct crm_a.id),"") as acc_class_id'),
                DB::raw('IFNULL(group_concat(distinct crm_a.name),"") as class'),
                DB::raw('IFNULL(crm_accounts.tel,"") as tel'),
                DB::raw('IFNULL(crm_accounts.email,"") as email'),
                DB::raw('IFNULL(crm_accounts.address,"") as address'),
                DB::raw('IFNULL(group_concat(distinct crm_doctors.name),"") as doctor'),
                DB::raw('IFNULL(group_concat(distinct crm_doctors.id),"") as doctor_id'),
                DB::raw('IFNULL(group_concat(distinct crm_doctors.ucode),"") as ucode'),
                DB::raw('IFNULL(group_concat(distinct crm_specialities.name),"") as speciality'),
                DB::raw('IFNULL(group_concat(distinct crm_specialities.id),"") as speciality_id'),
                DB::raw('IFNULL(group_concat(DATE_FORMAT(crm_actual_visits.visit_date,"%d-%m-%Y"),"  "),"") as visit_date'),
                'division_types.color'
            ]
        )
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
            ->leftJoin('actual_visits', function ($join) use ($from, $to) {
                $join->on('accounts.id', 'actual_visits.account_id')
                    ->whereBetween('visit_date', [$from, $to]);
            });
        if (!$setting) {
            $accounts = $accounts
                ->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
                ->leftJoin(
                    'new_account_doctors',
                    function ($join) {
                        $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                        $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id');
                    }
                );
        } else {
            $accounts = $accounts->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
                ->leftJoin('new_account_doctors', 'accounts.id', 'new_account_doctors.account_id');
        }
        $accounts = $accounts->leftJoin('doctors', 'new_account_doctors.doctor_id', 'doctors.id')
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('classes as a', 'account_lines.class_id', 'a.id')
            ->leftJoin('line_users_divisions', function ($join) {
                $join->on('line_divisions.id', 'line_users_divisions.line_division_id')
                    ->where('line_users_divisions.from_date', '<=', Carbon::now())
                    ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)
                        ->orWhere('line_users_divisions.to_date', '>=', (string)Carbon::now()));
            })
            // ->orderBy('accounts.code', 'asc')
            ->whereNull('new_account_doctors.deleted_at')
            ->whereNull('account_lines.deleted_at')
            ->whereNull('doctors.deleted_at')
            ->whereNull('doctors.deleted_at');
        if (!empty($lines)) {
            $accounts = $accounts->whereIntegerInRaw('new_account_doctors.line_id', $lines)
                ->whereIntegerInRaw('account_lines.line_id', $lines);
        }
        if (!empty($divisions)) {
            $accounts = $accounts->whereIntegerInRaw('account_lines.line_division_id', $divisions);
        }
        if (!empty($accountTypes)) {
            $accounts = $accounts->whereIntegerInRaw('accounts.type_id', $accountTypes);
        }
        if (!empty($specialities)) {
            $accounts = $accounts->whereIntegerInRaw('specialities.id', $specialities);
        }
        if (!empty($shifts)) {
            $accounts = $accounts->whereIntegerInRaw('account_types.shift_id', $shifts);
        }
        if (!empty($bricks)) {
            $accounts = $accounts->whereIntegerInRaw('bricks.id', $bricks);
        }
        return $accounts
            ->groupBy('accounts.id', 'account_types.name', 'lines.id', 'line_divisions.id')
            ->get();
    }
    public function getAccountsCoverage(?array $lines = [], ?array $divisions = [], ?Carbon $from = null, ?carbon $to = null, ?array $shifts = [], ?array $specialities = [], ?array $accountTypes = [], ?array $bricks = [], ?array $classes = []): Collection
    {
        $setting = ListType::first()->type == 'Default List';
        $accounts = Account::select(
            [
                'accounts.id as account_id',
                'accounts.id as id',
                'lines.name as line',
                'lines.id as line_id',
                'line_divisions.name as division',
                'line_divisions.id as div_id',
                DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                DB::raw('IFNULL(group_concat(distinct crm_bricks.id),"") as brick_id'),
                DB::raw('IFNULL(crm_accounts.code,"") as acc_code'),
                DB::raw('IFNULL(crm_accounts.name,"") as account'),
                DB::raw('IFNULL(crm_account_types.shift_id,"") as acc_shift_id'),
                'account_types.name as account_type',
                'account_types.id as account_type_id',
                DB::raw('IFNULL(group_concat(distinct crm_a.id),"") as acc_class_id'),
                DB::raw('IFNULL(group_concat(distinct crm_a.name),"") as class'),
                DB::raw('IFNULL(crm_accounts.tel,"") as tel'),
                DB::raw('IFNULL(crm_accounts.email,"") as email'),
                DB::raw('IFNULL(crm_accounts.address,"") as address'),
                DB::raw('IFNULL(group_concat(distinct crm_doctors.name),"") as doctor'),
                DB::raw('IFNULL(group_concat(distinct crm_doctors.id),"") as doctor_id'),
                DB::raw('IFNULL(group_concat(distinct crm_doctors.ucode),"") as ucode'),
                DB::raw('IFNULL(group_concat(distinct crm_specialities.name),"") as speciality'),
                DB::raw('IFNULL(group_concat(distinct crm_specialities.id),"") as speciality_id'),
                DB::raw('IFNULL(group_concat(DATE_FORMAT(crm_actual_visits.visit_date,"%d-%m-%Y"),"  "),"") as visit_date'),
                'division_types.color'
            ]
        )
            ->where('accounts.active_date', '<=', Carbon::now())
            ->where(fn($q) => $q->where('accounts.inactive_date', '=', null)->orWhere('accounts.inactive_date', '>=', (string)Carbon::now()))
            ->join('account_types', function ($join) use ($accountTypes, $shifts) {
                $join->on('accounts.type_id', 'account_types.id');
                if (!empty($accountTypes)) {
                    $join->whereIntegerInRaw('account_types.id', $accountTypes);
                }
                if (!empty($shifts)) {
                    $join->whereIntegerInRaw("account_types.shift_id", $shifts);
                }
            })
            ->join('account_lines', function ($join) use ($lines, $divisions, $bricks, $classes, $from, $to) {
                $join->on('accounts.id', 'account_lines.account_id')
                    ->whereIntegerInRaw('account_lines.line_id', $lines)
                    ->whereNull('account_lines.deleted_at')
                    ->where(function ($query) use ($from, $to) {
                        $query->where(function ($subQuery) use ($from, $to) {
                            $subQuery->whereNull('account_lines.to_date') // Active records
                                ->orWhereBetween('account_lines.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                                ->orWhere('account_lines.to_date', '>=', $to->toDateString()); // Ends within range
                        })
                            ->where(function ($subQuery) use ($from, $to) {
                                $subQuery->where('account_lines.from_date', '<=', $from->toDateString()) // Starts before range
                                    ->orWhereBetween('account_lines.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                            });
                    })
                    ->whereIntegerInRaw('account_lines.line_division_id', $divisions);
                if (!empty($bricks)) {
                    $join->whereIntegerInRaw('account_lines.brick_id', $bricks);
                }
                if (!empty($classes)) {
                    $join->whereIntegerInRaw('account_lines.class_id', $classes);
                }
            })->leftJoin('actual_visits', function ($join) use ($from, $to) {
                $join->on('accounts.id', 'actual_visits.account_id')
                    ->whereBetween('visit_date', [$from, $to]);
            });
        if (!$setting) {
            $accounts = $accounts
                ->join(
                    'new_account_doctors',
                    function ($join) use ($lines, $to, $from) {
                        $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                        $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id')
                            ->whereIntegerInRaw('new_account_doctors.line_id', $lines)
                            ->whereNull('new_account_doctors.deleted_at')
                            ->where(function ($query) use ($from, $to) {
                                $query->where(function ($subQuery) use ($from, $to) {
                                    $subQuery->whereNull('new_account_doctors.to_date') // Active records
                                        ->orWhereBetween('new_account_doctors.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                                        ->orWhere('new_account_doctors.to_date', '>=', $to->toDateString()); // Ends within range
                                })
                                    ->where(function ($subQuery) use ($from, $to) {
                                        $subQuery->where('new_account_doctors.from_date', '<=', $from->toDateString()) // Starts before range
                                            ->orWhereBetween('new_account_doctors.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                                    });
                            });
                    }
                );
        } else {
            $accounts = $accounts
                ->join('new_account_doctors', function ($join) use ($lines, $from, $to) {
                    $join->on('accounts.id', 'new_account_doctors.account_id')
                        ->whereIntegerInRaw('new_account_doctors.line_id', $lines)
                        ->whereNull('new_account_doctors.deleted_at')
                        ->where(function ($query) use ($from, $to) {
                            $query->where(function ($subQuery) use ($from, $to) {
                                $subQuery->whereNull('new_account_doctors.to_date') // Active records
                                    ->orWhereBetween('new_account_doctors.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                                    ->orWhere('new_account_doctors.to_date', '>=', $to->toDateString()); // Ends within range
                            })
                                ->where(function ($subQuery) use ($from, $to) {
                                    $subQuery->where('new_account_doctors.from_date', '<=', $from->toDateString()) // Starts before range
                                        ->orWhereBetween('new_account_doctors.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                                });
                        });
                });
        }
        $accounts = $accounts->join('doctors', function ($join) use ($specialities) {
            $join->on('new_account_doctors.doctor_id', 'doctors.id')
                ->whereNull('doctors.deleted_at');
            if (!empty($specialities)) {
                $join->whereIntegerInRaw('doctors.speciality_id', $specialities);
            }
        })
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin('classes as a', 'account_lines.class_id', 'a.id')
            ->leftJoin('line_users_divisions', function ($join) {
                $join->on('line_divisions.id', 'line_users_divisions.line_division_id')
                    ->where('line_users_divisions.from_date', '<=', Carbon::now())
                    ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)
                        ->orWhere('line_users_divisions.to_date', '>=', (string)Carbon::now()));
            });
        return $accounts
            ->groupBy('accounts.id', 'account_types.name', 'lines.id', 'line_divisions.id')
            ->get();
    }
}
