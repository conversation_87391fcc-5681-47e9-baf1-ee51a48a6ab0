<?php

namespace App\Providers;

use App\Setting;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\ServiceProvider;

class DatabaseConfigProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // $settings = Setting::get();
        // if ($settings) {
        //     $settings->each(function ($setting) {
        //         Config::set($setting->key, $setting->value);
        //     });
        // }
    }
}
