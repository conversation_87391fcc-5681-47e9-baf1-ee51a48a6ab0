<?php

namespace App\Providers;

use App\Events\ProductOffer;
use App\Line;
use App\Listeners\DistributeProductOffer;
use App\Listeners\RevokeExistingTokens;
use App\Observers\LineObserver;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Laravel\Passport\Events\AccessTokenCreated;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        ProductOffer::class=>[
            DistributeProductOffer::class
        ],
        AccessTokenCreated::class => [
            RevokeExistingTokens::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        Line::observe(LineObserver::class);
    }
}
