<?php

namespace App\Providers;

use App\Exceptions\CrmException;
use App\Rules\PercentagePerMapping;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\ServiceProvider;
use Illuminate\Validation\Rules\DatabaseRule;
use Illuminate\Validation\Validator;

class CustomValidatorProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // check existence of records but for polymorph tables
        $this->PolyExists();
        $this->PolyDiff();
        $this->PolyPlanRejected();
        $this->PercentagePerCodeDivision();
        $this->existsNotSoftlyDeleted();


    }


    private function PolyExists()
    {
        $this->app['validator']->extend('poly_exists', function ($attribute, $value, $parameters, $validator) {

            if (!$objectType = $validator->getData()[$parameters[0]]) {
                return false;
            }

            return !empty(resolve($objectType)->find($value));
        });
    }

    private function PolyPlanRejected()
    {
        $this->app['validator']->extend('poly_plan_rejected', function ($attribute, $value, $parameters, $validator) {

            if (!$objectType = $validator->getData()[$parameters[0]]) {
                return false;
            }

            return resolve($objectType)->find($value)->isRejected();
        });
    }

    private function PolyDiff()
    {
        $this->app['validator']->extend('poly_diff', function ($attribute, $value, $parameters, $validator) {
            if (!$objectType = $validator->getData()[$parameters[0]]) {
                return false;
            }
            if (!$objectDiffValue = $validator->getData()[$parameters[2]]) {
                return false;
            }

            $object = resolve($objectType)->find($value);
            $restrictedObject = resolve($parameters[1])->find($objectDiffValue);

            if ($object != $restrictedObject) {
                return true;
            }

            return false;
        });
    }

    private function PercentagePerCodeDivision()
    {
        $this->app['validator']->extend('percent_per_code', function ($attribute, $value, $parameters, $validator) {
            // $row = explode(".", $attribute)[0];
            return (new PercentagePerMapping(collect($validator->getData())))->passes($attribute, $value);
        }, PercentagePerMapping::getMessage());
    }

    public function existsNotSoftlyDeleted()
    {

        $this->app['validator']->extend('exists_not_soft_deleted', function ($attribute, $value, $parameters, $validator) {
            $table = $parameters[0];
            $column = $parameters[1] ?? 'id';

            $query = DB::table($table)
                ->where($column, $value)
                ->whereNull('deleted_at');

            return $query->exists();
        },
        trans('validation.exists')
    );
    }


}
