<?php

namespace App\Notifications;

use App\Helpers\Notifications\ANotification;
use App\User;

class VacationApprovedNotification extends ANotification
{


    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(private String $message, private User $user)
    {
        //
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray()
    {
        return [
            "type" => "vacation",
            "message" => $this->message,
            "link" => ["name" => "vacations"],
            "user" => [
                "id" => $this->user->id,
                "name" => $this->user->fullname,
                "url" => $this->user->url
            ]
        ];
    }
}
