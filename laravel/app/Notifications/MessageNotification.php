<?php

namespace App\Notifications;

use App\Helpers\Notifications\ANotification;

class MessageNotification extends ANotification
{
    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(private $messageData)
    {
    }


    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray()
    {
        return [
            "type" => "message",
            "id" => $this->messageData->id,
            "subject" => $this->messageData->subject,
            "time" => $this->messageData->created_at,
            "sender" => [
                "id" => $this->messageData->sender->id,
                "name" => $this->messageData->sender->name,
                "url" => $this->messageData->sender->url
            ]
        ];
    }
}
