<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PlanApproval extends Model
{
    use SoftDeletes ;

    protected $guard_name = 'api';

    protected $table = 'plan_approvals';

        /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'plan_id', 'user_id', 'response'
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }
}
