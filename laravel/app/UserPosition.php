<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

class UserPosition extends Model
{

    protected $guard_name = 'api';

    protected $table = 'user_positions';

    protected $fillable = [
        'user_id',
        'position_id',
        'from_date',
        'to_date',
        'show_below_users'
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function position(): BelongsTo
    {
        return $this->belongsTo(Position::class);
    }

    public function lines(): BelongsToMany
    {
        return $this->belongsToMany(Line::class, 'line_user_positions', 'user_position_id', 'line_id')->whereNull('line_user_positions.deleted_at');
    }
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'line_product_positions', 'user_position_id', 'product_id')->whereNull('line_product_positions.deleted_at');
    }
    public function divisions(): BelongsToMany
    {
        return $this->belongsToMany(LineDivision::class, 'line_division_positions', 'user_position_id', 'line_div_id')
            ->whereNull('line_division_positions.deleted_at');
    }

    public function doubleVisitSetting(): BelongsTo
    {
        return $this->belongsTo(PositionDoubleVisitSetting::class, "id", "user_position_id");
    }
}
