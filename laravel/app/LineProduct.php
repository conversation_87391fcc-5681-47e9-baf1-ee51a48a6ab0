<?php

namespace App;

use App\Models\ProductWeight;
use App\Traits\ModelImportable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class LineProduct extends Model
{
    use SoftDeletes ;
    use ModelImportable;

    protected $guard_name = 'api';

    protected $table = 'line_products';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'product_id', 'line_id','ratio', 'from_date', 'to_date','file_id'
    ];

    protected $casts = [
        'from_date' => 'datetime',
        'to_date' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

    public function line()
    {
        return $this->belongsTo(Line::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    /**
     * Get the weights for this line product.
     * This relation matches records where:
     * - line_id in LineProduct matches line_id in ProductWeight
     * - product_id in LineProduct matches product_id in ProductWeight
     */
    public function weights(): HasMany
    {
        return $this->hasMany(ProductWeight::class, 'product_id', 'product_id')
            ->whereColumn('line_products.line_id','product_weights.line_id');
    }

}
