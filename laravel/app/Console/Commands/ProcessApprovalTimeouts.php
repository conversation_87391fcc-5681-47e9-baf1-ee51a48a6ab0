<?php

namespace App\Console\Commands;

use App\Models\Request as ApprovalRequest;
use App\Notifications\ApprovalEscalationNotification;
use Carbon\Carbon;
use Illuminate\Console\Command;

class ProcessApprovalTimeouts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'approvals:process-timeouts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process approval requests that have exceeded their time limits';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Processing approval timeouts...');

        // Get all active requests
        $requests = ApprovalRequest::whereIn('status', ['submitted', 'in_progress'])->get();
        $processed = 0;

        foreach ($requests as $request) {
            // Check if the request has expired (total time limit)
            if ($request->hasExpired()) {
                $this->handleExpiredRequest($request);
                $processed++;
                continue;
            }

            // Check if the current level has exceeded its time limit
            if ($request->currentLevelHasExceededTimeLimit()) {
                $this->handleLevelTimeout($request);
                $processed++;
            }
        }

        $this->info("Processed {$processed} requests with timeouts.");
        return 0;
    }

    /**
     * Handle a request that has exceeded its total time limit.
     *
     * @param  \App\Models\Request  $request
     * @return void
     */
    private function handleExpiredRequest($request)
    {
        $this->info("Request #{$request->id} has expired (total time limit).");

        // You can implement different strategies here based on your business rules
        // For example, you might want to auto-approve, auto-reject, or escalate to a higher authority

        // For now, we'll just mark it as expired in the comments and escalate to an admin
        $request->actions()->create([
            'approval_level_id' => $request->getCurrentLevel()->id,
            'user_id' => 1, // Assuming user ID 1 is an admin or system user
            'action' => 'escalate',
            'comments' => 'Request automatically escalated due to exceeding total time limit.',
            'action_at' => Carbon::now(),
        ]);

        // Notify administrators
        // In a real application, you would get admin users and notify them
        // For example: User::role('admin')->get()->each->notify(new ApprovalEscalationNotification($request, 'total'));
    }

    /**
     * Handle a request where the current level has exceeded its time limit.
     *
     * @param  \App\Models\Request  $request
     * @return void
     */
    private function handleLevelTimeout($request)
    {
        $currentLevel = $request->getCurrentLevel();
        $this->info("Request #{$request->id} has exceeded time limit for level {$currentLevel->level_number}.");

        // Check if there's an escalation path defined
        if ($currentLevel->escalation) {
            // Get the current approver's supervisor (next in escalation path)
            $nextApprover = $currentLevel->escalation->getNextApprover();

            if ($nextApprover) {
                // Record the escalation action
                $request->actions()->create([
                    'approval_level_id' => $currentLevel->id,
                    'user_id' => 1, // System user
                    'action' => 'escalate',
                    'comments' => "Automatically escalated to {$nextApprover->name} due to time limit exceeded.",
                    'action_at' => Carbon::now(),
                ]);

                // Notify the next approver
                // $nextApprover->notify(new ApprovalEscalationNotification($request, 'level'));

                $this->info("Escalated to {$nextApprover->name}.");
            } else {
                $this->info("No next approver found in escalation path.");
            }
        } else {
            // No escalation path defined, you might want to implement a default behavior
            // For example, auto-approve or notify an administrator
            $this->info("No escalation path defined for level {$currentLevel->level_number}.");
        }

        // Optionally, you could implement auto-approval after a certain number of escalations
        // or if no escalation path is defined
    }
}