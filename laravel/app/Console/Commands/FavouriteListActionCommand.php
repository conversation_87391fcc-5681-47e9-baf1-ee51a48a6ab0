<?php

namespace App\Console\Commands;

use App\Services\Schedules\Weekly\FavouriteListActionService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class FavouriteListActionCommand extends Command
{

    public function __construct(private FavouriteListActionService $favouriteListActionService)
    {

        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:favourite-list-action-command
    { --grant : used to add creating favourite list }
    { --revoke : used to remove creating favourite list }
    {role_id : The ID of the role to add or remove}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'add or remove creating favourite list';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $roleId = $this->argument('role_id');
        if ($this->option('grant')) {
            $this->favouriteListActionService->grant($roleId);
            $this->info("added creating favourite list for role_id: $roleId");
            return 0;
        }

        if ($this->option('revoke')) {
            $this->favouriteListActionService->revoke($roleId);
            $this->info("revoked creating favourite list for role_id: $roleId");
            return 0;
        }

        return 0;
    }
}
