<?php

namespace App\Console\Commands;

use App\Services\Schedules\Weekly\PlanActionService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class PlanActionCommand extends Command
{

    public function __construct(private PlanActionService $planActionService)
    {

        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:plan-action-command
    { --grant : used to grant creating plans }
    { --revoke : used to grant creating plans }
    {role_id : The ID of the role to grant or revoke}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'grant or revoke creating plans';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $roleId = $this->argument('role_id');
        if ($this->option('grant')) {
            $this->planActionService->grant($roleId);
            $this->info("granted creating plans for role_id: $roleId");
            return 0;
        }

        if ($this->option('revoke')) {
            $this->planActionService->revoke($roleId);
            $this->info("revoked creating plans for role_id: $roleId");
            return 0;
        }

        return 0;
    }
}
