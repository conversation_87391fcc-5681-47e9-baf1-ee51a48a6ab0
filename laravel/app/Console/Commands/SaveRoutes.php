<?php

namespace App\Console\Commands;

use App\Helpers\Json;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Route;

class SaveRoutes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'save:routes { --where= : file path in which routes will be saved .}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'to save routes.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $filePath=$this->option('where') ?: '../coreui/src/api/ApiRoutes.json';
        if (!is_file($filePath)) {
            $this->call('make:file',[
                'where'=>$filePath,
            ]);
        }
        (new Json(
            $filePath
        ))
            ->override(
                collect(Route::getRoutes()->getRoutes())->map(function($route){
                    return [
                        'name' => $route->getName(),
                        'domain' => $route->getDomain(),
                        'action' => $route->getActionName(),
                        'uri' => $route->uri(),
                        'method' => $route->methods()
                    ];

                })->keyBy('name')
            )->save();

        $this->info('routes are saved successfully.');
        return 0;
    }
}
