<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Sale;
use App\SaleDetail;
use App\Services\Enums\Ceiling;
use Carbon\Carbon;

class RollbackDistribution extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'distribution:rollback
                            {--from-date= : Start date for rollback (YYYY-MM-DD)}
                            {--to-date= : End date for rollback (YYYY-MM-DD)}
                            {--distribution-type= : Distribution type (1=Private Pharmacy, 2=Store, 3=Local Chain)}
                            {--product-ids= : Comma-separated product IDs}
                            {--distributor-ids= : Comma-separated distributor IDs}
                            {--dry-run : Preview changes without executing}
                            {--backup : Create backup before rollback}
                            {--force : Skip confirmation prompts}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rollback distribution operations by removing distributed sales and restoring original sales ceiling status';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            $this->info('=== DISTRIBUTION ROLLBACK COMMAND ===');

            // Validate options
            if (!$this->validateOptions()) {
                return 1;
            }

            // Analyze what will be rolled back
            $analysis = $this->analyzeDistribution();
            $this->displayAnalysis($analysis);

            if ($analysis['distributed_sales_count'] === 0) {
                $this->info('No distributed sales found for the specified criteria.');
                return 0;
            }

            // Confirm proceed
            if (!$this->confirmProceed($analysis)) {
                $this->info('Rollback cancelled.');
                return 0;
            }

            // Create backup if requested
            if ($this->option('backup') && !$this->option('dry-run')) {
                $this->createBackupTables();
            }

            // Execute rollback
            if (!$this->option('dry-run')) {
                $this->executeRollback($analysis);
                $this->info('✅ Rollback completed successfully!');
            } else {
                $this->info('🔍 DRY RUN: No changes were made to the database.');
            }

            return 0;

        } catch (\Exception $e) {
            $this->error('❌ ERROR: ' . $e->getMessage());
            Log::error('Distribution rollback command failed', [
                'error' => $e->getMessage(),
                'options' => $this->options()
            ]);
            return 1;
        }
    }

    /**
     * Validate command options
     */
    private function validateOptions(): bool
    {
        $fromDate = $this->option('from-date');
        $toDate = $this->option('to-date');

        if (!$fromDate || !$toDate) {
            $this->error('Both --from-date and --to-date are required');
            return false;
        }

        try {
            $from = Carbon::createFromFormat('Y-m-d', $fromDate);
            $to = Carbon::createFromFormat('Y-m-d', $toDate);

            if ($from->gt($to)) {
                $this->error('from-date cannot be after to-date');
                return false;
            }
        } catch (\Exception $e) {
            $this->error('Invalid date format. Use YYYY-MM-DD');
            return false;
        }

        $distributionType = $this->option('distribution-type');
        if ($distributionType && !in_array((int)$distributionType, [1, 2, 3])) {
            $this->error('distribution-type must be 1, 2, or 3');
            return false;
        }

        return true;
    }

    /**
     * Analyze what will be affected by the rollback
     */
    private function analyzeDistribution(): array
    {
        $query = $this->buildDistributedSalesQuery();
        $distributedSales = $query->get();

        // Get original sales that will be restored
        $originalSalesCount = 0;
        $allOriginalSaleIds = [];

        if ($distributedSales->isNotEmpty()) {
            foreach ($distributedSales as $distributedSale) {
                if ($distributedSale->sale_ids) {
                    $originalSaleIds = explode(',', $distributedSale->sale_ids);
                    $allOriginalSaleIds = array_merge($allOriginalSaleIds, $originalSaleIds);
                }
            }
            $allOriginalSaleIds = array_unique($allOriginalSaleIds);
            $originalSalesCount = count($allOriginalSaleIds);
        }

        $analysis = [
            'distributed_sales_count' => $distributedSales->count(),
            'distributed_sales' => $distributedSales,
            'original_sales_count' => $originalSalesCount,
            'original_sale_ids' => $allOriginalSaleIds,
            'affected_products' => $distributedSales->pluck('product_id')->unique()->count(),
            'affected_distributors' => $distributedSales->pluck('distributor_id')->unique()->count(),
            'total_distributed_value' => $distributedSales->sum('value'),
            'date_range' => [
                'from' => $this->option('from-date'),
                'to' => $this->option('to-date')
            ]
        ];

        // Get sales details count
        if ($distributedSales->isNotEmpty()) {
            $saleIds = $distributedSales->pluck('id');
            $analysis['sales_details_count'] = SaleDetail::whereIn('sale_id', $saleIds)->count();
        } else {
            $analysis['sales_details_count'] = 0;
        }

        return $analysis;
    }

    /**
     * Build query for distributed sales based on options
     */
    private function buildDistributedSalesQuery()
    {
        $query = Sale::where('ceiling', Ceiling::DISTRIBUTED->value)
            ->whereNotNull('sale_ids')
            ->whereBetween('date', [$this->option('from-date'), $this->option('to-date')]);

        if ($productIds = $this->option('product-ids')) {
            $productIds = array_map('intval', explode(',', $productIds));
            $query->whereIn('product_id', $productIds);
        }

        if ($distributorIds = $this->option('distributor-ids')) {
            $distributorIds = array_map('intval', explode(',', $distributorIds));
            $query->whereIn('distributor_id', $distributorIds);
        }

        return $query;
    }

    /**
     * Display analysis results
     */
    private function displayAnalysis(array $analysis): void
    {
        $this->newLine();
        $this->info('=== ROLLBACK ANALYSIS ===');

        $headers = ['Metric', 'Value'];
        $rows = [
            ['Date Range', "{$analysis['date_range']['from']} to {$analysis['date_range']['to']}"],
            ['Distributed Sales to Remove', $analysis['distributed_sales_count']],
            ['Sales Details to Remove', $analysis['sales_details_count']],
            ['Original Sales to Restore', $analysis['original_sales_count']],
            ['Affected Products', $analysis['affected_products']],
            ['Affected Distributors', $analysis['affected_distributors']],
            ['Total Distributed Value', number_format($analysis['total_distributed_value'], 2)],
        ];

        $this->table($headers, $rows);

        if ($analysis['distributed_sales_count'] > 0) {
            $this->newLine();
            $this->info('=== SAMPLE DISTRIBUTED SALES ===');

            $sampleHeaders = ['ID', 'Product ID', 'Distributor ID', 'Date', 'Quantity', 'Value'];
            $sampleRows = [];

            $sample = $analysis['distributed_sales']->take(5);
            foreach ($sample as $sale) {
                $sampleRows[] = [
                    $sale->id,
                    $sale->product_id,
                    $sale->distributor_id ?? 'N/A',
                    $sale->date,
                    $sale->quantity,
                    number_format($sale->value, 2)
                ];
            }

            $this->table($sampleHeaders, $sampleRows);

            if ($analysis['distributed_sales_count'] > 5) {
                $this->info("... and " . ($analysis['distributed_sales_count'] - 5) . " more sales");
            }
        }
    }

    /**
     * Confirm with user before proceeding
     */
    private function confirmProceed(array $analysis): bool
    {
        if ($this->option('force') || $this->option('dry-run')) {
            return true;
        }

        $this->newLine();
        $this->warn('=== WARNING ===');
        $this->warn("This will permanently delete {$analysis['distributed_sales_count']} distributed sales");
        $this->warn("and {$analysis['sales_details_count']} related sales details.");
        $this->warn("It will restore {$analysis['original_sales_count']} original sales by changing their ceiling status from 'ABOVE' to 'BELOW'.");

        if (!$this->option('backup')) {
            $this->newLine();
            $this->comment('NOTE: No backup will be created. Consider using --backup option.');
        }

        return $this->confirm('Do you want to proceed?');
    }

    /**
     * Create backup tables
     */
    private function createBackupTables(): void
    {
        $timestamp = date('Y_m_d_H_i_s');

        $this->info('Creating backup tables...');

        // Backup distributed sales (that will be deleted)
        DB::statement("CREATE TABLE sales_backup_{$timestamp} AS SELECT * FROM sales WHERE ceiling = ? AND sale_ids IS NOT NULL AND date BETWEEN ? AND ?",
            [Ceiling::DISTRIBUTED->value, $this->option('from-date'), $this->option('to-date')]);

        // Backup sales_details (that will be deleted)
        DB::statement("CREATE TABLE sales_details_backup_{$timestamp} AS SELECT sd.* FROM sales_details sd
            INNER JOIN sales s ON sd.sale_id = s.id
            WHERE s.ceiling = ? AND s.sale_ids IS NOT NULL AND s.date BETWEEN ? AND ?",
            [Ceiling::DISTRIBUTED->value, $this->option('from-date'), $this->option('to-date')]);

        // Backup original sales (that will be restored)
        DB::statement("CREATE TABLE original_sales_backup_{$timestamp} AS SELECT * FROM sales WHERE ceiling = ? AND date BETWEEN ? AND ?",
            [Ceiling::ABOVE->value, $this->option('from-date'), $this->option('to-date')]);

        $this->info("✅ Backup tables created: sales_backup_{$timestamp}, sales_details_backup_{$timestamp}, original_sales_backup_{$timestamp}");
    }

    /**
     * Execute the actual rollback
     */
    private function executeRollback(array $analysis): void
    {
        $this->info('Executing rollback...');

        $progressBar = $this->output->createProgressBar(3);
        $progressBar->start();

        DB::transaction(function () use ($analysis, $progressBar) {
            $distributedSaleIds = $analysis['distributed_sales']->pluck('id');

            // Step 1: Delete sales details for distributed sales
            $deletedDetails = SaleDetail::whereIn('sale_id', $distributedSaleIds)->delete();
            $this->newLine();
            $this->info("✅ Deleted {$deletedDetails} sales details");
            $progressBar->advance();

            // Step 2: Delete distributed sales
            $deletedSales = Sale::whereIn('id', $distributedSaleIds)->delete();
            $this->info("✅ Deleted {$deletedSales} distributed sales");
            $progressBar->advance();

            // Step 3: Restore original sales using sale_ids from distributed sales
            $updated = $this->restoreOriginalSales($analysis['original_sale_ids']);
            $this->info("✅ Restored {$updated} original sales (ceiling: ABOVE → BELOW)");
            $progressBar->advance();
        });

        $progressBar->finish();
        $this->newLine(2);

        Log::info('Distribution rollback completed via Artisan command', [
            'deleted_sales' => $analysis['distributed_sales_count'],
            'deleted_details' => $analysis['sales_details_count'],
            'restored_sales' => $analysis['original_sales_count'],
            'options' => $this->options()
        ]);
    }

    /**
     * Restore original sales by changing their ceiling status from ABOVE to BELOW
     */
    private function restoreOriginalSales(array $originalSaleIds): int
    {
        if (empty($originalSaleIds)) {
            return 0;
        }

        return Sale::whereIn('id', $originalSaleIds)
            ->where('ceiling', Ceiling::ABOVE->value)
            ->update(['ceiling' => Ceiling::BELOW->value]);
    }
}
