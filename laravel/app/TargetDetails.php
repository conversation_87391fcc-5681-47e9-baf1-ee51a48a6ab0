<?php

namespace App;

use App\Traits\ModelImportable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TargetDetails extends Model
{
    use SoftDeletes;
    use ModelImportable;

    protected $guard_name = 'api';

    protected $table = 'target_details';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'div_id',
        'line_id',
        'brick_id',
        'product_id',
        'date',
        'target',
        'value',
        'type_id',
        'file_id',
        'target_id'
    ];


    protected $casts = [
        'date' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function price($from = null, $to = null)
    {
        $value = Productprice::price($this->product, null, $from, $to);
        return $value * $this->target;
    }
}
