<?php


namespace App\Traits;

use App\Action;
use App\Exceptions\CrmException;
use App\Form;
use App\Helpers\LogActivity;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Permission;

trait ModelExportable
{
    public static function export(string $writerType)
    {
        // throw new CrmException(self::class,1);
        $model = Str::plural(self::class);

        $Export_class = "";

        // to check if we are using coaching models or not
        if (Str::is("App\\Models\\*", $model)) {
            $Export_class = Str::replaceFirst('App\Models', 'App\Exports', $model . 'Export');
        } else {

            $Export_class = Str::replaceFirst('App', 'App\Exports', $model . 'Export');
        }

        $model_name = Str::lower(collect(explode("\\", $model))->last());
        $writerTypeLower = Str::lower($writerType);
        LogActivity::addLog();
        $response = Excel::download(new $Export_class, Str::plural($model_name) . '.' . $writerTypeLower, $writerType);
        ob_end_clean();
        return $response;
    }
}
