<?php

namespace App\Traits;

use App\Models\ApprovalFlow;
use App\Models\Request;
use Illuminate\Database\Eloquent\Relations\MorphOne;

trait ApprovableRequest
{
    /**
     * Boot the trait.
     *
     * @return void
     */
    public static function bootApprovableRequest()
    {
        static::deleting(function ($model) {
            // Delete associated approval request when model is deleted
            $model->approvalRequest()->delete();
        });
    }

    /**
     * Get the approval request for this model.
     */
    public function approvalRequest(): MorphOne
    {
        return $this->morphOne(Request::class, 'requestable', 'request_type', 'request_id');
    }

    /**
     * Submit this model for approval.
     *
     * @param int $approvalFlowId
     * @param int $userId
     * @param array $data
     * @return Request|null
     */
    public function submitForApproval(int $approvalFlowId, int $userId, array $data = []): ?Request
    {
        // Check if approval flow exists
        $approvalFlow = ApprovalFlow::find($approvalFlowId);
        if (!$approvalFlow) {
            return null;
        }

        // Create a new request
        $request = new Request([
            'approval_flow_id' => $approvalFlowId,
            'user_id' => $userId,
            'data' => $data,
            'status' => 'submitted',
            'submitted_at' => now(),
        ]);

        // Set expiration date if total time limit is defined
        if ($approvalFlow->hasTotalTimeLimit()) {
            $request->expires_at = now()->addSeconds($approvalFlow->getTotalTimeLimitInSeconds());
        }

        $this->approvalRequest()->save($request);

        return $request;
    }

    /**
     * Check if this model is pending approval.
     *
     * @return bool
     */
    public function isPendingApproval(): bool
    {
        $request = $this->approvalRequest;
        return $request && ($request->status === 'submitted' || $request->status === 'in_progress');
    }

    /**
     * Check if this model is approved.
     *
     * @return bool
     */
    public function isApproved(): bool
    {
        $request = $this->approvalRequest;
        return $request && $request->status === 'approved';
    }

    /**
     * Check if this model is rejected.
     *
     * @return bool
     */
    public function isRejected(): bool
    {
        $request = $this->approvalRequest;
        return $request && $request->status === 'rejected';
    }

    /**
     * Get the current approval level for this request.
     *
     * @return \App\Models\ApprovalLevel|null
     */
    public function getCurrentApprovalLevel(): ?\App\Models\ApprovalLevel
    {
        $request = $this->approvalRequest;
        if (!$request || !in_array($request->status, ['submitted', 'in_progress'])) {
            return null;
        }

        return $request->getCurrentLevel();
    }

    /**
     * Get the approval timeline for this request.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getApprovalTimeline(): \Illuminate\Support\Collection
    {
        $request = $this->approvalRequest;
        if (!$request) {
            return collect([]);
        }

        return $request->actions()->with(['user', 'approvalLevel'])->orderBy('created_at')->get();
    }

    /**
     * Get the time remaining for the current approval level.
     *
     * @return int|null Seconds remaining or null if no time limit
     */
    public function getTimeRemainingForCurrentLevel(): ?int
    {
        $request = $this->approvalRequest;
        $currentLevel = $this->getCurrentApprovalLevel();

        if (!$request || !$currentLevel || !$currentLevel->time_limit) {
            return null;
        }

        $lastAction = $request->actions()
            ->where('approval_level_id', '!=', $currentLevel->id)
            ->latest()
            ->first();

        $startTime = $lastAction ? $lastAction->created_at : $request->submitted_at;
        $elapsedSeconds = now()->diffInSeconds($startTime);
        $totalSeconds = $currentLevel->time_limit * 3600; // Convert hours to seconds

        $remaining = $totalSeconds - $elapsedSeconds;
        return $remaining > 0 ? $remaining : 0;
    }

    /**
     * Get the time remaining for the entire approval process.
     *
     * @return int|null Seconds remaining or null if no time limit
     */
    public function getTotalTimeRemaining(): ?int
    {
        $request = $this->approvalRequest;

        if (!$request || !$request->expires_at) {
            return null;
        }

        $remaining = now()->diffInSeconds($request->expires_at, false);
        return $remaining > 0 ? $remaining : 0;
    }
}