<?php


namespace App\Traits;

use App\Action;
use App\Exceptions\CrmException;
use App\Form;
use App\Helpers\LogActivity;
use App\Permission;
use App\Setting;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use M<PERSON>rlosen\LaravelMpdf\Facades\LaravelMpdf as PDF;

trait SendMail
{
    public static function sendMail(FormRequest $request, Collection $exportedData)
    {

        $name = Str::lower(Str::afterLast(Str::plural(self::class), '\\'));
        // throw new CrmException($data,1);
        $header = Setting::where('key', 'pdf_header')->pluck('value')->first();
        $image = Setting::where('key', 'pdf_logo')->pluck('value')->first();
        $footer = Setting::where('key', 'pdf_footer')->pluck('value')->first();
        $subject = Setting::where('key', 'mail.title.prefix')->pluck('value')->first();
        $from = Setting::where('key', 'mail.from.address')->pluck('value')->first();
        $user_id = Auth::user()->id;
        $user_name = Auth::user()->name;
        $exported_date = Carbon::now();
        $emailobjs = json_decode($request->emails);
        // throw new CrmException($emailobjs,1);
        foreach ($emailobjs as $emailobj) {
            $data["email"] = $emailobj->text;
            $data["subject"] = $subject;
            $data["from"] = $from;
            $data["text"] = $request->text;
            $pdf = PDF::loadView($name . '.' . 'print', [
                "user_id" => $user_id,
                "user_name" => $user_name,
                "exported_date" => $exported_date,
                "footer" => $footer,
                "image" => $image,
                "header" => $header,
                $name => $exportedData
            ]);
            Mail::send($name . '.' . 'test', $data, function ($message) use ($data, $pdf, $name) {
                $message->from($data["from"])
                    ->to($data["email"])
                    ->subject($data["subject"])
                    ->attachData($pdf->output(), $name . '.' . 'pdf');
            });
        }
        LogActivity::addLog();
        return response()->json(['status' => 'success']);
    }
}
