<?php


namespace App\Traits;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

trait CreatedUpdatedTime
{

    public static function boot()
    {
        parent::boot();

        static::creating(function ($item) {
            $item->created_at = Carbon::now(config('timezone'));
            Log::info($item);
        });

        static::updating(function ($item) {
            $item->updated_at = Carbon::now(config('timezone'));
        });
    }
}
