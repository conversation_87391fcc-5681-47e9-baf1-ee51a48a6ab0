<?php
namespace App\Traits\Alerts;


use App\Exports\RevokedPolicyUsersExport;
use App\Services\Enums\AlertAttachmentExtention;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Excel as BaseExcel;
use Maatwebsite\Excel\Facades\Excel;

trait AlertAttachable {

    private function saveAttachment(RevokedPolicyUsersExport $export, AlertAttachmentExtention $fileType, string $report_name, string $date): string
    {
        [$fileName, $extension] = $this->format($fileType, $report_name, $date);

        $path = 'public/alerts/Alert_' . $fileName;

        Excel::store($export, $path, 'local', $extension);

        return Storage::url($path);
    }

    function format(AlertAttachmentExtention $fileType, string $report_name, string $date): array
    {
        return match ($fileType) {
            AlertAttachmentExtention::EXCEL => [
                $report_name .'_'. $date . '.xlsx',
                BaseExcel::XLSX
            ],
            AlertAttachmentExtention::CSV => [
                $report_name .'_'. $date . '.csv',
                BaseExcel::CSV
            ],
            AlertAttachmentExtention::PDF => [
                $report_name .'_'. $date . '.pdf',
                BaseExcel::MPDF
            ],
        };
    }
}
