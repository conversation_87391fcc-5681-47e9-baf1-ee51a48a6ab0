<?php

namespace App\Traits;

use App\Helpers\CrmExcelDate;
use App\Helpers\SimpleDate;
use Illuminate\Support\Carbon;

trait ModelAvailability
{

    public function isAvailable()
    {
        $fromDate = Carbon::createFromFormat(CrmExcelDate::OFFICIAL_FORMAT, $this->from_date);

        if (!$this->to_date) {
            return $fromDate->lte(now());
        }

        $toDate = Carbon::createFromFormat(CrmExcelDate::OFFICIAL_FORMAT, $this->to_date);

        return $fromDate->lte(now()) && $toDate->gte(now());
    }

    /**
     * @param Carbon $first
     * @param Carbon $last
     * @description the default is using first date of month and last date of month
     */
    public static function betweenTwoDates(Carbon $firstDate, Carbon $lastDate,$column='date')
    {
        return static::orderBy($column)->whereBetween($column, [$firstDate->toDateString(), $lastDate->toDateString()]);
    }

    /**
     * @param string $column default = 'date'
     * @param int $day default = null
     * @description the default is using first date of day and last date of day
     */
    public static function inDay($column='date',$day=null)
    {
        [$first, $last] = SimpleDate::firstAndLastOFDay($day);
        return static::betweenTwoDates($first, $last,$column);
    }
    /**
     * @param string $column default = 'date'
     * @param int $month default = null
     * @description the default is using first date of month and last date of month
     */
    public static function inMonth($column='date',$month = null)
    {
        [$first, $last] = SimpleDate::firstAndLastOFMonth($month);
        return static::betweenTwoDates($first, $last,$column);
    }
    /**
     * @param string $column default = 'date'
     * @param int $year default = null
     * @description the default is using first date of year and last date of year
     */
    public static function inYear($column='date',$year = null)
    {
        [$first, $last] = SimpleDate::firstAndLastOFYear($year);
        return static::betweenTwoDates($first, $last,$column);
    }

    public static function yesterday($column='date')
    {
        return static::inDay($column,now()->day -1);
    }
}
