<?php

namespace App\Traits;


use App\Action;
use App\Exceptions\CrmException;
use App\Exceptions\ExcelValidationException;
use App\Form;
use App\Helpers\LogActivity;
use App\Import;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Str;

trait ModelNormalImportable
{
    public static function import(FormRequest $request, $onlyValidate = true, $onlyImport = true, $array = [])
    {
        // class name of model with namespace
        $model = Str::plural(self::class);

        // getting import class of the model
        if (Str::is("App\\Models\\*", $model)) {
            $import_class = Str::replaceFirst('App\Models', 'App\Imports', $model . 'Import');
        } else {
            $import_class = Str::replaceFirst('App', 'App\Imports', $model . 'Import');
        }

        // throw new CrmException($import_class,1);

        $model_name = Str::lower(collect(explode("\\", $model))->last());

        $name = $request->file('file')->getClientOriginalName();
        $path = $request->file('file')->storeAs($model_name, str_replace(' ', '_', date("d-m-Y H-i-s") . '_' . rand(00000, 99999) . '_' . $name));



        return DB::transaction(function () use ($model_name, $import_class, $name, $path, $onlyValidate, $onlyImport, $array) {
            $permission = Permission::firstOrCreate(array('name' => 'import_' . $model_name));
            $permission_id = $permission->id;
            $import = new Import();
            $import->name = $name;
            $import->path = $path;
            $import->user_id = Auth::user()->id;
            $import->permission_id = $permission_id;
            $import->save();

            $model_id = $import->id;
            $model_type = Import::class;
            $modelImport = new $import_class($model_id, self::class, $onlyValidate, $onlyImport,$array);
            $modelImport->import(storage_path("app/".$path));

            LogActivity::addLog($model_id, $model_type);
            if ($modelImport->failures()->isNotEmpty()) {
                throw new ExcelValidationException('validation errors has recorded', 422, $modelImport->failures());
            }
            return $modelImport->readableData();
        });
    }
}
