<?php

namespace App\Traits;

use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use Illuminate\Support\Facades\DB;

trait ForceDeleteData
{
    public function forceDeleted($id)
    {
        $model = DB::table('log_activities')->select('log_activities.id', 'log_activities.permission_id', 'log_activities.form_id', 'log_activities.action_id', 'log_activities.model_id', 'log_activities.model_type')
            ->where('log_activities.id', '=', $id)
            ->first();
        $trashedModel = $model->model_type;
        $trashedID = $model->model_id;
        $trashedData = resolve($trashedModel)->withTrashed()->find($trashedID);
        $trashedData->forceDelete();
        LogActivity::addLog($trashedID, $trashedModel);
        return response()->json(['status' => 'success']);
    }

    public function forceDelete(){
        $this->withTrashed()->where('id', $this->id)->forceDelete();
    }
}
