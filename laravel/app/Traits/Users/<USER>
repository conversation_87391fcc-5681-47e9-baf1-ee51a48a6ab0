<?php


namespace App\Traits\Users;

use App\DivisionType;
use App\Exceptions\CrmException;
use App\Line;
use App\LineDivision;
use App\LineDivisionUser;
use App\LineUser;
use App\Models\LineDivisionProduct;
use App\Position;
use App\PositionManager;
use App\User;
use App\UserPosition;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

trait UserData
{
    public function userLines($from = null, $to = null, $withProduct = false, $withBrands = false)
    {
        $isAdmin = $this->hasRole('admin');
        $isSubAdmin = $this->hasRole('sub admin');
        $isGemstoneAdmin = $this->hasRole('Gemstone Admin');
        $with = [];
        if ($isAdmin || $isSubAdmin || $isGemstoneAdmin) {
            $lines = Line::select('id', 'name')->orderBy('sort', 'ASC');
            // ->where('from_date', '<=', now())
            // ->where(fn ($q) => $q->where('to_date', '>', (string)Carbon::now())
            // ->orWhere('to_date', null));
        }

        if (!$isAdmin && !$isSubAdmin && !$isGemstoneAdmin) {
            $lines = $this->lines($from, $to);
        }

        if ($withProduct) {
            $with[] = 'products';
        }

        if ($withBrands) {
            $with[] = 'brands';
        }

        // Log::info(collect($with));


        $lines = $withProduct ? $lines->with($with)->get() : $lines->get();


        if ($this->hasPosition()) {
            $userPositionLines = $withProduct ?
                $this->userPosition->first()->lines()->with($with)->get()
                : $this->userPosition->first()->lines;
            $lines = $lines->merge($userPositionLines);
            if (count($lines) == 0) {
                $lines = Line::select('id', 'name')
                    ->where('lines.from_date', '<=',  $from ?? (string)Carbon::now())
                    ->where(fn($q) => $q->where('lines.to_date', '>', Carbon::parse($to)->toDateString() ?? (string)Carbon::now())
                        ->orWhere('lines.to_date', null));

                $lines = $withProduct ? $lines->get() : $lines->with($with)->get();
            }
        }

        return $lines;
    }

    public function userDivisions(Line $line, $from = null, $to = null)
    {
        $from = $from ? Carbon::parse($from)->startOfDay() : Carbon::now()->startOfDay();
        $to = $to ? Carbon::parse($to)->endOfDay() : Carbon::now()->endOfDay();
        $divisions = collect([]);
        if ($this->hasRole('admin') || $this->hasRole('sub admin') || $this->hasRole('Gemstone Admin')) {
            $divisions = LineDivision::where('line_id', $line->id)
                ->where(function ($query) use ($from, $to) {
                    $query->where(function ($subQuery) use ($from, $to) {
                        $subQuery->whereNull('line_divisions.to_date') // Active records
                            ->orWhereBetween('line_divisions.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                            ->orWhere('line_divisions.to_date', '>=', $to->toDateString()); // Ends within range
                    })
                        ->where(function ($subQuery) use ($from, $to) {
                            $subQuery->where('line_divisions.from_date', '<=', $from->toDateString()) // Starts before range
                                ->orWhereBetween('line_divisions.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                        });
                })
                // ->where('line_divisions.from_date', '<=', $from?->toDateString() ?? (string)Carbon::now())
                // ->where(
                //     fn($q) => $q->where('line_divisions.to_date', '>=', $to?->toDateString() ?? (string)Carbon::now())
                //         ->orWhere('line_divisions.to_date', null)
                // )
                ->orderBy('line_divisions.division_type_id', 'ASC')
                ->get();
        }
        if (!$this->hasRole('admin') && !$this->hasRole('sub admin') && !$this->hasRole('Gemstone Admin') && $this->hasDivision($line, $from, $to)) {
            $divisions = $divisions->merge($this->allBelowDivisions($line, $from, $to));
        }

        if (!$this->hasRole('admin') && !$this->hasRole('sub admin') && !$this->hasRole('Gemstone Admin') && $this->hasPosition()) {
            $userPosition = $this->userPosition->first();
            $userDivisions = $userPosition->divisions->where('line_id', $line->id);
            if (count($userDivisions) == 0) {
                $userDivisions = LineDivision::where('line_id', $line->id)->where('division_type_id', 1)->whereNull('deleted_at')->get();
            }
            $userDivisions->values()->each(function ($division) use (&$divisions, $from, $to) {
                $divisions = $divisions->merge($division?->getBelowDivisions($from, $to)->prepend($division));
            });
        }
        return $divisions->unique('id');
    }

    public function belowUsersWithPositions(Line $line, $request = [], $from = null, $to = null)
    {
        $users = collect([]);
        $isAdmin = $this->hasRole('admin') || $this->hasRole('sub admin') || $this->hasRole('Gemstone Admin');

        // Admin or Sub-Admin Case
        if ($isAdmin) {
            if (!empty($request)) {
                $countryManager = DivisionType::where('level', 1)
                    ->first()->divisions($from, $to)
                    ->where('line_id', $line->id)->first()?->users()->first();

                $users = $countryManager?->allBelowUsers($line, $from, $to)?->prepend($countryManager);
            } else {
                $users = $line->users;
            }
        }

        // User has Division
        if (!$isAdmin && $this->hasDivision($line, $from, $to)) {
            $positionReportTo = PositionManager::where('managerable_type', DivisionType::class)
                ->where('managerable_id', $this->divisionType($line)?->id)
                ->pluck('position_id');

            if ($positionReportTo->isNotEmpty()) {
                $userPosition = UserPosition::whereIntegerInRaw('position_id', $positionReportTo)
                    ->with('user')->get()->pluck('user');

                $users = $this->allBelowUsers($line, $from, $to)
                    ->prepend($this)
                    ->merge($userPosition);
            } else {
                $users = $this->allBelowUsers($line, $from, $to)->prepend($this);
            }
        }

        // User has Position but not Admin or Division
        if (!$isAdmin && $this->hasPosition()) {
            $userPosition = $this->userPosition->first();
            $divisions = $userPosition->divisions->where('line_id', $line->id);

            if ($divisions->isEmpty()) {
                $divisions = LineDivision::where('line_id', $line->id)
                    ->where('division_type_id', 1)
                    ->whereNull('deleted_at')
                    ->get();
            }

            $positionReportTo = PositionManager::where('managerable_type', Position::class)
                ->where('managerable_id', $this->position()?->id)
                ->pluck('position_id');

            $userPosition = UserPosition::whereIntegerInRaw('position_id', $positionReportTo)
                ->with('user')->get()->pluck('user');

            $divisions->each(function ($division) use (&$users, $line, $from, $to, $userPosition) {
                $users = $users->merge(
                    $division->user($from, $to)?->allBelowUsers($line, $from, $to)->prepend($this)
                )->merge($userPosition);
            });
        }

        return $users->unique('id')->values();
    }

    public function userProducts(Line $line)
    {
        $products = collect([]);
        if ($this->hasRole('admin') || $this->hasRole('sub admin') || !$this->hasRole('Gemstone Admin') || $this->hasDivision($line)) {
            $products = $line->products()
                ->orderBy('products.sort', 'ASC')
                ->get();
        }

        if (!$this->hasRole('admin') && !$this->hasRole('sub admin') && !$this->hasRole('Gemstone Admin') && $this->hasPosition()) {
            $userPosition = $this->userPosition->first();
            $products = $userPosition->products()->whereHas('lines', function ($q) use ($line) {
                $q->where('line_id', $line->id);
            })->get();
            if (count($products) == 0) {
                $products = $line->products()
                    ->orderBy('products.sort', 'ASC')
                    ->get();
            }
        }
        return $products->unique('id');
    }

    public function userDivisionProducts(Line $line, ?array $divisions = [])
    {
        $products = collect([]);
        if ($this->hasRole('admin') || $this->hasRole('sub admin') || $this->hasRole('Gemstone Admin') || $this->hasDivision($line)) {
            $products = LineDivisionProduct::where('line_id', $line->id)
                ->where('line_division_products.from_date', '<=', (string)Carbon::now())
                ->where(fn($q) => $q->where('line_division_products.to_date', '=', null)
                    ->orWhere('line_division_products.to_date', '>=', (string)Carbon::now()));
            if (!empty($divisions)) {
                $products = $products->whereIntegerInRaw('line_division_id', $divisions);
            }
            $products = $products->with('product')->get()->pluck('product');
        }
        if (!$this->hasRole('admin') && !$this->hasRole('sub admin') && !$this->hasRole('Gemstone Admin') && $this->hasPosition()) {
            $userPosition = $this->userPosition->first();
            $products = $userPosition->products()->whereHas('lines', function ($q) use ($line) {
                $q->where('line_id', $line->id);
            })->get();
            if (count($products) == 0) {
                $products = $line->products()
                    ->orderBy('products.sort', 'ASC')
                    ->get();
            }
        }
        return $products->unique('id');
    }

    /** description: this function get users of all selected lines */
    public function belowUsersOfAllLinesWithPositions($lines, $status = 'Active', $from = null, $to = null)
    {
        // throw new CrmException($lines);
        $users = collect([]);
        foreach ($lines as $line) {
            if ($this->hasRole('admin') || $this->hasRole('sub admin') || $this->hasRole('Gemstone Admin')) {
                $data = $status == 'Active' ? $line->users($from, $to)->get() : $line->inactiveUsers;
                $users = $users->merge($data);
            }
            if (!$this->hasRole('admin') && !$this->hasRole('sub admin') && !$this->hasRole('Gemstone Admin') && $this->hasDivision($line, $from, $to)) {
                $positionReportTo = PositionManager::where('managerable_type', DivisionType::class)
                    ->where('managerable_id', $this->divisionType($line)?->id)->get()->pluck('position_id');
                $userPosition = UserPosition::whereIntegerInRaw('position_id', $positionReportTo)->with('user')->get()->pluck('user');
                $users = $users->merge($this->allBelowUsers($line, $from, $to)->prepend($this))->merge($userPosition);
            }

            if (!$this->hasRole('admin') && !$this->hasRole('sub admin') && !$this->hasRole('Gemstone Admin') && $this->hasPosition()) {
                $userPosition = $this->userPosition->first();
                $divisions = $userPosition->divisions->where('line_id', $line->id);
                if (count($divisions) == 0) {
                    $divisions = LineDivision::where('line_id', $line->id)->where('division_type_id', 1)->whereNull('deleted_at')->get();
                }
                $positionReportTo = PositionManager::where('managerable_type', Position::class)
                    ->where('managerable_id', $this->position()?->id)->get()->pluck('position_id');
                $userPosition = UserPosition::whereIntegerInRaw('position_id', $positionReportTo)->with('user')->get()->pluck('user');
                $divisions->each(function ($division) use (&$users, $line, $from, $to) {
                    $users = $users->merge($division->user($from, $to)?->allBelowUsers($line, $from, $to)->prepend($this));
                });
                $users = $users->merge($userPosition);
            }
        }
        return $users->unique('id')->values();
    }


    public function userApprovals($from = null, $to = null)
    {
        $lines = $this->lines($from, $to)->get();
        $linesCollectors = collect([]);
        $linesAapprovables = collect([]);
        if ($this?->hasPosition() && !$this?->hasDivisionOfAllLines()) {
            $linesCollectors = $linesCollectors->merge($this->positions($from, $to)->get());
            $linesAapprovables = $linesAapprovables->push($linesCollectors->map(fn($collector) => $collector->approvable->first()?->id));
        } else {
            $lines->each(function ($line) use ($linesCollectors, $linesAapprovables, $from, $to) {
                $linesCollectors = $linesCollectors->push($this->divisionType($line, $from, $to));
                $linesAapprovables = $linesAapprovables->push($linesCollectors->map(fn($collector) => $collector?->lineApprovables($line->id)?->first()?->id));
            });
        }
        return array(
            'lines' => $lines,
            'linesAapprovables' => $linesAapprovables,
        );
    }
}
