<?php


namespace App\Traits\Users;

use App\Exceptions\CrmException;
use App\Line;
use App\LineDivision;
use App\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

trait   UserLineStructureDivision
{

    public function allDivisions($selected = [
        "users.id as user_id",
        "users.name",
        "lines.id as line_id",
        "line_divisions.id as line_div",
    ], $from = null, $to = null)
    {
        $from = $from ? Carbon::parse($from)->startOfDay() : Carbon::now()->startOfDay();
        $to = $to ? Carbon::parse($to)->endOfDay() : Carbon::now()->endOfDay();
        return User::select(...$selected)
            ->leftJoin("line_users_divisions", "users.id", "line_users_divisions.user_id")
            ->leftJoin("line_divisions", "line_users_divisions.line_division_id", "line_divisions.id")
            ->leftJoin("line_users", "users.id", "line_users.user_id")
            ->leftJoin("lines", "line_users.line_id", "lines.id")
            // ->where('line_users_divisions.from_date', '<=', $from)
            // ->where(fn($q) => $q->where('line_users_divisions.to_date', '>=', $to)
            //     ->orWhere('line_users_divisions.to_date', null))
            // ->where(fn($q) => $q->where('lines.to_date', '>=', $to)
            //     ->orWhere('lines.to_date', null))
            // ->where(fn($q) => $q->where('line_divisions.to_date', '>=', $to)
            //     ->orWhere('line_divisions.to_date', null))
            // ->where(fn($q) => $q->where('line_users.to_date', '>=', $to)
            //     ->orWhere('line_users.to_date', null))


            ->where(function ($query) use ($from, $to) {
                $query->where(function ($subQuery) use ($from, $to) {
                    $subQuery->whereNull('line_users_divisions.to_date') // Active records
                        ->orWhereBetween('line_users_divisions.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                        ->orWhere('line_users_divisions.to_date', '>=', $to->toDateString()); // Ends within range
                })
                    ->where(function ($subQuery) use ($from, $to) {
                        $subQuery->where('line_users_divisions.from_date', '<=', $from->toDateString()) // Starts before range
                            ->orWhereBetween('line_users_divisions.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                    });
            })

            ->where(function ($query) use ($from, $to) {
                $query->where(function ($subQuery) use ($from, $to) {
                    $subQuery->whereNull('line_users.to_date') // Active records
                        ->orWhereBetween('line_users.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                        ->orWhere('line_users.to_date', '>=', $to->toDateString()); // Ends within range
                })
                    ->where(function ($subQuery) use ($from, $to) {
                        $subQuery->where('line_users.from_date', '<=', $from->toDateString()) // Starts before range
                            ->orWhereBetween('line_users.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                    });
            })

            ->where(function ($query) use ($from, $to) {
                $query->where(function ($subQuery) use ($from, $to) {
                    $subQuery->whereNull('line_divisions.to_date') // Active records
                        ->orWhereBetween('line_divisions.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                        ->orWhere('line_divisions.to_date', '>=', $to->toDateString()); // Ends within range
                })
                    ->where(function ($subQuery) use ($from, $to) {
                        $subQuery->where('line_divisions.from_date', '<=', $from->toDateString()) // Starts before range
                            ->orWhereBetween('line_divisions.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                    });
            })

            ->where(function ($query) use ($from, $to) {
                $query->where(function ($subQuery) use ($from, $to) {
                    $subQuery->whereNull('lines.to_date') // Active records
                        ->orWhereBetween('lines.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                        ->orWhere('lines.to_date', '>=', $to->toDateString()); // Ends within range
                })
                    ->where(function ($subQuery) use ($from, $to) {
                        $subQuery->where('lines.from_date', '<=', $from->toDateString()) // Starts before range
                            ->orWhereBetween('lines.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                    });
            })
            ->where("users.id", $this->id)
            ->whereNull('line_divisions.deleted_at')
            ->whereNull('line_users.deleted_at')
            ->whereNull('line_users_divisions.deleted_at');
        // ->orderBy('line_divisions.division_type_id','ASC');
    }


    public function allDivisionsOfAllLines($from = null, $to = null)
    {
        return $this->lines($from, $to)->get()->map(fn($line) => $this->allDivisionsOfLine($line, $from, $to));
    }

    public function allDivisionsOfLine(Line $line, $from = null, $to = null)
    {
        return $this->allDivisions(from: $from, to: $to)
            ->where("lines.id", $line->id)
            ->where("line_divisions.line_id", $line->id)
            ->get();
    }

    private function allAboveDivisionsOfLine(Line $line, $from = null, $to = null)
    {
        $divisions = collect([]);
        $this->allDivisionsOfLine($line, $from, $to)->map(function ($perDivision) use (&$divisions, $from, $to) {
            $divisions = $divisions->merge(LineDivision::find($perDivision->line_div)->getAboveDivisions($from, $to));
        });
        return $divisions;
    }

    private function allAboveDivisionsOfAllLines($from = null, $to = null)
    {
        $divisions = collect([]);
        $this->allDivisionsOfAllLines($from, $to)->map(function ($perLine) use (&$divisions, $from, $to) {
            return $perLine->map(function ($perDivision) use (&$divisions, $from, $to) {
                $divisions = $divisions->merge(LineDivision::find($perDivision->line_div)->getAboveDivisions($from, $to));
            });
        });
        return $divisions;
    }

    public function allAboveDivisions(?Line $line = null, $from = null, $to = null)
    {

        if ($line == null) return $this->allAboveDivisionsOfAllLines($from, $to)->unique('id');

        return $this->allAboveDivisionsOfLine($line, $from, $to)->unique('id');
    }


    private function allBelowDivisionsOfAllLines($from = null, $to = null)
    {
        // $divisions = collect([]);
        // $this->allDivisionsOfAllLines($from, $to)->map(function ($perLine) use (&$divisions, $from, $to) {
        //     return $perLine->map(function ($perDivision) use (&$divisions, $from, $to) {
        //         $divisions = $divisions->merge(LineDivision::find($perDivision->line_div)->getBelowDivisions($from, $to));
        //     });
        // });
        // return $divisions;
        $divisions = collect([]);

        $lineDivisionIds = $this->allDivisionsOfAllLines($from, $to)->collapse()->pluck('line_div');

        $lineDivisions = LineDivision::whereIn('id', $lineDivisionIds)->get();

        foreach ($lineDivisions as $lineDivision) {
            $divisions = $divisions->merge($lineDivision->getBelowDivisions($from, $to));
        }
        return $divisions;
    }

    private function allBelowDivisionsOfAllLinesPerDate($date = null)
    {
        // $divisions = collect([]);
        // $this->allDivisionsOfAllLines()->map(function ($perLine) use (&$divisions, $date) {
        //     return $perLine->map(function ($perDivision) use (&$divisions, $date) {
        //         $divisions = $divisions->merge(LineDivision::find($perDivision->line_div)->getBelowDivisionsPerPeriod($date));
        //     });
        // });
        // return $divisions;

        // Fetch all divisions in bulk
        $divisionIds = $this->allDivisionsOfAllLines()->pluck('line_div');
        $divisions = LineDivision::whereIn('id', $divisionIds)->get();

        $belowDivisions = collect([]);

        foreach ($divisions as $division) {
            $belowDivisions = $belowDivisions->merge($division->getBelowDivisionsPerPeriod($date));
        }

        return $belowDivisions;
    }

    private function allBelowDivisionsOfLine(Line $line, $from = null, $to = null)
    {
        // $divisions = collect([]);
        // $this->allDivisionsOfLine($line, $from, $to)->map(function ($perDivision) use (&$divisions, $from, $to) {
        //     $divisions = $divisions->merge(LineDivision::find($perDivision->line_div)->getBelowDivisions($from, $to));
        // });
        // return $divisions;
        // throw new CrmException($this->allDivisionsOfLine($line, $from, $to)->pluck('line_div'));
        $divisionIds = $this->allDivisionsOfLine($line, $from, $to)->pluck('line_div');
        $lineDivisions = LineDivision::whereIn('id', $divisionIds)->get();
        $divisions = collect([]);

        foreach ($lineDivisions as $division) {
            $divisions = $divisions->merge($division->getBelowDivisions($from, $to));
        }

        return $divisions;
    }

    private function allBelowDivisionsOfLinePerDate(Line $line, $date = null)
    {
        // $divisions = collect([]);
        // $this->allDivisionsOfLine($line)->map(function ($perDivision) use (&$divisions, $date) {
        //     $divisions = $divisions->merge(LineDivision::find($perDivision->line_div)->getBelowDivisionsPerPeriod($date));
        // });
        // return $divisions;

        $divisionIds = $this->allDivisionsOfLine($line)->pluck('line_div');
        $lineDivisions = LineDivision::whereIn('id', $divisionIds)->get();
        $divisions = collect([]);

        foreach ($lineDivisions as $division) {
            $divisions = $divisions->merge($division->getBelowDivisions($date));
        }

        return $divisions;
    }

    /**
     * @description get all below divisions from structure for one line or for all lines
     */
    public function allBelowDivisions(?Line $line = null, $from = null, $to = null)
    {
        if ($line == null) return $this->allBelowDivisionsOfAllLines($from, $to)->unique('id');

        return $this->allBelowDivisionsOfLine($line, $from, $to)->unique('id');
    }

    public function allBelowDivisionsPerDate(?Line $line = null, $date = null)
    {

        if ($line == null) return $this->allBelowDivisionsOfAllLinesPerDate($date)->unique('id');

        return $this->allBelowDivisionsOfLinePerDate($line, $date)->unique('id');
    }

    /**
     * @description get all below user from structure for one line or for all lines
     */
    public function allBelowUsers(?Line $line = null, $from = null, $to = null)
    {
        // throw new CrmException(['welcome',$from,$to]);

        return $this->allBelowDivisions($line, $from, $to)->where('is_kol', 0)
            ->map(fn($item) => $item->user($from, $to))
            ->filter(fn($item) => $item != null)->unique("id")->values();
    }

    /**
     * @description get all below user from structure for one line or for all lines
     */
    public function allBelowUsersWithDivision(?Line $line = null, $from = null, $to = null)
    {
        return $this->allBelowDivisions($line, $from, $to)->where('is_kol', 0)
            ->map(function ($item) {
                $factory = new \stdClass();
                $user = $item->user();
                $factory->div_id = $item->id;
                $factory->div_name = $item->name;
                $factory->user_id = $user?->id;
                $factory->div_type_id = $item->division_type_id;
                $factory->user_name = $user?->name;
                $factory->full_user_name = $user?->fullname;

                return [$factory];
            })->collapse()
            ->filter(fn($item) => $item->user_id != null)
            ->unique("user_id")->values();
    }


    /**
     * @description get all above user from structure for one line or for all lines
     */
    public function allAboveUsers(?Line $line = null, $from = null, $to = null)
    {
        return $this->allAboveDivisions($line, $from, $to)->map(fn($item) => $item->user())
            ->filter(fn($item) => $item != null)->unique("id")->values();
    }

    /**
     * @description get Direct Below user from structure of line division
     */
    public function getDirectBelowUsers(LineDivision $division)
    {
        if ($division->isLastLevelType()) return [];
        $users = $division->divisionChildren->reduce(function ($acc, $child) {
            $childDivisionUser = $child->user();
            if ($childDivisionUser != null) {
                $acc->push($childDivisionUser);
            }
            return $acc;
        }, new Collection());
        return $users->unique("id")->values();
    }

    /**
     * @description get all Below user from structure of line division
     */
    public function getBelowUsers(LineDivision $division)
    {
        if ($division->isLastLevelType()) return [];
        $users = $division->divisionChildren->reduce(function ($acc, $child) {
            $childDivisionUser = $child->user();
            if ($childDivisionUser != null) {
                $acc->push($childDivisionUser);
            }
            $acc = $acc->merge($this->getBelowUsers($child));
            return $acc;
        }, new Collection());
        return $users->unique("id")->values();
    }
}
