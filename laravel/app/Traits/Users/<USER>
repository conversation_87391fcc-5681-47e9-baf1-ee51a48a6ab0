<?php


namespace App\Traits\Users;

use App\Line;
use App\LineDivision;
use App\Position;
use App\PositionManager;
use App\Setting;
use App\User;
use App\UserPosition;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

trait IndexPerUser
{
    public function indexPerUser(User $user)
    {
        if ($user->hasRole("admin") || $user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) {
            $users = collect([]);
            return $users;
            // return User::whereNot('status','inactive')->pluck('id')->unique();
        }

        if ($user->hasPosition()) {
            $count = $user->lines()->count();
            $allLines = Line::select('id')->count();
            $allUsersPositions = collect([]);
            if ($count == $allLines) {
                $allUsersPositions = User::select('id')->pluck('id');
            }
            $positionUsers = $user->positionDivisions($user)->pluck('id')->push(auth()->id());
            $users = $positionUsers->merge($allUsersPositions);
            $positionsWithSameRole = collect();
            $isAuthorizationPositionSetting = Setting::where('key', 'authorize_position_show_data')->value('value') === 'Yes';
            if ($isAuthorizationPositionSetting) {
                $positionsWithSameRole = $user->position()->users()->pluck('users.id');
            }
            $users = $users->merge($positionsWithSameRole);

            return $users->unique();
        }

        $users = $user->allBelowUsers()->pluck('id');
        $users->push($user->id);


        return $users->unique();
    }
}
