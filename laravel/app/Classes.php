<?php

namespace App;

use App\Traits\ModelExportable;
use App\Traits\ModelImportable;
use App\Traits\PdfExportable;
use App\Traits\SendMail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

class Classes extends Model
{
    use SoftDeletes;
    use ModelImportable;
    use ModelExportable;
    use PdfExportable;
    use SendMail;

    protected $guard_name = 'api';

    protected $table = 'classes';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name', 'notes', 'ratio', 'sort', 'file_id'
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function lineClasses()
    {
        return $this->hasMany(LineClasses::class, 'class_id')->whereNull('line_classes.deleted_at')->where('line_classes.from_date', '<=', now())
            ->where(fn($q) => $q->where('line_classes.to_date', '>', (string)Carbon::now())
                ->orWhere('line_classes.to_date', null));
    }

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

    public function doctors()
    {
        return $this->hasMany(Doctor::class,'class_id');
    }

    public function actualVisits()
    {
        return $this->hasManyThrough(ActualVisit::class,Doctor::class,'class_id','account_dr_id');
    }
    public function lines($from = null, $to = null)
    {
        return $this->belongsToMany(Line::class, 'line_classes', 'class_id', 'line_id')
            ->withPivot('from_date', 'to_date', 'deleted_at')
            ->where('line_classes.deleted_at')
            ->where("line_classes.from_date", "<=", $from ?? now())
            ->where(fn ($q) => $q->where('line_classes.to_date', '>=', Carbon::parse($to)->toDateString() ?? (string)Carbon::now())
                ->orWhere('line_classes.to_date', null));
    }

}
