<?php

namespace App;

use App\Models\RoleHierarchy;
use App\Models\RoleSaleSetting;
use App\Models\ScheduledSetting;
use App\Traits\ModelImportable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Permission\Models\Role as BaseRoleModel;


class Role extends BaseRoleModel
{
    use ModelImportable;

    protected $guard_name = 'api';

    protected $with = ['permissions'];



    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

    public function roleSaleSetting(): HasOne
    {
        return $this->hasOne(RoleSaleSetting::class, 'role_id');
    }

    public function setRoleHierarchy()
    {
        RoleHierarchy::create([
            'role_id' => $this->id,
            'hierarchy' => $this->id
        ]);
    }
}
