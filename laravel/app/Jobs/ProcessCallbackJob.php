<?php

namespace App\Jobs;

use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use <PERSON><PERSON>\SerializableClosure\SerializableClosure;

class ProcessCallbackJob implements ShouldQueue
{
    use Batchable,Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 60 * 30;
    public $failOnTimeout = true;

    protected $callback;

    public function __construct(string $callback)
    {
        $this->callback = $callback;
    }


    public function handle(): void
    {
        $callback = unserialize($this->callback)->getClosure();

        $callback();
    }
}
