<?php

namespace App\Jobs;

use App\Helpers\Notifications\NotificationHelper;
use App\Models\Message;
use App\Notifications\MessageNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class SendMessageNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(private readonly Message $message,private readonly Collection $users)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $notification = new MessageNotification($this->message->load(["sender"]));

        NotificationHelper::send($this->users, $notification);
    }
}
