<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

class LineUser extends Model
{
    use SoftDeletes;

    protected $guard_name = 'api';

    protected $table = 'line_users';
    // protected $appends=['user'];
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'line_id',
        'from_date',
        'to_date',
        'file_id'
    ];


    protected $casts = [
        'from_date' => 'datetime',
        'to_date' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

    public function line()
    {
        return $this->belongsTo(Line::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function lineuserdivisions()
    {
        return $this->hasMany(LineDivisionUser::class);
    }

    public function lineDivisionUser()
    {
        return $this->hasMany(LineDivisionUser::class, 'user_id', 'user_id');
    }

    public function position()
    {
        return $this->hasOne(UserPosition::class);
    }
    // public function getUserAttribute(){
    //     return $this->user()->get('name');
    // }

    public function restore()
    {
        $this->withTrashed()->where('id', $this->id)->restore();
    }


    public function forceDelete()
    {
        $this->withTrashed()->where('id', $this->id)->forceDelete();
    }
}
