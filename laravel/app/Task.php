<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Task extends Model
{
    use SoftDeletes;
    protected $guard_name = 'api';

    protected $table = 'tasks';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['sender_id','subject','description','date','periority'];

    protected $casts = [
        'date' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }
    public function users()
    {
        return $this->belongsToMany(User::class,'recievers');
    }
    public function user()
    {
        return $this->belongsTo(User::class,'sender_id');
    }
    public function recievers()
    {
        return $this->hasMany(Reciever::class,'task_id');
    }
    public function attachments()
    {
        return $this->hasMany(TaskAttachment::class,'task_id');
    }
    public function restore()
    {
        $this->withTrashed()->where('id',$this->id)->restore();
        Reciever::withTrashed()->where('task_id',$this->id)->restore();
        TaskAttachment::withTrashed()->where('task_id',$this->id)->restore();
    }

    public function forceDelete()
    {
        Reciever::withTrashed()->where('task_id',$this->id)->forceDelete();
        TaskAttachment::withTrashed()->where('task_id',$this->id)->forceDelete();
        $this->withTrashed()->where('id',$this->id)->forceDelete();
    }
}
