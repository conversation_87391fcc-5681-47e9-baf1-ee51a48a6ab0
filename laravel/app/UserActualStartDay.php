<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserActualStartDay extends Model
{
    use SoftDeletes;

    protected $guard_name = 'api';

    protected $table = 'user_actual_start_day';

    // protected $guarded = ['id'];

    protected $fillable = ['line_id', 'user_id', 'date'];

    protected $casts = [
        'date' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo('App\User', 'users_id');
    }
}
