<?php

namespace App;

use App\Models\ProductSample;
use App\Services\Structure\Repositories\LineDivisionRepositoryInterface;
use App\Traits\ModelImportable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Staudenmeir\EloquentHasManyDeep\HasManyDeep;
use Staudenmeir\EloquentHasManyDeep\HasOneDeep;
use \Staudenmeir\EloquentHasManyDeep\HasRelationships;
use Staudenmeir\LaravelCte\Eloquent\QueriesExpressions;

class LineDivision extends Model
{
    use SoftDeletes;
    use ModelImportable;
    use HasRelationships;


    //    use HasRecursiveRelationships;
    use QueriesExpressions;

    protected $guard_name = 'api';

    protected $table = 'line_divisions';

    // protected $appends = ['manager'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'division_type_id',
        'line_id',
        'name',
        'is_kol',
        'from_date',
        'to_date',
        'file_id',
        'll',
        'lg'
    ];

    protected $casts = [
        'from_date' => 'datetime',
        'to_date' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function scopeOfType(Builder $query, DivisionType|int $type)
    {
        if ($type instanceof DivisionType) {
            $typeId = $type->id;
        } else {
            $typeId = $type;
        }
        return $query->where('division_type_id', $typeId);
    }


    public function parent(): HasOneDeep
    {
        return $this->hasOneDeepFromRelations($this->linedivparents(), (new LineDivParent())->parent())
            ->where('line_div_parents.from_date', '<=', (string)Carbon::now())
            ->where(fn($q) => $q->where('line_div_parents.to_date', '=', null)->orWhere('line_div_parents.to_date', '>=', (string)Carbon::now()));
    }

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

    public function getColorAttribute()
    {
        return $this->DivisionType()->first()->color;
    }

    public function getPaddingAttribute()
    {
        return $this->DivisionType()->first()->padding;
    }

    public function line()
    {
        return $this->belongsTo(Line::class);
    }

    public function samples()
    {
        return $this->hasMany(ProductSample::class, 'division_id');
    }

    public function linedivisiontype()
    {
        return $this->belongsTo(LineDivisionType::class);
    }

    public function linedivparents()
    {
        return $this->hasMany(LineDivParent::class, 'line_div_id');
    }

    /**
     * @deprecated
     */
    public function linedivuser()
    {
        return $this->hasMany(LineDivisionUser::class);
    }

    public function accountLines()
    {
        return $this->hasMany(AccountLines::class, 'line_division_id');
    }

    public function DivisionType()
    {
        return $this->belongsTo(DivisionType::class, 'division_type_id');
    }

    public function lineBricks()
    {
        return $this->hasMany(LineBricks::class, 'line_division_id');
    }

    public function bricks($from = null, $to = null)
    {
        return $this->belongsToMany(Brick::class, 'line_bricks', 'line_division_id', 'brick_id')
            ->withPivot(['from_date', 'to_date', 'ratio', 'deleted_at'])
            ->where('line_bricks.from_date', '<=', Carbon::parse($from)->toDateString() ?? (string)Carbon::now())
            ->whereNull('line_bricks.deleted_at')
            ->where(fn($q) => $q->where('line_bricks.to_date', '=', null)->orWhere('line_bricks.to_date', '>=', Carbon::parse($to)->toDateString() ?? (string)Carbon::now()));
    }

    public function lineDivChildren()
    {
        return $this->hasMany(LineDivParent::class, 'parent_id');
    }

    public function getManagerAttribute()
    {
        return $this->linedivuser()->get();
    }

    public function users($from = null, $to = null)
    {
        $from = $from ? Carbon::parse($from)->startOfDay() : Carbon::now()->startOfDay();
        $to = $to ? Carbon::parse($to)->endOfDay() : Carbon::now()->endOfDay();
        return $this->belongsToMany(User::class, 'line_users_divisions', 'line_division_id', 'user_id')
            ->withPivot('from_date', 'to_date', 'line_id', 'created_at')
            ->whereNull('line_users_divisions.deleted_at')
            ->where(function ($query) use ($from, $to) {
                $query->where(function ($subQuery) use ($from, $to) {
                    $subQuery->whereNull('line_users_divisions.to_date') // Active records
                    ->orWhereBetween('line_users_divisions.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                    ->orWhere('line_users_divisions.to_date', '>=', $to->toDateString()); // Ends within range
                })
                    ->where(function ($subQuery) use ($from, $to) {
                        $subQuery->where('line_users_divisions.from_date', '<=', $from->toDateString()) // Starts before range
                        ->orWhereBetween('line_users_divisions.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                    });
            });
        // ->where('line_users_divisions.from_date', '<=', $from ?? (string)Carbon::now())
        // ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)
        // ->orWhere('line_users_divisions.to_date', '>=', Carbon::parse($to)->toDateString() ?? (string)Carbon::now()));
    }

    public function structureUsers($date)
    {
        return $this->belongsToMany(User::class, 'line_users_divisions', 'line_division_id', 'user_id')
            ->withPivot('from_date', 'to_date', 'line_id', 'created_at')
            ->where('line_users_divisions.from_date', '<=', $date)
            ->whereNull('line_users_divisions.deleted_at')
            ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)->orWhere('line_users_divisions.to_date', '>=', $date));
    }

    public function allUsers()
    {
        return $this->belongsToMany(User::class, 'line_users_divisions', 'line_division_id', 'user_id')
            ->whereNull('line_users_divisions.deleted_at');
    }


    public function user($from = null, $to = null)
    {
        return $this->users($from, $to)->wherePivot('line_id', '=', $this->line_id)->orderBy('created_at', 'desc')->first();
    }


    public static function whereTypeRoot(Line $line)
    {
        return self::where('line_id', $line->id)->where('division_type_id', self::root($line)->division_type_id);
    }

    public static function root(Line $line)
    {
        return self::where('line_id', $line->id)->whereDoesntHave('parents')->first();
    }

    public function parents($from = null, $to = null)
    {
        $from = $from ? Carbon::parse($from)->startOfDay() : Carbon::now()->startOfDay();
        $to = $to ? Carbon::parse($to)->endOfDay() : Carbon::now()->endOfDay();
        return $this->belongsToMany(LineDivision::class, 'line_div_parents', 'line_div_id', 'parent_id')
            ->withPivot(["parent_id", "from_date", "to_date", 'deleted_at'])
            ->whereNull('line_div_parents.deleted_at')
            ->where(function ($query) use ($from, $to) {
                $query->where(function ($subQuery) use ($from, $to) {
                    $subQuery->whereNull('line_div_parents.to_date') // Active records
                    ->orWhereBetween('line_div_parents.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                    ->orWhere('line_div_parents.to_date', '>=', $to->toDateString()); // Ends within range
                })
                    ->where(function ($subQuery) use ($from, $to) {
                        $subQuery->where('line_div_parents.from_date', '<=', $from->toDateString()) // Starts before range
                        ->orWhereBetween('line_div_parents.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                    });
            });
    }

    public function divisionChildren($from = null, $to = null): BelongsToMany
    {
        $fromDate = $from ? Carbon::parse($from)->startOfDay() : Carbon::now()->startOfDay();
        $toDate = $to ? Carbon::parse($to)->endOfDay() : Carbon::now()->endOfDay();

        return $this->belongsToMany(LineDivision::class, 'line_div_parents', 'parent_id', 'line_div_id')
            ->withPivot(['from_date', 'to_date'])
            ->whereNull('line_div_parents.deleted_at')
            // Join with a subquery to get the latest parent relationship
            ->joinSub(
                function ($query) use ($fromDate, $toDate) {
                    $query->select('line_div_id')
                        ->selectRaw('MAX(from_date) as latest_from_date')
                        ->from('line_div_parents')
                        ->whereNull('deleted_at')
                        ->where('from_date', '<=', $toDate)
                        ->where(function ($q) use ($fromDate) {
                            $q->whereNull('to_date')
                                ->orWhere('to_date', '>=', $fromDate);
                        })
                        ->groupBy('line_div_id');
                },
                'latest_relations',
                function ($join) {
                    $join->on('line_div_parents.line_div_id', '=', 'latest_relations.line_div_id')
                        ->on('line_div_parents.from_date', '=', 'latest_relations.latest_from_date');
                }
            )
            ->where('line_div_parents.from_date', '<=', $toDate)
            ->where(function ($query) use ($fromDate) {
                $query->whereNull('line_div_parents.to_date')
                    ->orWhere('line_div_parents.to_date', '>=', $fromDate);
            });
    }

    public function divisionChildrenPerPeriod($date)
    {
        return $this->belongsToMany(LineDivision::class, 'line_div_parents', 'parent_id', 'line_div_id')
            ->withPivot(["from_date", "to_date", "deleted_at"])
            ->whereNull('line_div_parents.deleted_at')
            ->where('line_div_parents.from_date', '<=', $date)
            // ->where('line_div_parents.from_date', '<=', $to)
            ->where(fn($q) => $q->where('line_div_parents.to_date', '=', null)
                ->orWhere('line_div_parents.to_date', '>=', $date));
    }

    public static function getDivisionUser($division)
    {
        if (LineDivision::where('id', '=', $division->id)->first()->linedivuser->first() == null) {
            return null;
        }
        $linediv = LineDivision::where('id', '=', $division->id)->first()->linedivuser->first()->lineuser->first()->user;
        return $linediv;
    }

    public static function isLastLevel($division)
    {
        return LineDivision::where("id", $division->id)->with('DivisionType')->get()->pluck('DivisionType')->first()->last_level == 1;
    }

    public function isLastLevelType(): bool
    {
        return $this->DivisionType->last_level == 1;
    }

    public function isFirstLevelType(): bool
    {
        return $this->DivisionType->level == 1;
    }

    public function doctors()
    {
        return $this->hasManyDeepFromRelations($this->accounts(), (new Account)->activeAccountDoctors())
            ->where('accounts.active_date', '<=', (string)Carbon::now())
            ->where('account_lines.from_date', '<=', (string)Carbon::now())
            ->where(fn($q) => $q->where('accounts.inactive_date', '=', null)->orWhere('accounts.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('account_lines.to_date', '=', null)->orWhere('account_lines.to_date', '>=', (string)Carbon::now()))
            ->where('doctors.active_date', '<=', (string)Carbon::now())
            ->where('new_account_doctors.from_date', '<=', (string)Carbon::now())
            ->where(fn($q) => $q->where('doctors.inactive_date', '=', null)->orWhere('doctors.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)->orWhere('new_account_doctors.to_date', '>=', (string)Carbon::now()));
    }

    public function accounts()
    {
        return $this->belongsToMany(Account::class, 'account_lines', 'line_division_id', 'account_id')
            ->withPivot(["from_date", "to_date"])
            ->where('accounts.active_date', '<=', (string)Carbon::now())
            ->where('account_lines.from_date', '<=', (string)Carbon::now())
            ->where(fn($q) => $q->where('accounts.inactive_date', '=', null)->orWhere('accounts.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('account_lines.to_date', '=', null)->orWhere('account_lines.to_date', '>=', (string)Carbon::now()));
    }

    public function getAccounts(AccountType $type)
    {
        return $this->accounts()->where('type_id', $type->id)->get();
    }

    public function accountTypes($ids = null)
    {
        return $this->accounts->map(function ($account) use ($ids) {
            $type = $account->type;
            if (!$ids) {
                return $type;
            }

            return collect($ids)->reduce(function ($acc, $id) use ($type) {
                if ($id == $type->id) $acc = $type;

                return $acc;
            });
        })->filter(function ($item) {
            return $item != null;
        });
    }

    /**
     * @param array|null $ids
     * @param array|null $specIds
     * @description $ids are ids of account types array and array for speciality ids
     */
    public function specialities($ids = null, $specIds = null)
    {
        return $this->accounts->map(function ($account) use ($ids, $specIds) {
            $specialities = $account->doctors->map(function ($doctor) use ($specIds) {
                $speciality = $doctor->speciality;
                if (!$specIds) {
                    return $speciality;
                }

                return collect($specIds)->reduce(function ($acc, $id) use ($speciality) {
                    if ($id == $speciality->id) $acc = $speciality;

                    return $acc;
                });
            })->filter(function ($item) {
                return $item != null;
            });
            if (!$ids) {
                return $specialities;
            }
            $type = $account->type;
            return collect($ids)->reduce(function ($acc, $id) use ($type, $specialities) {
                if ($id == $type->id) $acc = $specialities;

                return $acc;
            });
        })->collapse();
    }
    // public function specialities()
    // {
    //     return $this->hasManyDeepFromRelations($this->accounts(), (new Account)->specialities());
    // }

    public function classes()
    {
        return $this->hasManyDeepFromRelations($this->accounts(), (new Account)->classes());
    }

    // public function accountTypes()
    // {
    //     return $this->hasManyDeepFromRelations($this->accounts(), (new Account)->type());
    // }

    // public function specialitiesPerAccountTypes()
    // {
    //     return $this->hasManyDeepFromRelations($this->accountTypes(),(new AccountType)->specialities());
    // }

    public function specialitiesPerAccountTypes()
    {
        return $this->with('accountTypes.specialities')->get()->pluck('accountTypes')->collapse()->groupBy('id')->map(function ($item) {
            return $item->first()->specialities->reduce(function ($acc, $item) {
                if ($speciality = $acc->where('id', $item->id)->first()) {
                    $speciality->count += 1;
                } else {
                    $item->setAttribute('count', 1);
                    $acc->push($item);
                }

                return $acc;
            }, new Collection());
        });
    }


    public function getBelowDivisions($from = null, $to = null): Collection
    {

        if ($this->isLastLevelType()) {
            $this->parent_id = $this->parents($from, $to)->first()?->id;
            return collect([$this]);
        }
        return $this->divisionChildren($from, $to)->get()
            ->reduce(function ($acc, $child) use ($from, $to) {
                if ($child != null) {
                    // Add parent_id attribute to each child division
                    $child->parent_id = $this->id;
                    $acc->add($child);
                }

                // Get below divisions and add the parent_id to each
                $belowDivisions = $child->getBelowDivisions($from, $to);
                foreach ($belowDivisions as $division) {
                    if (!isset($division->parent_id)) {
                        $division->parent_id = $child->id;
                    }
                }

                return $acc->merge($belowDivisions);
            }, new Collection());
    }

    public function getBelowDivisionsPerPeriod($date): Collection
    {
        // throw new CrmException($date);
        if ($this->isLastLevelType()) return new Collection([$this]);
        return $this->divisionChildrenPerPeriod($date)->get()
            ->reduce(function ($acc, $child) use ($date) {
                if ($child != null) {
                    $acc->push($child);
                }
                $acc = $acc->merge($child->getBelowDivisionsPerPeriod($date));
                return $acc;
            }, new Collection());
    }

    public function getAboveDivisions($from = null, $to = null): Collection
    {
        if ($this->isFirstLevelType()) return new Collection([$this]);
        return $this->parents($from, $to)->get()
            ->reduce(function ($acc, $parent) use ($from, $to) {
                if ($parent != null) {
                    $acc->push($parent);
                }
                $acc = $acc->merge($parent->getAboveDivisions($from, $to));
                return $acc;
            }, new Collection());
    }

    public function allDivisions()
    {
        $selected = [
            'line_divisions.id',
            'line_div_parents.parent_id',
        ];
        $data = LineDivision::select(...$selected)
            ->leftJoin("line_div_parents", "line_divisions.id", "line_div_parents.parent_id")
            ->where(fn($q) => $q->where('line_divisions.to_date', '>', (string)Carbon::now())
                ->orWhere('line_divisions.to_date', null))
            ->whereNull('line_divisions.deleted_at')->get();
    }

    public static function getAllDivisions(
        int|string|null $authUserId = null,
        int|string|null $divisionTypeId = null,
        array           $userIds = [],
        array           $divisionIds = [],
        array           $lineIds = [],
        ?Carbon         $from = null,
        ?Carbon         $to = null
    )
    {
        $fromDate = $from ?? Carbon::now();
        $toDate = $to ?? Carbon::now();

        $query = LineDivision::from("line_divisions as d")
            ->withTrashed()
            ->whereNull("d.deleted_at")
            ->where("d.from_date", "<=", $fromDate)
            ->where(
                fn($q) => $q->where("d.to_date", ">", $toDate)->orWhere("d.to_date", null)
            )
            ->select(
                "d.id",
                "d.name",
                "dt.name as type",
                "dt.color as color",
                "d.line_id",
                "u.id as user_id",
                "u.fullname as user_name",
                "l.name as line_name",
                DB::raw("1 as level")
            )
            ->selectRaw("CAST(crm_d.id AS CHAR(50)) AS order_sequence")
            ->join("division_types as dt", "dt.id", "d.division_type_id")
            ->join("lines as l", "l.id", "d.line_id")
            ->leftjoin(
                "line_users_divisions as lud",
                fn($join) => $join
                    ->on("lud.line_division_id", "d.id")
                    ->whereColumn("d.line_id", "lud.line_id")
                    ->where("lud.from_date", "<=", $fromDate)
                    ->where(
                        fn($q) => $q
                            ->where("lud.to_date", ">", $toDate)
                            ->orWhere("lud.to_date", null)
                    )
                    ->whereNull("lud.deleted_at")
            )
            ->leftjoin("users as u", function ($join) use (
                $authUserId,
                $divisionIds,
                $userIds
            ) {
                $join->on("u.id", "lud.user_id");
                if ($authUserId !== null && (empty($divisionIds) && empty($userIds))) {
                    $join->where("u.id", $authUserId);
                }
            })
            ->unionAll(
                LineDivision::from("line_divisions as d")
                    ->withTrashed()
                    ->whereNull("d.deleted_at")
                    ->select([
                        "d.id",
                        "d.name",
                        "dt.name as type",
                        "dt.color as color",
                        "d.line_id",
                        "u.id as user_id",
                        "u.fullname as name",
                        "l.name as line_name",
                        DB::raw("crm_dh.level + 1 as level")
                    ])
                    ->selectRaw(
                        "CONCAT(CAST(crm_dh.order_sequence AS CHAR(50)),'_',CAST(crm_d.id AS CHAR(50))) AS order_sequence"
                    )
                    ->join("lines as l", "l.id", "d.line_id")
                    ->join(
                        "line_div_parents as dp",
                        fn($join) => $join
                            ->on("dp.line_div_id", "d.id")
                            ->where("d.from_date", "<=", $fromDate)
                            ->where(
                                fn($q) => $q
                                    ->where("d.to_date", ">", $toDate)
                                    ->orWhere("d.to_date", null)
                            )
                    ) //, "dp.line_div_id", "d.id")
                    ->join("division_hierarchy as dh", "dh.id", "dp.parent_id")
                    ->join("division_types as dt", "dt.id", "d.division_type_id")
                    ->leftjoin(
                        "line_users_divisions as lud",
                        fn($join) => $join
                            ->on("lud.line_division_id", "d.id")
                            ->whereColumn("d.line_id", "lud.line_id")
                            ->where("lud.from_date", "<=", $fromDate)
                            ->where(
                                fn($q) => $q
                                    ->where("lud.to_date", ">", $toDate)
                                    ->orWhere("lud.to_date", null)
                            )
                            ->whereNull("lud.deleted_at")
                    )
                    ->leftjoin("users as u", "u.id", "lud.user_id")
            );

        if (!empty($divisionIds)) {
            $query->whereIntegerInRaw("d.id", $divisionIds);
        } elseif (!empty($userIds)) {
            $query->whereIntegerInRaw("u.id", $userIds);
        } elseif ($authUserId == null) {
            $query->where("d.division_type_id", $divisionTypeId ?? 1);
        }

        if (!empty($lineIds)) {
            $query->whereIntegerInRaw("d.line_id", $lineIds);
        }

        $query = DB::table("division_hierarchy")
            ->select(["type", "color", "line_name", "line_id", "level"])
            ->withRecursiveExpression("division_hierarchy", $query)
            ->orderBy("order_sequence");

        if (!empty($divisionIds)) {
            $query
                ->selectRaw("group_concat(user_id) as user_id")
                ->selectRaw("group_concat(user_name) as user_name")
                ->selectRaw("group_concat(order_sequence) as order_sequence")
                ->addSelect("name", "id")
                ->groupBy([
                    "type",
                    "color",
                    "line_name",
                    "line_id",
                    "id",
                    "name",
                    "level"
                ]);
        } elseif (!empty($userIds)) {
            $query
                ->selectRaw("group_concat(id) as id")
                ->selectRaw("group_concat(name) as name")
                ->selectRaw("group_concat(order_sequence) as order_sequence")
                ->addSelect("user_id", "user_name")
                ->groupBy([
                    "type",
                    "color",
                    "line_name",
                    "line_id",
                    "user_id",
                    "user_name",
                    "level"
                ]);
        } else {
            $query->addSelect("name", "id", "user_id", "user_name");
        }

        return $query->get();
    }


    public static function salesPerDivision(
        array   $divisionIds = [],
        array   $lineIds = [],
        array   $productIds = [],
        array   $distIds = [],
        ?Carbon $from = null,
        ?Carbon $to = null,
        string  $label = 'quantity'
    ): Collection
    {
        $fromDate = $from ?? Carbon::now();
        $toDate = $to ?? Carbon::now();

        if (!str($label)->is(["quantity", "value"])) {
            throw new \Exception("selection must be quantity or value");
        }

        $q = LineDivision::from("line_divisions as d")
            ->withTrashed()
            ->whereNull("d.deleted_at")
            ->where("d.from_date", "<=", $fromDate)
            ->where(
                fn($q) => $q->where("d.to_date", ">", $toDate)->orWhere("d.to_date", null)
            )
            ->select([
                "d.id",
                "d.name",
                "dt.color",
                "dp.parent_id",
                DB::raw("0 as has_child"),
                DB::raw("sum(COALESCE(crm_sd.${label},0)) as quantity")
            ])
            ->join(
                "line_div_parents as dp",
                fn($join) => $join
                    ->on("dp.line_div_id", "d.id")
                    ->where("dp.from_date", "<=", $fromDate)
                    ->where(
                        fn($q) => $q
                            ->where("dp.to_date", ">", $toDate)
                            ->orWhere("dp.to_date", null)
                    )
            )
            ->leftjoin("sales_details as sd", function ($join) use ($fromDate, $toDate, $productIds, $distIds) {
                $join->on("sd.div_id", "d.id")
                    ->whereBetween("sd.date", [(string)$fromDate, (string)$toDate]);

                if (!empty($productIds) || !empty($distIds))
                    $join
                        ->join("sales as s", "s.id", "sd.sale_id");

                if (!empty($productIds)) {
                    $join->whereIn("s.product_id", $productIds);
                }

                if (!empty($distIds)) {
                    $join->whereIn("s.distributor_id", $distIds);
                }
            })
            ->join("division_types as dt", "dt.id", "d.division_type_id")
            ->where("dt.last_level", 1)
            ->groupBy(["d.id", "d.name", "dt.color", "dp.parent_id"])
            ->unionAll(
                LineDivision::from("line_divisions as d")
                    ->withTrashed()
                    ->whereNull("d.deleted_at")
                    ->where("d.from_date", "<=", $fromDate)
                    ->where(
                        fn($q) => $q->where("d.to_date", ">", $toDate)->orWhere("d.to_date", null)
                    )
                    ->select([
                        "d.id",
                        "d.name",
                        "dt.color",
                        "dp.parent_id",
                        DB::raw("1 as has_child"),
                        DB::raw("crm_dh.quantity as quantity")
                    ])
                    ->join("division_types as dt", "dt.id", "d.division_type_id")
                    ->leftjoin(
                        "line_div_parents as dp",
                        fn($join) => $join
                            ->on("dp.line_div_id", "d.id")
                            ->where("dp.from_date", "<=", $fromDate)
                            ->where(
                                fn($q) => $q
                                    ->where("dp.to_date", ">", $toDate)
                                    ->orWhere("dp.to_date", null)
                            )
                    )
                    ->join("division_hierarchy as dh", "dh.parent_id", "d.id")
            );

        if (!empty($lineIds))
            $q->whereIntegerInRaw("d.line_id", $lineIds);

        $query = DB::table("division_hierarchy")
            ->select([
                "division_hierarchy.id",
                "division_hierarchy.name",
                "u.fullname as user",
                "division_hierarchy.color",
                "division_hierarchy.has_child",
                DB::raw("sum(crm_division_hierarchy.quantity) as total"),
                "division_hierarchy.parent_id"
            ])
            ->withRecursiveExpression("division_hierarchy", $q)
            ->leftjoin(
                "line_users_divisions as lud",
                fn($join) => $join
                    ->on("lud.line_division_id", "division_hierarchy.id")
                    ->where("lud.from_date", "<=", $fromDate)
                    ->where(
                        fn($q) => $q
                            ->where("lud.to_date", ">", $toDate)
                            ->orWhere("lud.to_date", null)
                    )
                    ->whereNull("lud.deleted_at")
            )
            ->leftjoin("users as u", function ($join) {
                $join->on("u.id", "lud.user_id");
            })
            ->groupBy(
                "division_hierarchy.id",
                "division_hierarchy.color",
                "division_hierarchy.name",
                "division_hierarchy.has_child",
                "u.fullname",
                "division_hierarchy.parent_id"
            );

        if (!empty($divisionIds)) {
            $query->whereIN("division_hierarchy.id", $divisionIds);
        }

        return $query->get()->map(function ($item) {

            $data = [
                "Id" => (string)$item->id,
                "Name" => $item->name,
                "user" => $item->user ?? "",
                "Color" => $item->color,
                'expanded' => false,
                "total" => (string)round($item->total, 2),
                "ParentId" => $item->parent_id ? (string)$item->parent_id : null
            ];

            if (!$item->parent_id)
                $data["hasChild"] = !!$item->has_child;

            return $data;
        });
    }


    // Add a relationship for accessing UserPosition models
    public function userPositions($from = null, $to = null): BelongsToMany
    {
        $from = $from ? Carbon::parse($from)->startOfDay() : Carbon::now()->startOfDay();
        $to = $to ? Carbon::parse($to)->endOfDay() : Carbon::now()->endOfDay();

        return $this->belongsToMany(
            UserPosition::class,
            LineDivisionPosition::getModel()->getTable(),
            'line_div_id',
            'user_position_id'
        )->whereNull('line_division_positions.deleted_at')
            ->where('line_division_positions.from_date', '<=', $from)
            ->where(function ($query) use ($to) {
                $query->whereNull('line_division_positions.to_date')
                    ->orWhere('line_division_positions.to_date', '>=', $to);
            });
    }


    // Access all positions related to this division
    public function positions($from = null, $to = null): HasManyDeep
    {
        return $this->hasManyDeepFromRelations(
            $this->userPositions($from, $to),
            (new UserPosition())->position()
        )
            ->distinct();
    }


    /**
     * Delegate to a repository for easier testing
     */
    public function findDeepestDescendants(?string $date = null): Collection
    {
        return app(LineDivisionRepositoryInterface::class)->findDeepestDescendants($this, $date);
    }

    public function getDeepestDescendantsAtLevel(DivisionType|int $targetType, ?string $date = null): Collection
    {
        return app(LineDivisionRepositoryInterface::class)->getDeepestDescendantsAtLevel($this, $targetType, $date);
    }

    public function findDeepestDescendantsCTE(?string $date = null): Collection
    {
        return app(LineDivisionRepositoryInterface::class)->findDeepestDescendantsCTE($this, $date);
    }

    public function findAncestorAtLevelCTE(DivisionType|int $targetType, ?string $date = null): ?LineDivision
    {
        return app(LineDivisionRepositoryInterface::class)->findAncestorAtLevelCTE($this, $targetType, $date);
    }

}
