<?php

namespace App;

use App\Traits\ModelImportable;
use App\Traits\ModelExportable;
use App\Traits\PdfExportable;
use App\Traits\SendMail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Manufacturer extends Model
{
    use SoftDeletes;
    use ModelImportable;
    use ModelExportable;
    use PdfExportable;
    use SendMail;
    protected $guard_name = 'api';

    protected $table = 'manufacturers';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name', 'notes', 'sort','file_id'
    ];


    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function productmanufacturer()
    {
        return $this->belongsTo(ProductManufacturers::class);
    }

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }
}
