<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\ModelImportable;


class LineSpecialities extends Model
{
    use SoftDeletes;
    use ModelImportable;
    // use Exportable;

    protected $guard_name = 'api';

    protected $table = 'line_specialities';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'speciality_id','line_id','from_date', 'to_date','file_id'
    ];

    protected $casts = [
        'from_date' => 'datetime',
        'to_date' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

    public function specialities()
    {
        return $this->belongsTo(Speciality::class ,'speciality_id');
    }

    public function line()
    {
        return $this->belongsTo(Line::class);
    }
}
