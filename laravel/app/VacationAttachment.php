<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class VacationAttachment extends Model
{
    //
    use SoftDeletes ;
    public $timestamps = true;
    protected $guard_name = 'api';

    protected $table = 'vacation_attachments';

    protected $fillable = ['vacation_id','name','path'];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];
}
