<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChartLabel extends Model
{
    use SoftDeletes;

    protected $guard_name = 'api';

    protected $table = 'chart_labels';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name','chart_id'
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function chart()
    {
        return $this->belongsTo(Chart::class);
    }

}
