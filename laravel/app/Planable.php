<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Planable extends Model
{
    protected $table = 'planables';

    protected $fillable = ['planable_id', 'planable_type', 'line_id', 'file_id'];

    protected $hidden = ['pivot'];

    public function approvables()
    {
        return $this->belongsToMany(Approvable::class)->withPivot(['id', 'required', 'request_type', 'flow', 'num_days', 'show_data', 'planable_id', 'approvable_id']);
    }

    public function planable()
    {
        return $this->morphTo();
    }

    public function line()
    {
        return $this->belongsTo(Line::class);
    }
    public function approvablePlanableVisits()
    {
        return $this->hasMany(ApprovablePlanable::class);
    }
}
