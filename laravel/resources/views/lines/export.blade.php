@if ($lines->count() > 0)
    <table class="table table-bordered table-striped table-hover">
        <thead>
            <tr>
                <th colspan="10">Lines</th>
            </tr>
            <tr>
                <th style="font-weight: bold">#</th>
                <th style="font-weight: bold">ID</th>
                <th style="font-weight: bold">Name</th>
                <th style="font-weight: bold">Notes</th>
                <th style="font-weight: bold">Sort</th>
                <th style="font-weight: bold">Country</th>
                <th style="font-weight: bold">Currency</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($lines as $index => $line)
                <tr>
                    <th colspan="10">Line {{ $line?->name }}</th>
                </tr>
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td>{{ $line->id }}</td>
                    <td>{{ $line?->name }}</td>
                    <td>{{ $line?->notes }}</td>
                    <td>{{ $line?->sort }}</td>
                    <td>{{ $line?->country?->name }}</td>
                    <td>{{ $line->currency?->name }}</td>
                </tr>

                <tr>
                    <th colspan="10" style="font-weight: bold">Division Types of {{ $line?->name }}</th>
                </tr>
                @if ($line->linedivisiontypes->count() > 0)
                    <tr>
                        <th style="font-weight: bold">#</th>
                        <th style="font-weight: bold">ID</th>
                        <th style="font-weight: bold">Type Name</th>
                        <th style="font-weight: bold">From</th>
                        <th style="font-weight: bold">To</th>
                    </tr>
                    @foreach ($line->linedivisiontypes as $index => $linedivisiontype)
                        <tr>
                            <td>{{ $index + 1 }}</td>
                            <td>{{ $linedivisiontype?->id }}</td>
                            <td>{{ $linedivisiontype?->divisiontype?->name }}</td>
                            <td>{{ $linedivisiontype?->from_date }}</td>
                            <td>{{ $linedivisiontype?->to_date }}</td>
                        </tr>
                    @endforeach
                @else
                    <tr>
                        <th colspan="10">No Data Found</th>
                    </tr>
                @endif

                <tr>
                    <th colspan="10" style="font-weight: bold">Divisions of {{ $line?->name }}</th>
                </tr>
                @if ($line->divisions->count() > 0)
                    <tr>
                        <th style="font-weight: bold">#</th>
                        <th style="font-weight: bold">ID</th>
                        <th style="font-weight: bold">Division Name</th>
                        <th style="font-weight: bold">Parent Name</th>
                        <th style="font-weight: bold">From</th>
                        <th style="font-weight: bold">To</th>
                    </tr>
                    @foreach ($line->divisions as $index => $division)
                        {{-- var_dump({{$division}}); --}}
                        <tr>
                            <td>{{ $index + 1 }}</td>
                            <td>{{ $division?->id }}</td>
                            <td>{{ $division?->name }}</td>
                            <td>{{ isset($division?->linedivparents) && isset($division?->linedivparents?->parent) ? $division?->linedivparents?->parent?->name : '---' }}
                            </td>
                            <td>{{ $division?->from_date }}</td>
                            <td>{{ $division?->to_date }}</td>
                        </tr>
                    @endforeach
                @else
                    <tr>
                        <th colspan="10">No Data Found</th>
                    </tr>
                @endif

                <tr>
                    <th colspan="10" style="font-weight: bold">Products of {{ $line?->name }}</th>
                </tr>
                @if ($line->lineproducts->count() > 0)
                    <tr>
                        <th style="font-weight: bold">#</th>
                        <th style="font-weight: bold">Line Product ID</th>
                        <th style="font-weight: bold">Line Name</th>
                        <th style="font-weight: bold">Line ID</th>
                        <th style="font-weight: bold">Product Name</th>
                        <th style="font-weight: bold">Product ID</th>
                        <th style="font-weight: bold">From</th>
                        <th style="font-weight: bold">To</th>
                    </tr>
                    @foreach ($line->lineproducts as $index => $lineproduct)
                        <tr>
                            <td>{{ $index + 1 }}</td>
                            <td>{{ $lineproduct?->id }}</td>
                            <td>{{ $line?->name }}</td>
                            <td>{{ $line?->id }}</td>
                            <td>{{ $lineproduct?->product?->name }}</td>
                            <td>{{ $lineproduct?->product?->id }}</td>
                            <td>{{ $lineproduct?->from_date }}</td>
                            <td>{{ $lineproduct?->to_date }}</td>
                        </tr>
                    @endforeach
                @else
                    <tr>
                        <th colspan="10">No Data Found</th>
                    </tr>
                @endif

                <tr>
                    <th colspan="10" style="font-weight: bold">Users of {{ $line?->name }}</th>
                </tr>
                @if ($line->lineusers->count() > 0)
                    <tr>
                        <th style="font-weight: bold">#</th>
                        <th style="font-weight: bold">ID</th>
                        <th style="font-weight: bold">User Name</th>
                        <th style="font-weight: bold">From</th>
                        <th style="font-weight: bold">To</th>
                    </tr>
                    @foreach ($line->lineusers as $index => $lineuser)
                        <tr>
                            {{-- var_dump({{$lineuser}}); --}}
                            <td>{{ $index + 1 }}</td>
                            <td>{{ $lineuser?->id }}</td>
                            <td>{{ $lineuser?->user?->fullname }}</td>
                            <td>{{ $lineuser?->from_date }}</td>
                            <td>{{ $lineuser?->to_date }}</td>
                        </tr>
                    @endforeach
                @else
                    <tr>
                        <th colspan="10">No Data Found</th>
                    </tr>
                @endif

                <tr>
                    <th colspan="10" style="font-weight: bold">User Divisions of {{ $line?->name }}</th>
                </tr>
                @if ($line->linedivusers->count() > 0)
                    <tr>
                        <th style="font-weight: bold">#</th>
                        <th style="font-weight: bold">ID</th>
                        <th style="font-weight: bold">User Name</th>
                        <th style="font-weight: bold">Division Name</th>
                        <th style="font-weight: bold">From</th>
                        <th style="font-weight: bold">To</th>
                    </tr>
                    @foreach ($line->linedivusers as $index => $linedivuser)
                        <tr>
                            <td>{{ $index + 1 }}</td>
                            <td>{{ $linedivuser?->id }}</td>
                            <td>{{ $linedivuser?->user?->fullname }}</td>
                            <td>{{ $linedivuser?->linedivision?->name }}</td>
                            <td>{{ $linedivuser?->from_date }}</td>
                            <td>{{ $linedivuser?->to_date }}</td>
                        </tr>
                    @endforeach
                @else
                    <tr>
                        <th colspan="10">No Data Found</th>
                    </tr>
                @endif
            @endforeach
        </tbody>
    </table>
@else
    <h2>No Data Found</h2>
@endif
