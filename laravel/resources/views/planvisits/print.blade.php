<!DOCTYPE html>
<html class=" ">

<style>
    @page {
        size: auto;
        margin-top: 4cm;
        margin-bottom: 2cm;
        header: myheader;
    }
    img {
        width: 200px;
        height: 100px;
    }
</style>

<body class=" ">
    <htmlpageheader name="myheader">
        <img src="{{ $image }}" class="img-thumbnail" alt="">
    </htmlpageheader>
    <div style="text-align: center; margin:0">
        <strong style="color: blue;">{{ $header }}</strong>
    </div>

    <div class="col-xl-12">
        <section class="box">
            <div class="content-body">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-12">
                        @if ($planvisits->count() > 0)
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        {{-- <th>#</th> --}}
                                        <th>ID</th>
                                        <th>Line</th>
                                        <th>Division</th>
                                        <th>User</th>
                                        <th>account</th>
                                        <th>Doctor</th>
                                        <th>Speciality</th>
                                        <th>Account Type</th>
                                        <th>Type</th>
                                        <th>Shift</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Reason</th>
                                    </tr>
                              
                                </thead>
                                @foreach ($planvisits as $index=>$plan)

                                <tbody>
                                    <tr>
                                        {{-- <td>{{ $index + 1 }}</td> --}}
                                        <td>{{ $plan->id }}</td>
                                        <td>{{ $plan->line->name }}</td>
                                        <td>{{ $plan->division->name }}</td>
                                        <td>{{ $plan->user->fullname }}</td>
                                        <td>{{ $plan->account->name }}</td>
                                        <td>{{ $plan->doctor != null ? $plan->doctor->name : "" }}</td>
                                        <td>{{ $plan->doctor != null ? $plan->doctor->speciality->name : "" }}</td>
                                        <td>{{ $plan->account->type->name }}</td>
                                        <td>{{ $plan->visitType->name }}</td>
                                        <td>{{ $plan->shift != null ? $plan->shift?->name : "" }}</td>
                                        <td>{{ $plan->visit_date }}</td>
                                        <td>{{ $plan->details->first()?->approval ?? null }}</td>
                                        <td>{{ $plan->reasons->first()?->reason ?? "" }}</td>
                                    </tr>
                                </tbody>

                            @endforeach
                            </table>
                            @else
                            <h2>No Data Found</h2>
                        @endif
                    </div>
                </div>
            </div>
        </section>
    </div>


    <br/>
    <div style="text-align: right">
        <strong>User ID: {{$user_id}} </strong>
        <br/>
        <strong>User Name: {{$user_name}} </strong>
        <br/>
        <strong>Date: {{$exported_date}} </strong>
        <br/>
    </div>

    <br/>
    <strong>{{$footer}}</strong>

    </body>
</html>
