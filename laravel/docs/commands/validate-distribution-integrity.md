# Distribution Management Commands

## Overview

This document covers the Laravel Artisan commands for managing and validating distribution processes in the LineDivision system. All commands follow Laravel conventions and are designed to work seamlessly with Laravel Sail and Laravel Octane.

## Available Commands

### 1. Distribution Integrity Validation
**Command:** `distribution:validate-integrity`

Provides comprehensive data integrity validation for the distribution process, ensuring that total quantities before distribution exactly match the total quantities after distribution.

### 2. Distribution Rollback
**Command:** `distribution:rollback`

Rollback distribution operations by removing distributed sales and restoring original sales ceiling status.

### 3. Distribution Test Suite
**Command:** `distribution:test-integrity`

Test distribution integrity validation with various scenarios and configurations.

### 4. Distribution Test Data Generator
**Command:** `distribution:generate-test-data`

Generate test data for distribution integrity validation testing.

---

## 1. Distribution Integrity Validation

### Command Signature

```bash
php artisan distribution:validate-integrity [options]
```

**With Laravel Sail:**
```bash
./vendor/bin/sail artisan distribution:validate-integrity [options]
```

## Options

### Required Options
- `--from-date=YYYY-MM-DD` - Start date for validation
- `--to-date=YYYY-MM-DD` - End date for validation

### Optional Options
- `--distribution-type=N` - Filter by distribution type:
  - `1` = Private Pharmacy
  - `2` = Store (includes 90/10 split validation)
  - `3` = Local Chain
- `--product-ids=1,2,3` - Comma-separated product IDs to filter
- `--distributor-ids=1,2,3` - Comma-separated distributor IDs to filter
- `--tolerance=0.001` - Tolerance for rounding errors (default: 0.001)
- `--detailed` - Show detailed breakdown by categories
- `--export-csv=filename` - Export results to CSV file

## Usage Examples

### 1. Basic Validation
```bash
./vendor/bin/sail artisan validate:distribution-integrity \
  --from-date=2024-01-01 \
  --to-date=2024-01-31
```

### 2. Detailed Validation with Custom Tolerance
```bash
./vendor/bin/sail artisan validate:distribution-integrity \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --detailed \
  --tolerance=0.01
```

### 3. Store Distribution Validation (90/10 Split)
```bash
./vendor/bin/sail artisan validate:distribution-integrity \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --distribution-type=2 \
  --detailed
```

### 4. Validation with CSV Export
```bash
./vendor/bin/sail artisan validate:distribution-integrity \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --export-csv=validation_results_jan2024
```

### 5. Filter by Specific Products
```bash
./vendor/bin/sail artisan validate:distribution-integrity \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --product-ids=1,2,3 \
  --detailed
```

### 6. Filter by Specific Distributors
```bash
./vendor/bin/sail artisan validate:distribution-integrity \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --distributor-ids=1,2,3 \
  --detailed
```

## Output Sections

### 1. Header Information
- Date range being validated
- Tolerance setting
- Distribution type (if specified)
- Export settings

### 2. Before Distribution Totals
Shows totals from original sales (ceiling = BELOW or ABOVE):
- Total quantity, value, bonus
- Number of records

### 3. After Distribution Totals
Shows totals from all sales plus distributed details:
- **Sales Totals**: All sales including distributed ones
- **Details Totals**: All sales details (distributed portions)
- **Combined Totals**: Sales + Details (should match before totals)

### 4. Integrity Validation
Compares before and after totals with tolerance checking:
- ✅ **PASS**: Values match within tolerance
- ❌ **FAIL**: Values differ beyond tolerance
- ⚠️ **WARNING**: Minor differences within tolerance

### 5. Additional Validations
- **Orphaned Details**: Sales details without corresponding sales
- **Negative Values**: Quantities, values, or bonuses < 0
- **Null Values**: Missing critical field values
- **Split Distribution**: For Store strategy, validates 90/10 split ratios

### 6. Detailed Breakdown (if --detailed flag used)
- Breakdown by product
- Breakdown by distributor
- Breakdown by ceiling status
- Breakdown by date

### 7. Final Result
- Overall pass/fail status
- Summary of errors and warnings
- Validation parameters used

## Return Codes

- `0` (SUCCESS): Validation passed
- `1` (FAILURE): Validation failed or error occurred

## CSV Export

When using `--export-csv`, the command creates a CSV file in `storage/app/` with:
- Validation metadata (date, range, status)
- Before/after totals for all metrics
- Differences and tolerance used
- Error and warning messages

## Integration with Laravel Sail

The command is fully compatible with Laravel Sail and can be executed using:

```bash
# Basic usage
./vendor/bin/sail artisan validate:distribution-integrity --from-date=2024-01-01 --to-date=2024-01-31

# With all options
./vendor/bin/sail artisan validate:distribution-integrity \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --distribution-type=2 \
  --product-ids=1,2,3 \
  --distributor-ids=1,2,3 \
  --tolerance=0.01 \
  --detailed \
  --export-csv=validation_results
```

## Laravel Octane Compatibility

The command is designed to be compatible with Laravel Octane:
- Uses dependency injection properly
- No static state dependencies
- Proper memory management
- Service container integration

## Error Handling

The command includes comprehensive error handling:
- Input validation with clear error messages
- Database connection error handling
- Graceful handling of missing data
- Detailed logging of errors

## Performance Considerations

- Large date ranges may take longer to process
- Use filters (product-ids, distributor-ids) to reduce dataset size
- Consider running during off-peak hours for production data
- Memory usage is optimized for large datasets

## Integration with Rollback Script

This command works alongside the rollback distribution functionality:

1. **Before Rollback**: Validate current state
2. **After Rollback**: Validate restored state
3. **Comparison**: Ensure rollback restored original totals

## Troubleshooting

### Common Issues

1. **Missing Required Options**
   ```
   Both --from-date and --to-date are required
   ```
   Solution: Provide both date options in YYYY-MM-DD format

2. **Invalid Date Format**
   ```
   Invalid date format. Use YYYY-MM-DD
   ```
   Solution: Use the correct date format

3. **Invalid Distribution Type**
   ```
   distribution-type must be 1, 2, or 3
   ```
   Solution: Use valid distribution type values

4. **Database Connection Issues**
   Ensure Laravel Sail is running and database is accessible

### Getting Help

```bash
./vendor/bin/sail artisan validate:distribution-integrity --help
```

## Examples for Different Scenarios

### Daily Validation
```bash
./vendor/bin/sail artisan validate:distribution-integrity \
  --from-date=$(date -d "yesterday" +%Y-%m-%d) \
  --to-date=$(date -d "yesterday" +%Y-%m-%d)
```

### Monthly Validation with Export
```bash
./vendor/bin/sail artisan validate:distribution-integrity \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --detailed \
  --export-csv=monthly_validation_$(date +%Y%m)
```

### Store Strategy Validation
```bash
./vendor/bin/sail artisan distribution:validate-integrity \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --distribution-type=2 \
  --detailed \
  --tolerance=0.001
```

---

## 2. Distribution Rollback

### Command Signature

```bash
./vendor/bin/sail artisan distribution:rollback [options]
```

### Options

- `--from-date=YYYY-MM-DD` - Start date for rollback (required)
- `--to-date=YYYY-MM-DD` - End date for rollback (required)
- `--distribution-type=N` - Distribution type filter (1, 2, or 3)
- `--product-ids=1,2,3` - Comma-separated product IDs
- `--distributor-ids=1,2,3` - Comma-separated distributor IDs
- `--dry-run` - Preview changes without executing
- `--backup` - Create backup before rollback
- `--force` - Skip confirmation prompts

### Usage Examples

```bash
# Basic rollback with confirmation
./vendor/bin/sail artisan distribution:rollback \
  --from-date=2024-01-01 \
  --to-date=2024-01-31

# Dry run to preview changes
./vendor/bin/sail artisan distribution:rollback \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --dry-run

# Rollback with backup
./vendor/bin/sail artisan distribution:rollback \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --backup

# Force rollback without confirmation
./vendor/bin/sail artisan distribution:rollback \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --force
```

---

## 3. Distribution Test Suite

### Command Signature

```bash
./vendor/bin/sail artisan distribution:test-integrity [options]
```

### Options

- `--scenario=SCENARIO` - Test scenario (all, basic, detailed, store, export, filtered, error-handling)
- `--from-date=YYYY-MM-DD` - Override start date for testing
- `--to-date=YYYY-MM-DD` - Override end date for testing
- `--dry-run` - Show commands without executing them

### Available Scenarios

- `all` - Run all test scenarios (default)
- `basic` - Basic validation with default settings
- `detailed` - Detailed validation with custom tolerance
- `store` - Store distribution validation (90/10 split)
- `export` - Validation with CSV export
- `filtered` - Validation with product/distributor filters
- `error-handling` - Test error handling scenarios

### Usage Examples

```bash
# Run all test scenarios
./vendor/bin/sail artisan distribution:test-integrity

# Run specific scenario
./vendor/bin/sail artisan distribution:test-integrity --scenario=store

# Dry run to see what would be executed
./vendor/bin/sail artisan distribution:test-integrity --dry-run

# Test with custom date range
./vendor/bin/sail artisan distribution:test-integrity \
  --from-date=2024-01-01 \
  --to-date=2024-01-31
```

---

## 4. Distribution Test Data Generator

### Command Signature

```bash
./vendor/bin/sail artisan distribution:generate-test-data [options]
```

### Options

- `--count=N` - Number of sales records to generate (default: 100)
- `--from-date=YYYY-MM-DD` - Start date for test data
- `--to-date=YYYY-MM-DD` - End date for test data
- `--product-ids=1,2,3` - Product IDs to use (default: 1,2,3)
- `--distributor-ids=1,2,3` - Distributor IDs to use (default: 1,2,3)
- `--with-distribution` - Generate distributed sales and details
- `--clean` - Remove existing test data before generating
- `--dry-run` - Show what would be generated without creating data

### Usage Examples

```bash
# Generate basic test data
./vendor/bin/sail artisan distribution:generate-test-data --count=50

# Generate test data with distribution
./vendor/bin/sail artisan distribution:generate-test-data \
  --count=100 \
  --with-distribution

# Clean and regenerate test data
./vendor/bin/sail artisan distribution:generate-test-data \
  --count=200 \
  --clean \
  --with-distribution

# Generate test data for specific date range
./vendor/bin/sail artisan distribution:generate-test-data \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --count=150
```

---

## Command Workflow Examples

### Complete Testing Workflow

```bash
# 1. Generate test data
./vendor/bin/sail artisan distribution:generate-test-data \
  --count=500 \
  --with-distribution \
  --clean

# 2. Run validation tests
./vendor/bin/sail artisan distribution:test-integrity

# 3. Run specific validation
./vendor/bin/sail artisan distribution:validate-integrity \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --detailed \
  --export-csv=validation_results

# 4. If issues found, rollback if needed
./vendor/bin/sail artisan distribution:rollback \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --dry-run
```

### Production Validation Workflow

```bash
# 1. Validate current state
./vendor/bin/sail artisan distribution:validate-integrity \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --detailed \
  --export-csv=pre_rollback_validation

# 2. Rollback with backup
./vendor/bin/sail artisan distribution:rollback \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --backup

# 3. Validate after rollback
./vendor/bin/sail artisan distribution:validate-integrity \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --detailed \
  --export-csv=post_rollback_validation
```
