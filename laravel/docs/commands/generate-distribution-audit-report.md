# Distribution Audit Report Command

## Overview

The `distribution:generate-audit-report` command generates comprehensive audit reports for distribution integrity validation. It analyzes sales data distribution patterns, validates the 90/10 split algorithm, checks LineDivision hierarchy integrity, and provides detailed recommendations for improving data quality.

## Command Signature

```bash
php artisan distribution:generate-audit-report
```

## Required Parameters

- `--from-date=YYYY-MM-DD` - Start date for audit period
- `--to-date=YYYY-MM-DD` - End date for audit period

## Optional Parameters

### Filtering Options
- `--distribution-type=N` - Filter by distribution type:
  - `1` = Private Pharmacy
  - `2` = Store (includes 90/10 split validation)
  - `3` = Local Chain
- `--product-ids=1,2,3` - Comma-separated product IDs to filter
- `--distributor-ids=1,2,3` - Comma-separated distributor IDs to filter
- `--tolerance=0.001` - Tolerance for rounding errors (default: 0.001)

### Output Options
- `--format=FORMAT` - Output format: `table` (default), `json`, `csv`
- `--export-file=FILENAME` - Export results to file (without extension)
- `--summary-only` - Show only summary statistics

### Analysis Options
- `--include-hierarchy` - Include LineDivision hierarchy validation
- `--include-split-analysis` - Include 90/10 split analysis
- `--include-ceiling-analysis` - Include ceiling sales analysis
- `--include-referential` - Include referential integrity checks

## Usage Examples

### 1. Basic Audit Report
```bash
./vendor/bin/sail artisan distribution:generate-audit-report \
  --from-date=2024-01-01 \
  --to-date=2024-01-31
```

### 2. Comprehensive Audit with All Validations
```bash
./vendor/bin/sail artisan distribution:generate-audit-report \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --include-hierarchy \
  --include-split-analysis \
  --include-ceiling-analysis \
  --include-referential
```

### 3. Store Strategy Audit (90/10 Split Focus)
```bash
./vendor/bin/sail artisan distribution:generate-audit-report \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --distribution-type=2 \
  --include-split-analysis
```

### 4. CSV Format Display in Terminal
```bash
./vendor/bin/sail artisan distribution:generate-audit-report \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --format=csv
```

### 5. Export to JSON File
```bash
./vendor/bin/sail artisan distribution:generate-audit-report \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --format=json \
  --export-file=audit_report_jan2024
```

### 6. Export to CSV File (also displays in terminal)
```bash
./vendor/bin/sail artisan distribution:generate-audit-report \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --format=csv \
  --export-file=audit_report_jan2024
```

### 7. Private Pharmacy Ceiling Analysis
```bash
./vendor/bin/sail artisan distribution:generate-audit-report \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --distribution-type=1 \
  --include-ceiling-analysis
```

### 8. Summary Only Report
```bash
./vendor/bin/sail artisan distribution:generate-audit-report \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --summary-only
```

## Report Sections

### 1. Summary Statistics
- Total sales analyzed
- Distribution breakdown by ceiling status
- Error counts by category
- Overall integrity score

### 2. Distribution Integrity Analysis
- Before/after distribution totals comparison
- Quantity, value, and bonus validation
- Distribution pattern analysis
- Ceiling status breakdown

### 3. LineDivision Hierarchy Validation (Optional)
- Temporal relationship validation
- Orphaned division detection
- Circular reference detection
- Hierarchy completeness analysis

### 4. 90/10 Split Analysis (Optional)
- Ratio validation for distributed sales
- Distribution accuracy verification
- Split completeness checks

### 5. Ceiling Sales Processing (Optional)
- Ceiling transition validation
- Private pharmacy processing verification
- Ceiling consistency checks

### 6. Referential Integrity (Optional)
- Orphaned distribution detection
- Missing original sales identification
- Sale reference validation

### 7. Recommendations
- Prioritized action items
- Category-specific recommendations
- Data quality improvement suggestions

## Output Formats

### Table Format (Default)
Displays results in formatted tables with summary statistics and detailed breakdowns.

### JSON Format
Outputs complete audit results as structured JSON for programmatic processing.

### CSV Format
Displays error details in CSV format in the terminal, or exports to CSV file for spreadsheet analysis when used with `--export-file`.

## Export Files

When using `--export-file`, files are saved to:
- JSON: `storage/app/distribution_audit_reports/{filename}_{timestamp}.json`
- CSV: `storage/app/distribution_audit_reports/{filename}_{timestamp}.csv`

## Distribution System Behavior

The audit command validates the correct distribution system behavior where:

### Sale Types and sale_ids Relationships
- **ABOVE sales (ceiling="1")**: Original sales that exceed limits
  - Should have `sale_ids = null`
  - These are the source sales that get split into limited and excess portions
- **BELOW sales (ceiling="0")**: Limited sales created from original ABOVE sales
  - Should have `sale_ids` populated with the ID of the original ABOVE sale
  - Represent the portion within the ceiling limit
- **DISTRIBUTED sales (ceiling="2")**: Excess sales created from original ABOVE sales
  - Should have `sale_ids` populated with the ID of the original ABOVE sale
  - Represent the portion above the ceiling limit that gets distributed

### Example Data Structure
```
Original ABOVE sale (id: 4098079, ceiling: "1", sale_ids: null, quantity: 300)
├── Limited BELOW sale (id: 4128688, ceiling: "0", sale_ids: "4098079", quantity: 100)
└── Excess DISTRIBUTED sale (id: 4128689, ceiling: "2", sale_ids: "4098079", quantity: 200)
```

### Validation Rules
- Sum of BELOW + DISTRIBUTED quantities should equal original ABOVE sale quantity
- All sale_ids references must point to valid ABOVE sales (ceiling="1")
- ABOVE sales should never have sale_ids populated
- BELOW and DISTRIBUTED sales must have valid sale_ids references

## Error Categories

### Distribution Integrity Errors
- Quantity mismatches between original and derived sales
- Value mismatches between original and derived sales
- Bonus mismatches between original and derived sales

### Hierarchy Errors
- Overlapping temporal ranges in LineDivParent
- Orphaned divisions without parent relationships
- Circular references in hierarchy structure

### Split Distribution Errors
- Invalid total ratios (should sum to 1.0)
- Negative ratios in distribution details
- Non-standard 90/10 split ratios for Store strategy
- Quantity/value mismatches between sales and details

### Ceiling Processing Errors
- ABOVE sales incorrectly having sale_ids (should be null)
- BELOW sales missing sale_ids references to original ABOVE sales
- DISTRIBUTED sales missing sale_ids references to original ABOVE sales
- Invalid sale_ids format or values
- Null ceiling values

### Referential Integrity Errors
- BELOW/DISTRIBUTED sales referencing non-existent original sales
- BELOW/DISTRIBUTED sales referencing sales that are not ABOVE status
- Invalid sale_ids format (non-numeric or invalid values)
- Orphaned derived sales without valid original sale references

## Performance Considerations

- Large date ranges may take longer to process
- Use filters to limit scope for better performance
- Consider running during off-peak hours for comprehensive audits
- Export large results to files rather than displaying in terminal

## Integration with Other Commands

This command complements:
- `distribution:validate-integrity` - Basic integrity validation
- `distribution:scan-errors` - Error detection and fixing
- `distribution:rollback` - Distribution rollback operations

## Troubleshooting

### Common Issues

1. **Memory Limits**: For large datasets, increase PHP memory limit
2. **Timeout Issues**: Use filters to reduce scope
3. **Permission Errors**: Ensure storage directory is writable
4. **Missing Dependencies**: Verify all required models and services are available

### Getting Help

```bash
./vendor/bin/sail artisan distribution:generate-audit-report --help
```

## Examples for Different Scenarios

### Daily Audit Check
```bash
./vendor/bin/sail artisan distribution:generate-audit-report \
  --from-date=$(date -d "yesterday" +%Y-%m-%d) \
  --to-date=$(date -d "yesterday" +%Y-%m-%d) \
  --summary-only
```

### Weekly Comprehensive Audit
```bash
./vendor/bin/sail artisan distribution:generate-audit-report \
  --from-date=$(date -d "1 week ago" +%Y-%m-%d) \
  --to-date=$(date -d "yesterday" +%Y-%m-%d) \
  --include-hierarchy \
  --include-split-analysis \
  --include-ceiling-analysis \
  --include-referential \
  --export-file=weekly_audit_$(date +%Y%m%d)
```

### Monthly Store Strategy Audit
```bash
./vendor/bin/sail artisan distribution:generate-audit-report \
  --from-date=2024-01-01 \
  --to-date=2024-01-31 \
  --distribution-type=2 \
  --include-split-analysis \
  --format=json \
  --export-file=monthly_store_audit_$(date +%Y%m)
```
