<?php

use App\Http\Controllers\CountryController;
use Illuminate\Support\Facades\Route;


//resource
Route::get('/countries',[CountryController::class,'index'])->name('show_all_countries');
Route::post('/countries',[CountryController::class,'store'])->name('create_countries');
Route::get('/countries/create',[CountryController::class,'create'])->name('create_countries');
Route::get('/countries/{id}/edit',[CountryController::class,'edit'])->name('edit_countries');
Route::get('/countries/{id}',[CountryController::class,'show'])->name('show_single_countries');
Route::put('/countries/{id}',[CountryController::class,'update'])->name('edit_countries');
Route::delete('/countries/{id}',[CountryController::class,'destroy'])->name('delete_countries');

Route::post('/importcountries', [CountryController::class,'import'])->name('import_countries');
Route::post('/importupdatecountries', [CountryController::class, 'updateByImport'])->name('import_bulk_edit');
// Route::get('/downloadcountry/{filename}', [CountryController::class,'export'])->name('download_template_countries');
Route::get('/exportcountries', [CountryController::class,'exportcountries'])->name('export_xlsx_countries');
Route::get('/exportcountrypdf', [CountryController::class,'exportpdf'])->name('export_pdf_countries');
Route::post('/sendmailcountries', [CountryController::class,'sendmail'])->name('export_email_countries');
Route::get('/exportcountriescsv', [CountryController::class,'exportcsv'])->name('export_csv_countries');
