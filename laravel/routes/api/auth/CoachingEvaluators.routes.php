<?php

//resource

use App\Http\Controllers\Coaching\EvaluatorController;
use Illuminate\Support\Facades\Route;

Route::get('/coaching/evaluators',[EvaluatorController::class,'index'])->name('');
Route::post('/coaching/evaluators',[EvaluatorController::class,'store'])->name('');
Route::get('/coaching/evaluators/{evaluator}',[EvaluatorController::class,'show'])->name('');
Route::put('/coaching/evaluators/{evaluator}',[EvaluatorController::class,'update'])->name('');
Route::delete('/coaching/evaluators/{evaluator}',[EvaluatorController::class,'destroy'])->name('');


// Tools 
Route::post('/coaching/importevaluators', [EvaluatorController::class,'import'])->name('import_evaluators');
Route::get('/coaching/downloadevaluator/{filename}', [EvaluatorController::class,'export'])->name('download_template_evaluators');
Route::get('/coaching/exportevaluators', [EvaluatorController::class,'exportevaluators'])->name('export_xlsx_evaluators');
Route::get('/coaching/exportevaluatorscsv', [EvaluatorController::class,'exportcsv'])->name('export_csv_evaluators');
Route::get('/coaching/exportevaluatorpdf', [EvaluatorController::class,'exportpdf'])->name('export_pdf_evaluators');

Route::get('/coaching/restoreevaluator', [EvaluatorController::class,'restore'])->name('restore_evaluators');

Route::post('/coaching/sendmailevaluators', [EvaluatorController::class,'sendmail'])->name('export_email_evaluators');


