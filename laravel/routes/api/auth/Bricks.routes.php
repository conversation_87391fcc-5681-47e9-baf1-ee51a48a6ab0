<?php

use App\Http\Controllers\BrickAccountController;
use App\Http\Controllers\BrickController;
use App\Http\Controllers\BrickDoctorController;
use App\Http\Controllers\BrickMapPositionController;
use App\Http\Controllers\BrickViewController;
use App\Http\Controllers\CopyAccountsPerBrickController;
use App\Http\Controllers\SharedBricksController;
use Illuminate\Support\Facades\Route;

//resource
Route::get('/bricks',[BrickController::class,'index'])->name('show_all_bricks');
Route::post('/bricks',[BrickController::class,'store'])->name('create_bricks');
Route::get('/bricks/create',[BrickController::class,'create'])->name('create_bricks');
Route::get('/bricks/{id}/edit',[BrickController::class,'edit'])->name('edit_bricks');
Route::get('/bricks/{id}',[BrickController::class,'show'])->name('show_single_bricks');
Route::put('/bricks/{id}',[BrickController::class,'update'])->name('edit_bricks');
Route::delete('/bricks/{id}',[BrickController::class,'destroy'])->name('delete_bricks');

Route::post('/importbricks', [BrickController::class, 'import'])->name('import_bricks');
Route::post('/importbricksunified', [BrickController::class, 'importUnified'])->name('import_bricks_unified');
Route::post('/importupdatebricks', [BrickController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/downloadbrick/{filename}', [BrickController::class, 'export'])->name('download_template_bricks');
Route::get('/exportbricks', [BrickController::class, 'exportbricks'])->name('export_xlsx_bricks');
Route::get('/exportbrickscsv', [BrickController::class, 'exportcsv'])->name('export_csv_bricks');
Route::get('/exportbrickpdf', [BrickController::class, 'exportpdf'])->name('export_pdf_bricks');
Route::post('/sendmailbricks', [BrickController::class, 'sendmail'])->name('export_email_bricks');
Route::get('/restorebrick', [BrickController::class, 'restore'])->name('restore_bricks');


// brick doctors

Route::post('/lines/{line}/divisions/{division}/bricks',[BrickViewController::class,'index'])->name('show_all_brick_view_plans');
// Route::get('/lines/{line}/divisions/{division}/bricks/doctors',[BrickDoctorController::class,'index'])->name('show_all_brick_doctors');
// Route::get('/lines/{line}/divisions/{division}/bricks/accounts',[BrickAccountController::class,'index'])->name('show_all_brick_accounts');

// Brick Positions

Route::get('/bricks-positions',[BrickMapPositionController::class,'index'])->name('');
Route::post('/bricks-positions/{brick}',[BrickMapPositionController::class,'store'])->name('');

// Shared Bricks

Route::get('/get-shared-bricks/{line}/divisions',[SharedBricksController::class,'divisions'])->name('');
Route::get('/get-shared-bricks/{lineDivision}/bricks',[SharedBricksController::class,'bricks'])->name('');
Route::post('/shared-bricks',[SharedBricksController::class,'index'])->name('');
Route::post('/save-shared-bricks',[SharedBricksController::class,'store'])->name('');


// copy accounts

// Route::get('/get-shared-bricks/{line}/divisions',[SharedBricksController::class,'divisions'])->name('');
Route::get('/copy-accounts-bricks/{line}/bricks',[CopyAccountsPerBrickController::class,'bricks'])->name('');
Route::post('/copy-accounts-bricks',[CopyAccountsPerBrickController::class,'index'])->name('');
Route::post('/save-copy-accounts-bricks',[CopyAccountsPerBrickController::class,'store'])->name('');
