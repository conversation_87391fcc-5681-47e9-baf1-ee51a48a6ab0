<?php

use App\Http\Controllers\AccountClassificationController;
use App\Http\Controllers\AccountController;
use App\Http\Controllers\ListTypeController;
use Illuminate\Support\Facades\Route;

//resource
Route::get('/merge-accounts', [AccountController::class, 'mergeAccounts'])->name('');
Route::post('/save-merge-accounts', [AccountController::class, 'saveMergedAccounts'])->name('');
Route::post('/accounts/index', [AccountController::class, 'index'])->name('show_all_accounts');
Route::post('/accounts', [AccountController::class, 'store'])->name('create_accounts');
Route::get('/accounts/create', [AccountController::class, 'create'])->name('create_accounts');
Route::get('/accounts/{id}/edit', [AccountController::class, 'edit'])->name('edit_accounts');
Route::get('/accounts/{id}', [AccountController::class, 'show'])->name('show_single_accounts');
Route::put('/accounts/{id}', [AccountController::class, 'update'])->name('edit_accounts');
Route::delete('/accounts/{id}', [AccountController::class, 'destroy'])->name('delete_accounts');

Route::post('/accounts/{id}/visits/{role}', [AccountController::class, 'getVisits'])->name('show_single_accounts');


Route::post('/importaccounts', [AccountController::class, 'import'])->name('import_accounts');
Route::post('/importupdateaccounts', [AccountController::class, 'updateByImport'])->name('import_bulk_edit');

Route::get('/downloadaccount/{filename}', [AccountController::class, 'export'])->name('download_template_accounts');
Route::get('/exportaccounts', [AccountController::class, 'exportaccounts'])->name('export_xlsx_accounts');
Route::get('/exportaccountscsv', [AccountController::class, 'exportcsv'])->name('export_csv_accounts');
Route::get('/exportaccountpdf', [AccountController::class, 'exportpdf'])->name('export_pdf_accounts');
Route::post('/sendmailaccounts', [AccountController::class, 'sendmail'])->name('export_email_accounts');
Route::get('/restoreaccount', [AccountController::class, 'restore'])->name('restore_accounts');

Route::get('/accounts/{account}/account-lines',[AccountController::class, 'getAccountLines'])->name(''); // TODO: HAS NO PERMISSION YET
Route::get('/accounts/{account}/account-lines/{line}/divisions',[AccountController::class, 'getAccountDivisions'])->name(''); // TODO: HAS NO PERMISSION YET
Route::get('/accounts/{account}/account-divisions/{lineDivision}/bricks',[AccountController::class, 'getAccountBricks'])->name(''); // TODO: HAS NO PERMISSION YET
Route::post('/accounts/{account}/assign-location',[AccountController::class, 'assignAccountLocation'])->name('assign_location_accounts');// TODO: HAS NO PERMISSION YET

Route::get('/list-type', [ListTypeController::class, 'index'])->name('show_all_list_type_settings');
Route::get('/list-type/{id}', [ListTypeController::class, 'show'])->name('show_single_list_type_settings');
Route::put('/list-type/{id}', [ListTypeController::class, 'update'])->name('edit_list_type_settings');




Route::get('/account-classifications', [AccountClassificationController::class, 'index'])->name('show_all_account_classifications');
Route::post('/account-classifications', [AccountClassificationController::class, 'store'])->name('create_account_classifications');
Route::get('/account-classifications/{id}', [AccountClassificationController::class, 'show'])->name('show_single_account_classifications');
Route::put('/account-classifications/{id}', [AccountClassificationController::class, 'update'])->name('edit_account_classifications');
Route::delete('/account-classifications/{id}', [AccountClassificationController::class, 'destroy'])->name('delete_account_classifications');
