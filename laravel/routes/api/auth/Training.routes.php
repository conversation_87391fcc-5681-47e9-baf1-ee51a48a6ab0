<?php

use App\Http\Controllers\CoachingSettingController;
use App\Http\Controllers\QuizSettingController;
use Illuminate\Support\Facades\Route;


// Coaching Settings
Route::get('/coaching-settings', [CoachingSettingController::class, 'index'])->name('show_all_coaching_settings');
Route::get('/coaching-settings/{id}', [CoachingSettingController::class, 'show'])->name('show_single_coaching_settings');
Route::put('/coaching-settings/{id}', [CoachingSettingController::class, 'update'])->name('edit_coaching_settings');



// Quiz Settings
Route::get('/quiz-settings', [QuizSettingController::class, 'index'])->name('show_all_quiz_settings');
Route::get('/quiz-settings/{id}', [QuizSettingController::class, 'show'])->name('show_single_quiz_settings');
Route::put('/quiz-settings/{id}', [QuizSettingController::class, 'update'])->name('edit_quiz_settings');
