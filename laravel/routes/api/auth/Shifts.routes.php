<?php

use App\Http\Controllers\ShiftController;
use Illuminate\Support\Facades\Route;

//resource shifts
Route::get('/shifts',[ShiftController::class,'index'])->name('show_all_shifts');
Route::post('/shifts',[ShiftController::class,'store'])->name('create_shifts');
Route::get('/shifts/create',[ShiftController::class,'create'])->name('create_shifts');
Route::get('/shifts/{id}/edit',[ShiftController::class,'edit'])->name('edit_shifts');
Route::get('/shifts/{id}',[ShiftController::class,'show'])->name('show_single_shifts');
Route::put('/shifts/{id}',[ShiftController::class,'update'])->name('edit_shifts');
Route::delete('/shifts/{id}',[ShiftController::class,'destroy'])->name('delete_shifts');

Route::post('/importshifts', [ShiftController::class, 'import'])->name('import_shifts');
Route::post('/importupdateshifts', [ShiftController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/downloadshift/{filename}', [ShiftController::class, 'export'])->name('download_template_shifts');
Route::get('/exportshifts', [ShiftController::class, 'exportshifts'])->name('export_xlsx_shifts');
Route::get('/exportshiftscsv', [ShiftController::class, 'exportcsv'])->name('export_csv_shifts');
Route::get('/exportshiftpdf', [ShiftController::class, 'exportpdf'])->name('export_pdf_shifts');
Route::post('/sendmailshifts', [ShiftController::class, 'sendmail'])->name('export_email_shifts');
Route::get('/restoreshift', [ShiftController::class, 'restore'])->name('restore_shifts');
