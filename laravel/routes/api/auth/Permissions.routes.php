<?php

use App\Http\Controllers\PermissionController;
use App\Http\Controllers\RolesController;
use Illuminate\Support\Facades\Route;


//resource permissions
Route::get('/permissions',[PermissionController::class,'index'])->name('show_all_permissions');
Route::post('/permissions',[PermissionController::class,'store'])->name('create_permissions');
Route::get('/permissions/create',[PermissionController::class,'create'])->name('create_permissions');
Route::get('/permissions/{id}/edit',[PermissionController::class,'edit'])->name('edit_permissions');
Route::get('/permissions/{id}',[PermissionController::class,'show'])->name('show_single_permissions');
Route::put('/permissions/{id}',[PermissionController::class,'update'])->name('edit_permissions');
Route::delete('/permissions/{id}',[PermissionController::class,'destroy'])->name('delete_permissions');

Route::post('/importpermissions', [PermissionController::class,'import'])->name('import_permissions');
Route::post('/importupdatepermissions', [PermissionController::class, 'updateByImport'])->name('import_bulk_edit');
// Route::get('/downloadpermission/{filename}', [PermissionController::class,'export'])->name('download_template_permissions');
Route::get('/exportpermissions', [PermissionController::class,'exportpermissions'])->name('export_xlsx_permissions');
Route::get('/exportpermissionpdf', [PermissionController::class,'exportpdf'])->name('export_pdf_permissions');
Route::post('/sendmailpermissions', [PermissionController::class,'sendmail'])->name('export_email_permissions');
Route::get('/exportpermissionscsv', [PermissionController::class,'exportcsv'])->name('export_csv_permissions');

//resource roles
Route::get('/roles',[RolesController::class,'index'])->name('show_all_roles');
Route::post('/roles',[RolesController::class,'store'])->name('create_roles');
Route::get('/roles/create',[RolesController::class,'create'])->name('create_roles');
Route::get('/roles/{id}/edit',[RolesController::class,'edit'])->name('edit_roles');
Route::get('/roles/{id}',[RolesController::class,'show'])->name('show_single_roles');
Route::put('/roles/{id}',[RolesController::class,'update'])->name('edit_roles');
Route::delete('/roles/{id}',[RolesController::class,'destroy'])->name('delete_roles');

Route::post('/roles/{role}/permissions/assign', [RolesController::class,'updatePermissions'])->name('assign_role_permissions');
