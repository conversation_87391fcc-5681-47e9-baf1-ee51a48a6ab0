<?php

use App\Exceptions\CrmException;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\UserLineController;
use App\Http\Controllers\UsersController;
use Illuminate\Support\Facades\Route;

Route::post('/importusers', [UsersController::class, 'import'])->name('import_users');
Route::post('/importupdateusers', [UsersController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/downloaduser/{filename}', [UsersController::class, 'export'])->name('download_template_users');
Route::get('/exportusers', [UsersController::class, 'exportusers'])->name('export_xlsx_users');
Route::get('/exportuserpdf', [UsersController::class, 'exportpdf'])->name('export_pdf_users');
Route::post('/sendmailusers', [UsersController::class, 'sendmail'])->name('export_email_users');
Route::get('/exportuserscsv', [UsersController::class, 'exportcsv'])->name('export_csv_users');
Route::post('/changePassword', [UsersController::class, 'changePassword'])->name('change_password');
Route::post('/changeProfilePic', [UsersController::class, 'changeProfilePic'])->name('change_profile_pic');
Route::get('/users/{id}/profile/image', [UsersController::class, 'profileImage'])->name('show_user_profile_image');
Route::get('/user-profile', [ProfileController::class, 'getUserData'])->name('show_single_user_profile');
Route::get('/showEditUserDetails/{id}', [UsersController::class, 'showEditUserDetails'])->name('show_single_user_details');
Route::get("/users/{id}/permissions", [UsersController::class, 'getPermissions'])->name(""); //has no permission

//resource users
Route::post('/users/index', [UsersController::class, 'index'])->name('show_all_users');
Route::post('/users', [UsersController::class, 'store'])->name('create_users');
Route::post('/get-user-history', [UsersController::class, 'userHistory'])->name('create_users');
Route::get('/users/create', [UsersController::class, 'create'])->name('create_users');
Route::get('/users/{id}/edit', [UsersController::class, 'edit'])->name('edit_users');
Route::get('/users/{id}', [UsersController::class, 'show'])->name('show_single_users');
Route::put('/users/{id}', [UsersController::class, 'update'])->name('edit_users');
Route::put('/users/image/{id}', [UsersController::class, 'updateImage'])->name('edit_users');
Route::delete('/users/{id}', [UsersController::class, 'destroy'])->name('delete_users');

Route::post('/user/details', [UsersController::class, 'storeUserDetails'])->name('create_user_details');
Route::put('/user/details/{id}', [UsersController::class, 'updateUserDetail'])->name('edit_user_details');
Route::post('/user/linedivision', [UsersController::class, 'storeUserLineDivision'])->name('create_user_details');
Route::get('/user/linedivision/edit/{lineDivisionUser}', [UsersController::class, 'getEditedUserLineDivision'])->name('');
Route::put('/user/linedivision', [UsersController::class, 'updateUserLineDivisions'])->name('edit_user_details');
Route::delete('/user/linedivision', [UsersController::class, 'destroyUserLineDivisions'])->name('edit_user_details');


Route::get('/users/{user}/in-personate', [UsersController::class, 'inPersonate'])->name('');


Route::get('/users/{user}/lines', [UserLineController::class, 'index'])->name('');








              