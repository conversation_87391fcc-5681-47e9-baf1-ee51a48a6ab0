<?php


use App\Http\Controllers\Analyzer\AccountTypesAnalyzerController;
use App\Http\Controllers\Analyzer\KpisAnalyzerController;
use App\Http\Controllers\Analyzer\MapsAnalyzerController;
use App\Http\Controllers\Analyzer\SalesAnalyzerController;
use App\Http\Controllers\Analyzer\StatisticsAnalyzerController;
use App\Http\Controllers\Analyzer\MedicalRepStatisticsAnalyzerController;
use App\Http\Controllers\TraceController;
use Illuminate\Support\Facades\Route;

Route::post('analyzer/kpis',[KpisAnalyzerController::class,'filter'])->name('');
Route::post('analyzer/sales',[SalesAnalyzerController::class,'filter'])->name('');
Route::post('analyzer/statistics',[StatisticsAnalyzerController::class,'filter'])->name('');
Route::post('analyzer/maps',[MapsAnalyzerController::class,'filter'])->name('');
Route::post('analyzer/maps/traces',[TraceController::class,'index'])->name('');
Route::post('analyzer/visit-statistics',[MedicalRepStatisticsAnalyzerController::class,'index'])->name('');
Route::post('analyzer/account-types-statistics',[AccountTypesAnalyzerController::class,'index'])->name('');
