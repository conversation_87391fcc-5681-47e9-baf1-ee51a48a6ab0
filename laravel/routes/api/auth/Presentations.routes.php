<?php

use App\Http\Controllers\PresentationController;
use Illuminate\Support\Facades\Route;


//resource
Route::get('/presentations', [PresentationController::class, 'index'])->name('show_all_presentations');
Route::post('/presentations', [PresentationController::class, 'store'])->name('create_presentations');
Route::get('/presentations/{id}/edit', [PresentationController::class, 'edit'])->name('edit_presentations');
Route::get('/presentations/{id}', [PresentationController::class, 'show'])->name('show_single_presentations');
Route::put('/presentations/{presentation}', [PresentationController::class, 'update'])->name('edit_presentations');
Route::delete('/presentations/{id}', [PresentationController::class, 'destroy'])->name('delete_presentations');
Route::delete('/presentation/slides/{id}', [PresentationController::class, 'destroySlide'])->name('delete_presentations');

Route::get('/media-types',[PresentationController::class, 'mediaTypes'])->name('');//->name('show_all_mediaTypes');


Route::post('/importpresentations', [PresentationController::class, 'import'])->name('import_presentations');
Route::post('/importproductpresentations', [PresentationController::class, 'importProductPresentation'])->name('import_presentations');
Route::post('/importupdatepresentations', [PresentationController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/downloadpresentation/{filename}', [PresentationController::class, 'export'])->name('download_template_presentations');
Route::get('/exportpresentations', [PresentationController::class, 'exportpresentations'])->name('export_xlsx_presentations');
Route::get('/exportpresentationscsv', [PresentationController::class, 'exportcsv'])->name('export_csv_presentations');
Route::get('/exportpresentationpdf', [PresentationController::class, 'exportpdf'])->name('export_pdf_presentations');
Route::get('/restorepresentation', [PresentationController::class, 'restore'])->name('restore_presentations');

Route::post('sendmailpresentations', [PresentationController::class, 'sendmail'])->name('export_email_presentations');


Route::post('/insert-statistics',[PresentationController::class,'insertStatistics'])->name('');
Route::put('/update-statistics/{statistic}',[PresentationController::class,'updateStatistics'])->name('');
