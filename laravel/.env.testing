APP_NAME=laravel
APP_ENV=testing
APP_KEY=base64:k35p/ov91/L6regRTIMNY/Ic9HqJ9Dv36hz2JKbb2nY=
APP_DEBUG=true
APP_URL=http://localhost:8001
APP_PORT=8001
SERVER_HOST=8001
SERVER_PORT=8001

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=purex_testing
DB_USERNAME=sail
DB_PASSWORD=password

DB_USERNAME_ADMIN=
DB_PASSWORD_ADMIN=

BROADCAST_DRIVER=pusher
CACHE_DRIVER=array
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=array
SESSION_LIFETIME=120

QUEUES_NUMBER=2
QUEUES_SERVICE_NUMBER=2
QUEUES_SERVICE_PROCESSES_NUMBER=2
QUEUE_REDIS_HOST=redis
QUEUE_REDIS_PASSWORD=null
QUEUE_REDIS_PORT=6379
HORIZON_BASIC_AUTH_USERNAME=admin
HORIZON_BASIC_AUTH_PASSWORD=P@ssw0rd

MEMCACHED_HOST=memcached

REDIS_CLIENT=predis
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# MAIL_MAILER=smtp
# MAIL_HOST=mailhog
# MAIL_PORT=1025
# MAIL_USERNAME=null
# MAIL_PASSWORD=null
# MAIL_ENCRYPTION=null
# MAIL_FROM_ADDRESS=null
# MAIL_FROM_NAME="${APP_NAME}"


MAIL_MAILER=smtp
MAIL_HOST=smtp.netfirms.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=Noreply_123
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false


PUSHER_APP_ID=itgates-app
PUSHER_APP_KEY=pulposoft-key
PUSHER_APP_SECRET=pulposoft-key-app-secret
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

VITE_WEBSOCKET_HOST=ws://localhost:3000
VITE_APP_NAME="${APP_NAME}"
VITE_SERVER_TURN=turn_server
VITE_SERVER_STUN=stun_server
VITE_TURN_SERVER_USERNAME=username
VITE_TURN_SERVER_CREDENTIAL=password
VITE_BASE_URL="${APP_URL}"
JWT_SECRET=tvjUQPRUSvlGBW6fOIXFlt88InF1zv6iz37dM8KrzZaY1Pd9HCjJrz7xQ2pcTTHj

UNIFIED_BASE_URL=
UNIFIED_CLIENT_ID=
UNIFIED_CLIENT_SECRET=
UNIFIED_SALT=

OCTANE_SERVER=swoole

# USERS_LIMIT=1000