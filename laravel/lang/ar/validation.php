<?php

use App\ErrorMessages;
use App\Helpers\ErrorMessageFetcher;

$fetcher = new ErrorMessageFetcher();

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'accepted' => $fetcher->getMessage('accepted'),
    'required' => $fetcher->getMessage('required'),
    'active_url' => $fetcher->getMessage('active_url'),
    'after' => $fetcher->getMessage('after') . ':date.',
    'after_or_equal' => $fetcher->getMessage('after_or_equal') . ':date.',
    'alpha' => $fetcher->getMessage('alpha'),
    'alpha_dash' => $fetcher->getMessage('alpha_dash'),
    'alpha_num' => $fetcher->getMessage('alpha_num'),
    'array' => $fetcher->getMessage('array'),
    'before' => $fetcher->getMessage('before') . ':date.',
    'before_or_equal' => $fetcher->getMessage('before_or_equal') . ':date.',
    'between' => [
        'numeric' => $fetcher->getMessage('between') . ':min and :max.',
        'file' => $fetcher->getMessage('between') . ':min and :max kilobytes.',
        'string' => $fetcher->getMessage('between') . ':min and :max characters.',
        'array' => $fetcher->getMessage('between') . ':min and :max items.',
    ],
    'boolean' => $fetcher->getMessage('boolean'),
    'confirmed' => $fetcher->getMessage('confirmed'),
    'date' => $fetcher->getMessage('date'),
    'date_equals' => $fetcher->getMessage('date_equals') . ':date.',
    'date_format' => $fetcher->getMessage('date_format') . ':format.',
    'different' => $fetcher->getMessage('different', [':attribute', ':value']),
    // 'digits' => 'The :attribute must be :digits digits.',
    // 'digits_between' => 'The :attribute must be between :min and :max digits.',
    'dimensions' => $fetcher->getMessage('dimensions'),
    'distinct' => $fetcher->getMessage('distinct'),
    'email' => $fetcher->getMessage('email'),
    'ends_with' => $fetcher->getMessage('ends_with') . ':values',
    'exists' => $fetcher->getMessage('exists'),
    'exists_not_soft_deleted' => $fetcher->getMessage('exists'),
    'file' => $fetcher->getMessage('file'),
    'filled' => $fetcher->getMessage('filled'),
    'gt' => [
        'numeric' => $fetcher->getMessage('gt') . ':value.',
        'file' => $fetcher->getMessage('gt') . ':value kilobytes.',
        'string' => $fetcher->getMessage('gt') . ':value characters.',
        'array' => $fetcher->getMessage('gt') . ':value items.',
    ],
    'gte' => [
        'numeric' => $fetcher->getMessage('gte') . 'or equal :value.',
        'file' => $fetcher->getMessage('gte') . 'or equal :value kilobytes.',
        'string' => $fetcher->getMessage('gte') . 'or equal :value characters.',
        'array' => $fetcher->getMessage('gte') . ':value items or more.',
    ],
    'image' => $fetcher->getMessage('image'),
    'in' => $fetcher->getMessage('in'),
    'in_array' => $fetcher->getMessage('in_array') . ':other.',
    'integer' => $fetcher->getMessage('integer'),
    'ip' => $fetcher->getMessage('ip'),
    'ipv4' => $fetcher->getMessage('ipv4'),
    'ipv6' => $fetcher->getMessage('ipv6'),
    'json' => $fetcher->getMessage('json'),
    'lt' => [
        'numeric' => $fetcher->getMessage('lt') . ':value.',
        'file' => $fetcher->getMessage('lt') . ':value kilobytes.',
        'string' => $fetcher->getMessage('lt') . ':value characters.',
        'array' => $fetcher->getMessage('lt') . ':value items.',
    ],
    'lte' => [
        'numeric' => $fetcher->getMessage('lte') . 'or equal :value.',
        'file' => $fetcher->getMessage('lte') . 'or equal :value kilobytes.',
        'string' => $fetcher->getMessage('lte') . 'or equal :value characters.',
        'array' => $fetcher->getMessage('lte') . ':value items.',
    ],
    'max' => [
        'numeric' => $fetcher->getMessage('max') . ':max.',
        'file' => $fetcher->getMessage('max') . ':max kilobytes.',
        'string' => $fetcher->getMessage('max') . ':max characters.',
        'array' => $fetcher->getMessage('max') . ':max items.',
    ],
    'mimes' => $fetcher->getMessage('mimes') . ':values.',
    'mimetypes' => $fetcher->getMessage('mimes') . ':values.',
    'min' => [
        'numeric' => $fetcher->getMessage('min') . ':min.',
        'file' => $fetcher->getMessage('min') . ':min kilobytes.',
        'string' => $fetcher->getMessage('min') . ':min characters.',
        'array' => $fetcher->getMessage('min') . ':min items.',
    ],
    'not_in' => $fetcher->getMessage('not_in'),
    'not_regex' => $fetcher->getMessage('not_regex'),
    'numeric' => $fetcher->getMessage('numeric'),
    'password' => $fetcher->getMessage('password'),
    'present' => $fetcher->getMessage('present'),
    'regex' => $fetcher->getMessage('regex'),
    'required_if' => $fetcher->getMessage('required') . 'when :other is :value.',
    'required_unless' => $fetcher->getMessage('required') . 'unless :other is in :values.',
    'required_with' => $fetcher->getMessage('required') . 'when :values is present.',
    'required_with_all' => $fetcher->getMessage('required') . 'when :values are present.',
    'required_without' => $fetcher->getMessage('required') . 'when :values is not present.',
    'required_without_all' => $fetcher->getMessage('required') . 'when none of :values are present.',
    'same' => $fetcher->getMessage('same', [':attribute', ':value']),
    'size' => [
        'numeric' => $fetcher->getMessage('size') . ':size.',
        'file' => $fetcher->getMessage('size') . ':size kilobytes.',
        'string' => $fetcher->getMessage('size') . ':size characters.',
        'array' => $fetcher->getMessage('size') . ':size items.',
    ],
    'starts_with' => $fetcher->getMessage('starts_with') . ':values',
    'string' => $fetcher->getMessage('string'),
    'timezone' => $fetcher->getMessage('timezone'),
    'unique' => $fetcher->getMessage('unique'),
    'uploaded' => $fetcher->getMessage('uploaded'),
    'url' => $fetcher->getMessage('url'),
    'uuid' => $fetcher->getMessage('uuid'),

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email". This simply helps us make our message more expressive.
    |
    */

    'attributes' => [],

];
