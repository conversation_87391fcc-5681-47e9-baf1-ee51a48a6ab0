<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('quizzes', function (Blueprint $table) {
            $table->id();
            $table->string('name'); 
            $table->mediumText('desc')->nullable(); 

            $table->unsignedBigInteger('user_id')->unsigned(); 
            $table->foreign('user_id')->references('id')->on('users'); 
            
            $table->unsignedBigInteger('line_id')->unsigned(); 
            $table->foreign('line_id')->references('id')->on('lines'); 
            
            $table->string('time'); 
            $table->string('date'); 
            $table->decimal('quiz_degree'); 
            $table->decimal('success_degree');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('quizzes');
    }
}
;