<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sales_details', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('sale_id');
            $table->unsignedBigInteger('div_id');
            $table->unsignedBigInteger('brick_id')->nullable();
            // $table->unsignedBigInteger('product_id');
            // $table->unsignedBigInteger('line_id');
            // $table->unsignedBigInteger('distributor_id');
            $table->date('date');
            $table->Integer('quantity');
            $table->unsignedInteger('bonus');
            $table->decimal('value');
            $table->unsignedInteger('file_id')->nullable();
            $table->timestamps();

            $table->foreign('sale_id')->references('id')->on('sales');
            $table->foreign('div_id')->references('id')->on('line_divisions');
            $table->foreign('brick_id')->references('id')->on('bricks');
            // $table->foreign('line_id')->references('id')->on('lines');
            // $table->foreign('product_id')->references('id')->on('products');
            // $table->foreign('distributor_id')->references('id')->on('distributors');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sales_details');
    }
}
;