<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('planned_visits', function (Blueprint $table) {
            $table->timestamp('visit_date')->nullable()->default(null)->change();
        });
        Schema::table('actual_visits', function (Blueprint $table) {
            $table->timestamp('visit_date')->nullable()->default(null)->change();
        });
        Schema::table('ow_plan_visits', function (Blueprint $table) {
            $table->timestamp('day')->nullable()->default(null)->change();
        });
        Schema::table('vacations', function (Blueprint $table) {
            $table->timestamp('from_date')->nullable()->default(null)->change();
        });
        Schema::table('ow_actual_visits', function (Blueprint $table) {
            $table->timestamp('date')->nullable()->default(null)->change();
        });
        Schema::table('contributions', function (Blueprint $table) {
            $table->timestamp('date')->nullable()->default(null)->change();
        });
        Schema::table('targets', function (Blueprint $table) {
            $table->timestamp('date')->nullable()->default(null)->change();
        });
        Schema::table('target_details', function (Blueprint $table) {
            $table->timestamp('date')->nullable()->default(null)->change();
        });
        Schema::table('mappings', function (Blueprint $table) {
            $table->timestamp('from_date')->nullable()->default(null)->change();
        });
        Schema::table('tasks', function (Blueprint $table) {
            $table->timestamp('date')->nullable()->default(null)->change();
        });
        Schema::table('personal_requests', function (Blueprint $table) {
            $table->timestamp('date')->nullable()->default(null)->change();
        });
        Schema::table('expense_details', function (Blueprint $table) {
            $table->timestamp('date')->nullable()->default(null)->change();
        });
        Schema::table('announcements', function (Blueprint $table) {
            $table->timestamp('from_date')->nullable()->default(null)->change();
        });
        Schema::table('expense_locations', function (Blueprint $table) {
            $table->timestamp('date')->nullable()->default(null)->change();
        });
        Schema::table('expense_price_factors', function (Blueprint $table) {
            $table->timestamp('from_date')->nullable()->default(null)->change();
        });
        Schema::table('sale_factor_types', function (Blueprint $table) {
            $table->timestamp('from_date')->nullable()->default(null)->change();
        });
        Schema::table('materials', function (Blueprint $table) {
            $table->timestamp('date')->nullable()->default(null)->change();
        });
        Schema::table('fuel_expenses', function (Blueprint $table) {
            $table->timestamp('date')->nullable()->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('planned_visits', function (Blueprint $table) {
            $table->timestamp('visit_date')->change();
        });
        Schema::table('actual_visits', function (Blueprint $table) {
            $table->timestamp('visit_date')->change();
        });
        Schema::table('ow_plan_visits', function (Blueprint $table) {
            $table->timestamp('day')->change();
        });
        Schema::table('vacations', function (Blueprint $table) {
            $table->timestamp('from_date')->change();
        });
        Schema::table('ow_actual_visits', function (Blueprint $table) {
            $table->timestamp('date')->change();
        });
        Schema::table('contributions', function (Blueprint $table) {
            $table->timestamp('date')->change();
        });
        Schema::table('targets', function (Blueprint $table) {
            $table->timestamp('date')->change();
        });
        Schema::table('mappings', function (Blueprint $table) {
            $table->timestamp('from_date')->change();
        });
        Schema::table('target_details', function (Blueprint $table) {
            $table->timestamp('date')->nullable()->default(null)->change();
        });
        Schema::table('tasks', function (Blueprint $table) {
            $table->timestamp('date')->change();
        });
        Schema::table('personal_requests', function (Blueprint $table) {
            $table->timestamp('date')->change();
        });
        Schema::table('expense_details', function (Blueprint $table) {
            $table->timestamp('date')->change();
        });
        Schema::table('announcements', function (Blueprint $table) {
            $table->timestamp('from_date')->change();
        });
        Schema::table('expense_locations', function (Blueprint $table) {
            $table->timestamp('date')->change();
        });
        Schema::table('expense_price_factors', function (Blueprint $table) {
            $table->timestamp('from_date')->change();
        });
        Schema::table('sale_factor_types', function (Blueprint $table) {
            $table->timestamp('from_date')->change();
        });
        Schema::table('materials', function (Blueprint $table) {
            $table->timestamp('date')->change();
        });
        Schema::table('fuel_expenses', function (Blueprint $table) {
            $table->timestamp('date')->change();
        });
    }
}
;