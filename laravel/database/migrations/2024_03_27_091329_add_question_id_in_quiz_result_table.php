<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('quiz_result', function (Blueprint $table) {
            $table->unsignedBigInteger('question_id')->nullable()->after('user_id');
            $table->foreign('question_id')->references('id')->on('quiz_questions')->nullOnDelete();
            $table->unsignedBigInteger('answer_id')->nullable()->after('quiz_id');
            $table->foreign('answer_id')->references('id')->on('quiz_answers')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('quiz_result', function (Blueprint $table) {
            //
        });
    }
};
