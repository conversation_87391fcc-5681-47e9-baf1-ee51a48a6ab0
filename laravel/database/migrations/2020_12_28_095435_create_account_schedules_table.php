<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('account_schedules', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('account_id')->nullable();
            $table->integer('day')->nullable();
            $table->timestamp('open_from_time')->nullable();
            $table->timestamp('open_to_time')->nullable();
            $table->timestamp('visit_from_time')->nullable();
            $table->timestamp('visit_to_time')->nullable();
            $table->unsignedInteger('file_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('account_schedules');
    }
}
;