<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('materials', function (Blueprint $table) {
            $table->unsignedBigInteger('material_type_id')->unsigned()->after('date');
            $table->foreign('material_type_id')->references('id')->on('promotional_material_types');
            $table->integer('quantity')->after('material_type_id');
            $table->decimal('amount')->after('quantity');
            $table->string('vendor')->nullable()->after('amount');
            $table->text('description')->nullable()->after('vendor');
        });
        Schema::table('material_details', function (Blueprint $table) {
            $table->dropConstrainedForeignId('material_type_id');
            $table->dropConstrainedForeignId('material_id');
            $table->dropConstrainedForeignId('line_id');
            $table->dropColumn('qty');
            $table->dropColumn('vendor');
            $table->dropColumn('description');
            $table->dropColumn('amount');
        });
        Schema::table('material_details', function (Blueprint $table) {
            $table->unsignedBigInteger('material_id')->after('id');
            $table->foreign('material_id')->references('id')->on('materials');
            $table->unsignedBigInteger('line_id')->after('material_id');
            $table->foreign('line_id')->references('id')->on('lines');
        });
        Schema::table('material_product', function (Blueprint $table) {
            $table->unsignedBigInteger('material_id')->after('product_id');
            $table->foreign('material_id')->references('id')->on('materials')->cascadeOnDelete();
        });
        Schema::rename('material_details', 'material_lines');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('materials', function (Blueprint $table) {
            //
        });
    }
};
