<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('plan_visit_details', function (Blueprint $table) {
            $table->unsignedBigInteger('created_by')->after('approval')->nullable();
            $table->foreign('created_by')->references('id')->on('users')->cascadeOnDelete();
            $table->timestamp('date')->nullable()->after('created_by');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('plan_visit_details', function (Blueprint $table) {
            $table->dropColumn('created_by');
            $table->dropColumn('date');
        });
    }
}
;