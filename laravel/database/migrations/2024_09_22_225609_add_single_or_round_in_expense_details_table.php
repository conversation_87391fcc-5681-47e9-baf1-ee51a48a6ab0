<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('expense_details', function (Blueprint $table) {
            $table->unsignedBigInteger('single_or_round_id')->nullable()->after('amount');
            $table->foreign('single_or_round_id')->references('id')->on('distances')->onDelete('set null');
            $table->unsignedBigInteger('overnight_id')->nullable()->after('single_or_round_id');
            $table->foreign('overnight_id')->references('id')->on('over_nights')->onDelete('set null');
            $table->timestamp('return_date')->nullable()->after('overnight_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('expense_details', function (Blueprint $table) {
            //
        });
    }
};
