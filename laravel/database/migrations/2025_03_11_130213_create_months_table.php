<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('months', function (Blueprint $table) {
            $table->id();
            $table->string('name', 20);
            $table->integer('sort');
        });

        $months = [
            ['name' => 'January', 'sort' => 1],
            ['name' => 'February', 'sort' => 2],
            ['name' => 'March', 'sort' => 3],
            ['name' => 'April', 'sort' => 4],
            ['name' => 'May', 'sort' => 5],
            ['name' => 'June', 'sort' => 6],
            ['name' => 'July', 'sort' => 7],
            ['name' => 'August', 'sort' => 8],
            ['name' => 'September', 'sort' => 9],
            ['name' => 'October', 'sort' => 10],
            ['name' => 'November', 'sort' => 11],
            ['name' => 'December', 'sort' => 12],
        ];

        DB::table('months')->insert($months);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('months');
    }
};
