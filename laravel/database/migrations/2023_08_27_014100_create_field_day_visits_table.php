<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('field_day_visits', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('field_day_id')->nullable();
            $table->unsignedBigInteger('visit_type_id')->nullable();
            $table->unsignedBigInteger('shift_id')->nullable();
            $table->integer('count');
            $table->foreign('field_day_id')->references('id')->on('field_day_kpis')->cascadeOnDelete();
            $table->foreign('visit_type_id')->references('id')->on('visit_types')->cascadeOnDelete();
            $table->foreign('shift_id')->references('id')->on('shifts')->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('field_day_visits');
    }
}
;