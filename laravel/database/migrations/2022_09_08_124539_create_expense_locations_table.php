<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('expense_locations', function (Blueprint $table) {
            $table->id();
            $table->timestamp('date');
            $table->unsignedBigInteger('expense_location_price_id');
            $table->unsignedBigInteger('user_id');
            $table->foreign('expense_location_price_id')->references('id')->on('expense_location_prices');
            $table->foreign('user_id')->references('id')->on('users');
            $table->string('first_attach')->nullable();
            $table->string('second_attach')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('expense_locations');
    }
}
;