<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('new_account_doctors', function (Blueprint $table) {
            $table->index(['account_id', 'account_lines_id', 'line_id', 'deleted_at'],'idx_new_account_doctors_composite');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('new_account_doctors', function (Blueprint $table) {
            $table->dropIndex('idx_new_account_doctors_composite');
        });
    }
};
