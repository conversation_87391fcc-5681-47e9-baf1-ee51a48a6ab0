<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('branch_sales', function (Blueprint $table) {
            $table->id();
            $table->decimal('quantity',12,5);
            $table->decimal('value',15,5);
            $table->integer('region')->nullable();
            $table->decimal('bonus',8,1);
            $table->timestamp('date');
            $table->foreignId('branch_mapping_id')->constrained('branch_mappings')->cascadeOnDelete();
            $table->foreignId('line_id')->constrained('lines')->cascadeOnDelete();
            $table->foreignId('div_id')->constrained('line_divisions')->cascadeOnDelete();
            $table->foreignId('distributor_id')->constrained('distributors')->cascadeOnDelete();
            $table->foreignId('product_id')->constrained('products')->cascadeOnDelete();
            $table->foreignId('file_id')
                ->nullable()
                ->constrained('files_imported')
                ->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branch_sales');
    }
};
