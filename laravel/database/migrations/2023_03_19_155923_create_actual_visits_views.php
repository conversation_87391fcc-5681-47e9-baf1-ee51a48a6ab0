<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement(
            'CREATE OR REPLACE VIEW actual_visits_view AS SELECT DISTINCT
                    xactualvisits.visit_date AS actual_visit_date,
                    xactualvisits.ll AS actual_visit_latitude,
                    xactualvisits.lg AS actual_visit_longitude,
                    xactualvisits.ll_start AS actual_visit_latitude_start,
                    xactualvisits.lg_end AS actual_visit_longitude_end,
                    xactualvisits.visit_duration AS actual_visit_duration,
                    xactualvisits.visit_deviation AS actual_visit_deviation,
                    xactualvisits.visit_deviation_web AS actual_visit_deviation_web,
                    xactualvisits.visit_address AS actual_visit_address,
                    xaccount.name AS account_name,
                    xaccount.address AS account_address,
                    xaccount.mobile AS account_mobile,
                    xaccount.email AS account_email,
                    xaccount.tel AS account_telephone,
                    xaccount.website_link AS account_website_link,
                    xaccount.notes AS account_notes,
                    xaccount.active_date AS account_active_date,
                    xaccount.inactive_date AS account_inactive_date,
                    xaccount.ll AS account_latitude,
                    xaccount.lg AS account_longitude,
                    xdoctor.gender AS doctor_gender,
                    xdoctor.ucode AS doctor_ucode,
                    xdoctor.name AS doctor_name,
                    xdoctor.tel AS doctor_telephone,
                    xdoctor.mobile AS doctor_mobile,
                    xdoctor.email AS doctor_email,
                    xdoctor.dob AS doctor_date_of_birth,
                    xdoctor.dom AS doctor_date_of_marriage,
                    xdoctor.active_date AS doctor_active_date,
                    xdoctor.inactive_date AS doctor_inactive_date,
                    xlines.name AS line_name,
                    xlines.notes AS line_notes,
                    xlines.sort AS line_sort,
                    xlines.from_date AS line_from_date,
                    xlines.to_date AS line_to_date,
                    xlinedivisions.name AS division_name,
                    xlinedivisions.from_date AS division_from,
                    xlinedivisions.to_date AS division_to,
                    xusers.emp_code AS user_emp_code,
                    xusers.name AS user_name,
                    xusers.fullname AS user_full_name,
                    xusers.email AS user_email,
                    xusers.personal_email AS user_personal_email,
                    xusers.menuroles AS user_menuroles,
                    xusers.status AS user_status,
                    xbricks.name AS brick_name,
                    xbricks.notes AS brick_notes,
                    xbricks.sort AS brick_sort,
                    -- xspecialities.name AS speciality_name,
                    -- xspecialities.notes AS speciality_notes,
                    -- xspecialities.sort AS speciality_sort,
                    xacctypes.name AS account_type_name,
                    xacctypes.notes AS account_type_notes,
                    xacctypes.sort AS account_type_sort,
                    xvisittypes.name AS visit_type_name
            FROM
                crm_actual_visits AS xactualvisits
            INNER JOIN crm_accounts AS xaccount
            ON
                xactualvisits.account_id = xaccount.id
            INNER JOIN crm_doctors AS xdoctor
            ON
                xactualvisits.account_dr_id = xdoctor.id
            INNER JOIN crm_line_divisions AS xlinedivisions
            ON
                xactualvisits.div_id = xlinedivisions.id
            INNER JOIN crm_lines AS xlines
            ON
                xactualvisits.line_id = xlines.id
            INNER JOIN crm_account_types AS xacctypes
            ON
                xactualvisits.acc_type_id = xacctypes.id
            INNER JOIN crm_users AS xusers
            ON
                xactualvisits.user_id = xusers.id
            INNER JOIN crm_bricks AS xbricks
            ON
                xactualvisits.brick_id = xbricks.id
            -- INNER JOIN crm_specialities AS xspecialities
            -- ON
            --     xactualvisits.speciality_id = xspecialities.id
            INNER JOIN crm_visit_types AS xvisittypes
            ON
                xactualvisits.visit_type_id = xvisittypes.id;
        ');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement('DROP VIEW IF  EXISTS actual_visits_view');
    }
}
;
