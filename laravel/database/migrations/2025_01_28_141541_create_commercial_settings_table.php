<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('commercial_settings', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('key');
            $table->text('value')->nullable();
            $table->json('options')->nullable();
            $table->enum('type', ['text', 'number', 'select', 'date', 'checkbox'])->default('text');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('commercial_settings');
    }
};
