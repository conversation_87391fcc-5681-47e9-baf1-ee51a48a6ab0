<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('planned_visits', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->change();
            $table->unsignedBigInteger('visit_type')->change();
            $table->unsignedBigInteger('line_id')->change();
            $table->unsignedBigInteger('div_id')->change();
            $table->unsignedBigInteger('account_id')->change();
            $table->unsignedBigInteger('account_dr_id')->nullable()->change();
            $table->unsignedBigInteger('shift_id')->nullable()->change();
            $table->unsignedBigInteger('related_visit_id')->nullable()->change();
            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();
            $table->foreign('visit_type')->references('id')->on('visit_types')->cascadeOnDelete();
            $table->foreign('line_id')->references('id')->on('lines')->cascadeOnDelete();
            $table->foreign('div_id')->references('id')->on('line_divisions')->cascadeOnDelete();
            $table->foreign('account_id')->references('id')->on('accounts')->cascadeOnDelete();
            $table->foreign('account_dr_id')->references('id')->on('doctors')->nullOnDelete();
            $table->foreign('shift_id')->references('id')->on('shifts')->nullOnDelete();
            $table->foreign('related_visit_id')->references('id')->on('planned_visits')->nullOnDelete();
            $table->index('deleted_at');
            $table->index('visit_date');
            $table->index(['shift_id','visit_date','user_id','deleted_at'],'crm_planned_visits_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('planned_visits', function (Blueprint $table) {
            $table->dropIndex('crm_planned_visits_index');
        });
    }
}
;