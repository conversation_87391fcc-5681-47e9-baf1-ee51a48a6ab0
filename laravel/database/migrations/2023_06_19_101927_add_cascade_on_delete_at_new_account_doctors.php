<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('new_account_doctors', function (Blueprint $table) {
            $table->dropForeign('crm_new_account_doctors_account_lines_id_foreign');

            $table->foreign('account_lines_id')
                ->references('id')->on('account_lines')
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('new_account_doctors', function (Blueprint $table) {
            $table->dropForeign('crm_new_account_doctors_account_lines_id_foreign');
        });
    }
}
;