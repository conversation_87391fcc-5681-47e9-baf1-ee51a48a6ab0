<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_positions', function (Blueprint $table) {
            $table->boolean('show_below_users')->after('position_id')->default(1);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_positions', function (Blueprint $table) {
            $table->dropColumn('show_below_users');
        });
    }
};
