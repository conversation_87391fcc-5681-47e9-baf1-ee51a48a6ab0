<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('help_articles', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->mediumText('content')->nullable();
            $table->bigInteger('parent_topic_id')->unsigned();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('parent_topic_id')->references('id')->on('help_topics');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('help_articles');
    }
}
;