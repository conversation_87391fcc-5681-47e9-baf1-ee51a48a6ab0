<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('actual_visits', function (Blueprint $table) {
            $table->timestamp('end_visit_date')->after('visit_date')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('actual_visits', function (Blueprint $table) {
            $table->dropColumn('end_visit_date');
        });
    }
};
