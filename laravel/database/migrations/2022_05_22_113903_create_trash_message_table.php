<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('trash_message', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger("message_id");
            $table->unsignedBigInteger("user_id");
            $table->timestamps();
            $table->foreign("message_id")->references("id")->on("messages")->cascadeOnDelete();
            $table->foreign("user_id")->references("id")->on("users")->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('trash_message');
    }
}
;