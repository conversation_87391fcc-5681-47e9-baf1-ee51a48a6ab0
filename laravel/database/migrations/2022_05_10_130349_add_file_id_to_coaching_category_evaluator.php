<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('coaching_category_evaluator', function (Blueprint $table) {
            $table->unsignedInteger('file_id')->after('evaluator_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('coaching_category_evaluator', function (Blueprint $table) {
            $table->dropColumn('file_id');
        });
    }
}
;