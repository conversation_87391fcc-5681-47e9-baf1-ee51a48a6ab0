<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('service_completes', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->nullable()->after('invoice_amount');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });
        Schema::table('end_commercial_payments', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->nullable()->after('invoice_amount');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('service_completes', function (Blueprint $table) {
            //
        });
    }
};
