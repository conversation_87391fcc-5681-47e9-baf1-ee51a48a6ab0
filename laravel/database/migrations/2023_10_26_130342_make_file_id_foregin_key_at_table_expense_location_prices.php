<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('expense_location_prices', function (Blueprint $table) {
            // $table->dropForeign('file_id');
            // $table->dropColumn('file_id');
            // $table->unsignedBigInteger('file_id')->nullable();
            // $table->foreign('file_id')->references('id')->on('files_imported')->cascadeOnDelete();
            $table->unsignedBigInteger('file_id')->change();
            if (foreignExists("expense_location_prices", "file_id")) {
                $table->dropForeign('crm_expense_location_prices_file_id_foreign');
            }
            $table->foreign('file_id')->references('id')->on('files_imported')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('expense_location_prices', function (Blueprint $table) {
            //
        });
    }
}
;