<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('presentation_slides', function (Blueprint $table) {
            $table->dropForeign(['presentation_id']);

            // Add it back with cascade delete
            $table->foreign('presentation_id')
                ->references('id')
                ->on('presentations')
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('presentation_slides', function (Blueprint $table) {
            // In case of rollback, drop the cascade constraint
            $table->dropForeign(['presentation_id']);

            // Add back the original foreign key without cascade
            $table->foreign('presentation_id')
                ->references('id')
                ->on('presentations');
        });
    }
};
