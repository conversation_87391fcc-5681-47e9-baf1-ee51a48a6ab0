<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('article_keyword', function (Blueprint $table) {
            $table->bigInteger('article_id')->unsigned();
            $table->bigInteger('keyword_id')->unsigned();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('article_id')->references('id')->on('help_articles');
            $table->foreign('keyword_id')->references('id')->on('help_keywords');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('article_keyword');
    }
}
;