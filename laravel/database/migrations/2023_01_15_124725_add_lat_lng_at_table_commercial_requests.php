<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('commercial_requests', function (Blueprint $table) {
            $table->string('ll')->after('to_date')->nullable();
            $table->string('lg')->after('ll')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('commercial_requests', function (Blueprint $table) {
            $table->dropColumn('ll');
            $table->dropColumn('lg');
        });
    }
}
;