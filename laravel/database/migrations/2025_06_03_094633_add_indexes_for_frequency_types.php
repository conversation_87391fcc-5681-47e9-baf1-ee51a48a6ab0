<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
         // Add the same optimization to other frequency tables
        Schema::table('class_frequencies', function (Blueprint $table) {
            $table->unsignedSmallInteger('year')->after('date')->nullable();
            $table->unsignedTinyInteger('month')->after('year')->nullable();
        });

        DB::statement('UPDATE crm_class_frequencies SET year = YEAR(date), month = MONTH(date) WHERE date IS NOT NULL');

        Schema::table('class_frequencies', function (Blueprint $table) {
            $table->index(['class_id', 'line_id', 'year', 'month'], 'idx_class_freq_composite');
            $table->index(['line_id', 'year', 'month'], 'idx_class_freq_line_date');
            $table->index(['deleted_at'], 'idx_class_freq_deleted');
        });

        Schema::table('speciality_frequencies', function (Blueprint $table) {
            $table->unsignedSmallInteger('year')->after('date')->nullable();
            $table->unsignedTinyInteger('month')->after('year')->nullable();
        });

        DB::statement('UPDATE crm_speciality_frequencies SET year = YEAR(date), month = MONTH(date) WHERE date IS NOT NULL');

        Schema::table('speciality_frequencies', function (Blueprint $table) {
            $table->index(['speciality_id', 'line_id', 'year', 'month'], 'idx_speciality_freq_composite');
            $table->index(['line_id', 'year', 'month'], 'idx_speciality_freq_line_date');
        });

        Schema::table('speciality_class_frequencies', function (Blueprint $table) {
            $table->unsignedSmallInteger('year')->after('date')->nullable();
            $table->unsignedTinyInteger('month')->after('year')->nullable();
        });

        DB::statement('UPDATE crm_speciality_class_frequencies SET year = YEAR(date), month = MONTH(date) WHERE date IS NOT NULL');

        Schema::table('speciality_class_frequencies', function (Blueprint $table) {
            $table->index(['speciality_id', 'class_id', 'line_id', 'year', 'month'], 'idx_spec_class_freq_composite');
            $table->index(['line_id', 'year', 'month'], 'idx_spec_class_freq_line_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop indexes and columns in reverse order
        Schema::table('speciality_class_frequencies', function (Blueprint $table) {
            $table->dropIndex('idx_spec_class_freq_composite');
            $table->dropIndex('idx_spec_class_freq_line_date');
            $table->dropColumn(['year', 'month']);
        });

        Schema::table('speciality_frequencies', function (Blueprint $table) {
            $table->dropIndex('idx_speciality_freq_composite');
            $table->dropIndex('idx_speciality_freq_line_date');
            $table->dropColumn(['year', 'month']);
        });

        Schema::table('class_frequencies', function (Blueprint $table) {
            $table->dropIndex('idx_class_freq_composite');
            $table->dropIndex('idx_class_freq_line_date');
            $table->dropIndex('idx_class_freq_deleted');
            $table->dropColumn(['year', 'month']);
        });
    }
};
