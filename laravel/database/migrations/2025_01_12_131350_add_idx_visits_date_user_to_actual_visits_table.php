<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('actual_visits', function (Blueprint $table) {
            $table->index(['visit_date', 'user_id','deleted_at'],'idx_visits_date_user');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('actual_visits', function (Blueprint $table) {
            $table->dropIndex('idx_visits_date_user');
        });
    }
};
