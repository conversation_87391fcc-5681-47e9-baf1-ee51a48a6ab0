<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('actual_visits', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('plan_id')->nullable();
            $table->unsignedInteger('user_id');
            $table->unsignedInteger('line_id');
            $table->unsignedInteger('div_id');
            $table->unsignedInteger('brick_id');
            $table->unsignedInteger('acc_type_id');
            $table->unsignedInteger('account_id');
            $table->unsignedInteger('account_dr_id');
            $table->unsignedInteger('visit_type_id');
            $table->timestamp('visit_date');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('actual_visits');
    }
};
