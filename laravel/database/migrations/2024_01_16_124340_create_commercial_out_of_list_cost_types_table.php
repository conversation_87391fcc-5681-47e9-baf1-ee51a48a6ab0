<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('commercial_out_of_list_cost_types', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('request_id');
            $table->foreign('request_id')->references('id')->on('commercial_requests');
            $table->unsignedBigInteger('cost_type_id');
            $table->foreign('cost_type_id')->references('id')->on('cost_types');
            $table->unsignedBigInteger('out_of_list_id');
            $table->foreign('out_of_list_id')->references('id')->on('commercial_out_of_lists');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('commercial_out_of_list_cost_types');
    }
}
;