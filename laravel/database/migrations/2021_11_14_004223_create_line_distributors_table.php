<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('line_distributors', function (Blueprint $table) {
            $table->id();
            $table->unique(['line_id','distributor_id']);
            $table->unsignedBigInteger('line_id');
            $table->unsignedBigInteger('distributor_id');
            $table->timestamp('from_date')->nullable();
            $table->timestamp('to_date')->nullable();
            $table->unsignedInteger('file_id')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('line_id')->references('id')->on('lines')->cascadeOnDelete();
            $table->foreign('distributor_id')->references('id')->on('distributors')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('line_distributors');
    }
}
;