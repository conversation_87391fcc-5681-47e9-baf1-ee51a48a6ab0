<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('approval_settings', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('key');
            $table->text('value')->nullable();
            $table->enum('type', ['text', 'number', 'email', 'textarea', 'map', 'select', 'file', 'url', 'date', 'checkbox'])->default('text');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('approval_settings');
    }
}
;