<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plan_visit_details', function (Blueprint $table) {
            $table->boolean('status_active_inactive')->nullable()->after('date');
            $table->unsignedBigInteger('account_lines_id')->nullable()->after('status_active_inactive');
            $table->foreign('account_lines_id')->references('id')->on('account_lines')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plan_visit_details', function (Blueprint $table) {
            $table->dropColumn('status_active_inactive');
            $table->dropForeign('crm_account_lines_account_lines_id_foreign');
            $table->dropColumn('account_lines_id');
        });
    }
};
