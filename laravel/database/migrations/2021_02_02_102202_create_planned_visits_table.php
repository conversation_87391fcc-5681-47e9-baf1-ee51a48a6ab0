<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('planned_visits', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id');
            $table->unsignedInteger('div_id');
            $table->unsignedInteger('account_id');
            $table->unsignedInteger('account_dr_id');
            $table->unsignedInteger('shift_id');
            $table->unsignedInteger('related_visit_id');
            $table->tinyInteger('visit_type');
            $table->timestamp('visit_date');
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('planned_visits');
    }
}
;