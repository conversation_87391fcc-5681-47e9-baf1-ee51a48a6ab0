<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('quiz_questions', function (Blueprint $table) {
            $table->id(); 
            $table->text('name');
            $table->text('desc_answer');
            
            $table->unsignedBigInteger('quiz_question_level_id')->unsigned();
            $table->foreign('quiz_question_level_id')->references('id')->on('quiz_question_levels'); 

            $table->unsignedBigInteger('quiz_category_id')->unsigned();
            $table->foreign('quiz_category_id')->references('id')->on('quiz_categories'); 
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('quiz_questions');
    }
}
;