<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('approval_levels', function (Blueprint $table) {
            $table->id();
            $table->foreignId('approval_flow_id')->constrained()->onDelete('cascade');
            $table->integer('level_number')->comment('Sequential level number in the approval hierarchy');
            $table->integer('time_limit')->comment('Time limit in hours for this approval level');
            $table->enum('approver_type', ['role', 'user', 'department'])->comment('Type of approver');
            $table->unsignedBigInteger('approver_id')->comment('ID of the approver (role_id, user_id, or department_id)');
            $table->boolean('can_override')->default(false)->comment('Whether this level can override prior rejections');
            $table->boolean('is_required')->default(true)->comment('Whether this level is mandatory');
            $table->timestamps();
            
            // Ensure levels are unique within a flow
            $table->unique(['approval_flow_id', 'level_number']);
        });
        
        Schema::create('requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('approval_flow_id')->constrained();
            $table->foreignId('user_id')->constrained()->comment('User who submitted the request');
            $table->string('request_type')->comment('Type of request (can be used for polymorphic relations)');
            $table->unsignedBigInteger('request_id')->nullable()->comment('ID of the related request entity');
            $table->json('data')->nullable()->comment('Request-specific data in JSON format');
            $table->enum('status', ['submitted', 'in_progress', 'approved', 'rejected', 'canceled'])->default('submitted');
            $table->timestamp('submitted_at')->useCurrent();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('expires_at')->nullable()->comment('When the total process time limit expires');
            $table->timestamps();
            
            // Add index for polymorphic relation
            $table->index(['request_type', 'request_id']);
        });
        
        Schema::create('approval_actions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('request_id')->constrained()->onDelete('cascade');
            $table->foreignId('approval_level_id')->constrained();
            $table->foreignId('user_id')->constrained()->comment('User who performed the action');
            $table->enum('action', ['approve', 'reject', 'undo_approve', 'undo_reject', 'escalate']);
            $table->text('comments')->nullable();
            $table->timestamp('action_at')->useCurrent();
            $table->timestamps();
        });
        
        Schema::create('approval_escalations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('approval_level_id')->constrained()->onDelete('cascade');
            $table->foreignId('primary_approver_id')->constrained('users');
            $table->foreignId('secondary_approver_id')->nullable()->constrained('users');
            $table->foreignId('tertiary_approver_id')->nullable()->constrained('users');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('approval_escalations');
        Schema::dropIfExists('approval_actions');
        Schema::dropIfExists('requests');
        Schema::dropIfExists('approval_levels');
    }
};