<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('actual_visits', function (Blueprint $table) {
            $table->unsignedInteger('account_dr_id')->after('account_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('actual_visits', function (Blueprint $table) {
            $table->unsignedInteger('account_dr_id')->nullable(false)->change();
        });
    }
}
;