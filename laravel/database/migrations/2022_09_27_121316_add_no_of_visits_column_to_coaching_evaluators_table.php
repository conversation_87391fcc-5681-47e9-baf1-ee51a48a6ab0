<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('coaching_evaluators', function (Blueprint $table) {
            $table->integer('no_of_visits')->nullable()->after('evaluatorable_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('coaching_evaluators', function (Blueprint $table) {
            $table->dropColumn('no_of_visits');
        });
    }
}
;