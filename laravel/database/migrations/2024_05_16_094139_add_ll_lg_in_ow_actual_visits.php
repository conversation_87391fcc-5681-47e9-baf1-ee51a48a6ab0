<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ow_actual_visits', function (Blueprint $table) {
            $table->string('ll')->nullable()->after('user_id');
            $table->string('lg')->nullable()->after('ll');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ow_actual_visits', function (Blueprint $table) {
            $table->dropColumn('ll');
            $table->dropColumn('lg');
        });
    }
};
