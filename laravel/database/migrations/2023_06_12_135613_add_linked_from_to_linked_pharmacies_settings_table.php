<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('linked_pharmacies_settings', function (Blueprint $table) {
            $table->enum('linked_from', ['list', 'mapping', 'unified', 'manual'])->after('value')->nullable()->default('list');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('linked_pharmacies_settings', function (Blueprint $table) {
            $table->dropColumn('linked_from');
        });
    }
}
;