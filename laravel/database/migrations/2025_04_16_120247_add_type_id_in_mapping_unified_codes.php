<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('mapping_unified_codes', function (Blueprint $table) {
            $table->unsignedBigInteger('type_id')->nullable()->after('name');
            $table->foreign('type_id')->references('id')->on('unified_pharmacy_types')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('mapping_unified_codes', function (Blueprint $table) {
            $table->dropForeign('crm_mapping_unified_codes_type_id_foreign');
            $table->dropColumn('type_id');
        });
    }
};
