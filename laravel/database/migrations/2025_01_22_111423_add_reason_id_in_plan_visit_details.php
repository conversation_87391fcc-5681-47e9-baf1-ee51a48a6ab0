<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plan_visit_details', function (Blueprint $table) {
            $table->unsignedBigInteger('reason_id')->nullable()->after('account_lines_id');
            $table->foreign('reason_id')->references('id')->on('reasons')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plan_visit_details', function (Blueprint $table) {
            //
        });
    }
};
