<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('expense_attachments', function (Blueprint $table) {
            $table->unsignedBigInteger('attachment_id')->nullable()->after('id');
            $table->foreign('attachment_id')->references('id')->on('attachments');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('expense_attachments', function (Blueprint $table) {
            Schema::dropIfExists('expense_attachments');
        });
    }
}
;