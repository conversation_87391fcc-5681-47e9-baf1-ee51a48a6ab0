<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable("alerts_sending_methods"))
            Schema::create('alerts_sending_methods', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('alert_id');
                $table->unsignedBigInteger('by_id');
                $table->foreign('alert_id')->on('alerts')->references('id')->cascadeOnDelete();
                $table->foreign('by_id')->on('sending_methods')->references('id')->cascadeOnDelete();
                $table->timestamps();
            });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('alerts_sending_methods');
    }
};
