<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('actual_visit_errors', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('plan_id')->nullable();
            $table->foreign('plan_id')->references('id')->on('planned_visits')->onDelete("set null");
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();
            $table->unsignedBigInteger('line_id');
            $table->foreign('line_id')->references('id')->on('lines')->cascadeOnDelete();
            $table->unsignedBigInteger('div_id');
            $table->foreign('div_id')->references('id')->on('line_divisions')->cascadeOnDelete();
            $table->unsignedBigInteger('brick_id')->nullable();
            $table->foreign('brick_id')->references('id')->on('bricks')->onDelete("set null");
            $table->unsignedBigInteger('acc_type_id')->nullable();
            $table->foreign('acc_type_id')->references('id')->on('account_types')->onDelete("set null");
            $table->unsignedBigInteger('account_id');
            $table->foreign('account_id')->references('id')->on('accounts')->cascadeOnDelete();
            $table->unsignedBigInteger('account_dr_id');
            $table->foreign('account_dr_id')->references('id')->on('doctors')->cascadeOnDelete();
            $table->unsignedBigInteger('visit_type_id');
            $table->foreign('visit_type_id')->references('id')->on('visit_types')->cascadeOnDelete();
            $table->timestamp('visit_date')->nullable();
            $table->timestamp('end_visit_time')->nullable();
            $table->boolean('is_web_visit')->default(1);
            $table->time('visit_duration')->nullable();
            $table->boolean('invalid_duration')->default(0);
            $table->string('ll')->nullable();
            $table->string('lg')->nullable();
            $table->string('ll_start')->nullable();
            $table->string('lg_end')->nullable();
            $table->integer('visit_deviation')->nullable();
            $table->integer('visit_deviation_web')->nullable();
            $table->string('visit_address')->nullable();
            $table->boolean('is_fake_gps')->default(0);
            $table->string('os_version')->nullable();
            $table->string('os_type')->nullable();
            $table->string('device_brand')->nullable();
            $table->json('failures')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('actual_visit_errors');
    }
};
