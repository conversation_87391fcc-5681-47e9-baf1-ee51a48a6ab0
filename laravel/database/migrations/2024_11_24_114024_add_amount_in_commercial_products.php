<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('commercial_products', function (Blueprint $table) {
            $table->decimal('amount',12,2)->nullable()->after('ratio');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('commercial_products', function (Blueprint $table) {
            $table->dropColumn('amount');
        });
    }
};
