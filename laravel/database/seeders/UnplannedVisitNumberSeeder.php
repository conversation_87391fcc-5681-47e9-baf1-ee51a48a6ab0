<?php

namespace Database\Seeders;

use App\Models\UnplannedVisitNumber;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class UnplannedVisitNumberSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $settings =
            [
                [
                    'line_id' => null,
                    'number' => 2,
                ]
            ];

        Schema::disableForeignKeyConstraints();
        UnplannedVisitNumber::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_settings = array_chunk($settings, 20);


        foreach ($chunked_settings as $value) {
            UnplannedVisitNumber::insert($value);
        }
    }
}
