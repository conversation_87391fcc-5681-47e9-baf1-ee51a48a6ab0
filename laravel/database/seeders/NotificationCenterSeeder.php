<?php

namespace Database\Seeders;

use App\Models\Notificaion\NotificationCenter;
use App\Models\Notificaion\NotificationCenterType;
use App\Models\Notificaion\NotificationType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class NotificationCenterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $notification_types = [
            [

                'id' => 1,
                'name' => 'Visits',
            ],
            [
                'id' => 2,
                'name' => 'Request',
            ],
            [
                'id' => 3,
                'name' => 'Training',
            ],

        ];

        $types =
            [
                [
                    'type_id' => 1,
                    'name' => 'Visits',
                    'sort' => 100
                ],
                [
                    'type_id' => 1,
                    'name' => 'Plan Approved / Disapproved Notification',
                    'sort' => 200
                ],

                [
                    'type_id' => 2,
                    'name' => 'Request Notification',
                    'sort' => 300
                ],
                [
                    'type_id' => 2,
                    'name' => 'Request Action Notification',
                    'sort' => 400
                ],
                [
                    'type_id' => 2,
                    'name' => 'Expense Request Notification',
                    'sort' => 500
                ],
                [
                    'type_id' => 2,
                    'name' => 'Vacation Request Notification',
                    'sort' => 600
                ],
                [
                    'type_id' => 2,
                    'name' => 'Commercial & Branding Request Notification',
                    'sort' => 700
                ],
                [
                    'type_id' => 2,
                    'name' => 'Material Request Notification',
                    'sort' => 800
                ],
                [
                    'type_id' => 2,
                    'name' => 'Personal Request Notification',
                    'sort' => 900
                ],


                [
                    'type_id' => 3,
                    'name' => 'Quiz Creation Notification',
                    'sort' => 1000
                ],
                [
                    'type_id' => 3,
                    'name' => 'Coaching Notification',
                    'sort' => 1100
                ],
            ];

        Schema::disableForeignKeyConstraints();
        NotificationCenter::truncate();
        NotificationCenterType::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_types = array_chunk($types, 5);


        foreach ($notification_types as $type) {
            NotificationCenterType::insert($type);
        }
        foreach ($chunked_types as $value) {
            NotificationCenter::insert($value);
        }
    }
}
