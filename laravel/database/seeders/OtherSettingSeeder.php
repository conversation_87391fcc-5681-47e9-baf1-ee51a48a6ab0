<?php

namespace Database\Seeders;

use App\Models\OtherSetting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class OtherSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $other_settings =
            [
                [
                    'name' => 'Type Of Frequency',
                    'key' => 'type_of_frequency',
                    'value' => '1',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        OtherSetting::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_other_settings = array_chunk($other_settings, 5);


        foreach ($chunked_other_settings as $value) {
            OtherSetting::insert($value);
        }
    }
}
