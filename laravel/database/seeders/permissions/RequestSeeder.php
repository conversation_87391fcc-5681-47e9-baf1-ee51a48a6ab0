<?php

namespace Database\Seeders\Permissions;

use App\Form;
use App\Module;
use App\Permission;
use Illuminate\Database\Seeder;

class RequestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // requests

        resetPermissionModule("requests");
        $module = Module::firstOrCreate([
            "module" => "requests",
            "icon" => "cil-color-border",
        ]);

        $forms = [
            [
                'form' => 'vacation_types'
            ],
            [
                'form' => 'vacations'
            ],
            [
                'form' => 'public_holidays'
            ],
            [
                'form' => 'cost_types'
            ],
            [
                'form' => 'personal_requests'
            ],
            [
                'form' => 'personal_request_types'
            ],
            [
                'form' => 'commercial_request'
            ],
            [
                'form' => 'expense_types'
            ],
            [
                'form' => 'expenses'
            ],
            [
                'form' => 'request_types'
            ],
            [
                'form' => 'expense_location_settings'
            ],
            [
                'form' => 'expense_location_prices'
            ],
            [
                'form' => 'expense_location_price_factors'
            ],

            [
                'form' => 'expense_locations'
            ],
            [
                'form' => 'promotional_material_types'
            ],
            [
                'form' => 'Material'
            ],
            [
                'form' => 'kilometers_averages'
            ],
            [
                'form' => 'commercial_pharmacies'
            ],
            [
                'form' => 'budget_settings'
            ],
            [
                'form' => 'request_feedback'
            ],
            [
                'form' => 'budgets'
            ],
            [
                'form' => 'paid_requests'
            ],
            [
                'form' => 'approval_requests'
            ],
            [
                'form' => 'commercial_categoreis'
            ],
            [
                'form' => 'commercial_categoreis_types'
            ],
            [
                'form' => 'commercial_payment_methods'
            ],
            [
                'form' => 'commercial_custodies'
            ],
            [
                'form' => 'service_completes'
            ],
            [
                'form' => 'partial_payments'
            ],
        ];

        $formIds = [];
        foreach ($forms as $value) {
            $form = Form::firstOrCreate($value);
            array_push($formIds, $form->id);
        }

        $permissions = [
            //vacation types
            [
                'name'          => 'show_all_vacation_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_vacation_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_vacation_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_vacation_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_vacation_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_vacation_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // vacations
            [
                'name'          => 'show_all_vacations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_vacations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_vacations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_vacations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_vacations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_vacations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'export_xlsx_vacations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_vacations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_vacations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_vacations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],



            // Public Holidays
            [
                'name'          => 'show_all_public_holidays',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_public_holidays',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_public_holidays',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_public_holidays',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_public_holidays',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_public_holidays',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Commercial Cost

            [
                'name'          => 'show_all_cost_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_cost_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_cost_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_cost_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_cost_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_cost_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_cost_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_cost_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_cost_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_cost_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_cost_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            //personal request

            [
                'name'          => 'show_all_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            //personal request Types

            [
                'name'          => 'show_all_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Commercial

            [
                'name'          => 'show_all_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_commercial_archived',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // Expense Types

            [
                'name'          => 'show_all_expense_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_expense_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_expense_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_expense_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_expense_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_expense_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_expense_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_expense_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_expense_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_expense_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_expense_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Expense Meals

            [
                'name'          => 'show_all_expense_meals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_expense_meals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_expense_meals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_expense_meals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            // [
            //     'name'          => 'restore_expense_types',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[7],
            //     'action_id'     => '6',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'import_expense_types',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[7],
            //     'action_id'     => '10',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'export_xlsx_expense_types',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[7],
            //     'action_id'     => '11',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ],
            // [
            //     'name'          => 'export_csv_expense_types',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[7],
            //     'action_id'     => '22',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'export_pdf_expense_types',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[7],
            //     'action_id'     => '12',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'export_email_expense_types',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[7],
            //     'action_id'     => '13',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ],

            // Expenses

            [
                'name'          => 'show_all_expenses',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_expenses',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_expenses',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_expenses',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_expenses',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_expenses',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_expenses',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_expenses',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_expenses',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_expenses',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_expenses',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Request Types

            [
                'name'          => 'show_all_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_request_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // Expense Location Settings

            [
                'name'          => 'show_all_expense_location_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_expense_location_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_expense_location_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_expense_location_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_expense_location_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_expense_location_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_expense_location_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_expense_location_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_expense_location_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_expense_location_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_expense_location_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_expense_location_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_expense_location_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Expense Location Prices

            [
                'name'          => 'show_all_expense_location_prices',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_expense_location_prices',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_expense_location_prices',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_expense_location_prices',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_expense_location_prices',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_expense_location_prices',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_expense_location_prices',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_expense_location_prices',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_expense_location_prices',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_expense_location_prices',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_expense_location_prices',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_expense_location_prices',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Expense Location Price Factors

            [
                'name'          => 'show_all_expense_price_factors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_expense_price_factors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_expense_price_factors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_expense_price_factors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_expense_price_factors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_expense_price_factors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_expense_price_factors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_expense_price_factors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_expense_price_factors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_expense_price_factors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_expense_price_factors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_expense_price_factors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_expense_price_factors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Expense Location

            [
                'name'          => 'show_all_expense_locations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_expense_locations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_expense_locations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_expense_locations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_expense_locations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_expense_locations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_expense_locations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_expense_locations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_expense_locations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_expense_locations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_expense_locations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_expense_locations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_expense_locations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Promotionsal material types
            [
                'name'          => 'show_all_promotional_material_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[14],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_promotional_material_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[14],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_promotional_material_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[14],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_promotional_material_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[14],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_promotional_material_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[14],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_promotional_material_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[14],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_promotional_material_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[14],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_promotional_material_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[14],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_promotional_material_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[14],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_promotional_material_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[14],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_promotional_material_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[14],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_promotional_material_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[14],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_promotional_material_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[14],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // material 
            [
                'name'          => 'show_all_material',
                'guard_name'    => 'api',
                'form_id'       => $formIds[15],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_material',
                'guard_name'    => 'api',
                'form_id'       => $formIds[15],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_material',
                'guard_name'    => 'api',
                'form_id'       => $formIds[15],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_material',
                'guard_name'    => 'api',
                'form_id'       => $formIds[15],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_material',
                'guard_name'    => 'api',
                'form_id'       => $formIds[15],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_material',
                'guard_name'    => 'api',
                'form_id'       => $formIds[15],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_material',
                'guard_name'    => 'api',
                'form_id'       => $formIds[15],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_material',
                'guard_name'    => 'api',
                'form_id'       => $formIds[15],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_material',
                'guard_name'    => 'api',
                'form_id'       => $formIds[15],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_material',
                'guard_name'    => 'api',
                'form_id'       => $formIds[15],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_material',
                'guard_name'    => 'api',
                'form_id'       => $formIds[15],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // kilometers average
            [
                'name'          => 'show_all_kilometers_averages',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_kilometers_averages',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_kilometers_averages',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_kilometers_averages',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_kilometers_averages',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_kilometers_averages',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_kilometers_averages',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_kilometers_averages',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_kilometers_averages',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_kilometers_averages',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_kilometers_averages',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_kilometers_averages',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // Commercial Pharmacies
            [
                'name'          => 'show_all_commercial_pharmacies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[17],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_commercial_pharmacies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[17],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_commercial_pharmacies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[17],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_commercial_pharmacies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[17],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_commercial_pharmacies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[17],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_commercial_pharmacies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[17],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Budget Settings
            [
                'name'          => 'show_all_budget_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[18],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_budget_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[18],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_budget_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[18],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_budget_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[18],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            /* Request Feedback */
            [
                'name'          => 'commercial_request_feedback',
                'guard_name'    => 'api',
                'form_id'       => $formIds[19],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'expense_request_feedback',
                'guard_name'    => 'api',
                'form_id'       => $formIds[19],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'material_request_feedback',
                'guard_name'    => 'api',
                'form_id'       => $formIds[19],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'vacation_request_feedback',
                'guard_name'    => 'api',
                'form_id'       => $formIds[19],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'personal_request_feedback',
                'guard_name'    => 'api',
                'form_id'       => $formIds[19],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_request_feedback',
                'guard_name'    => 'api',
                'form_id'       => $formIds[19],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_request_feedback',
                'guard_name'    => 'api',
                'form_id'       => $formIds[19],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Budgets
            [
                'name'          => 'show_all_budget_setups',
                'guard_name'    => 'api',
                'form_id'       => $formIds[20],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_budget_setups',
                'guard_name'    => 'api',
                'form_id'       => $formIds[20],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_budget_setups',
                'guard_name'    => 'api',
                'form_id'       => $formIds[20],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_budget_setups',
                'guard_name'    => 'api',
                'form_id'       => $formIds[20],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_budget_setups',
                'guard_name'    => 'api',
                'form_id'       => $formIds[20],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_budget_setups',
                'guard_name'    => 'api',
                'form_id'       => $formIds[20],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_budget_setups',
                'guard_name'    => 'api',
                'form_id'       => $formIds[20],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_budget_setups',
                'guard_name'    => 'api',
                'form_id'       => $formIds[20],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_budget_setups',
                'guard_name'    => 'api',
                'form_id'       => $formIds[20],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_budget_setups',
                'guard_name'    => 'api',
                'form_id'       => $formIds[20],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_budget_setups',
                'guard_name'    => 'api',
                'form_id'       => $formIds[20],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_budget_setups',
                'guard_name'    => 'api',
                'form_id'       => $formIds[20],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_budget_setups',
                'guard_name'    => 'api',
                'form_id'       => $formIds[20],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            /* Paid Request */
            [
                'name'          => 'paid_commercial_request',
                'guard_name'    => 'api',
                'form_id'       => $formIds[21],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'paid_expense_request',
                'guard_name'    => 'api',
                'form_id'       => $formIds[21],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'paid_material_request',
                'guard_name'    => 'api',
                'form_id'       => $formIds[21],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'edit_paid_request',
                'guard_name'    => 'api',
                'form_id'       => $formIds[21],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_paid_request',
                'guard_name'    => 'api',
                'form_id'       => $formIds[21],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Approvals
            [
                'name'          => 'create_approve_vacation',
                'guard_name'    => 'api',
                'form_id'       => $formIds[22],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_disapprove_vacation',
                'guard_name'    => 'api',
                'form_id'       => $formIds[22],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'reset_vacation_approvals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[22],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_approve_commercial',
                'guard_name'    => 'api',
                'form_id'       => $formIds[22],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_disapprove_commercial',
                'guard_name'    => 'api',
                'form_id'       => $formIds[22],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'reset_commercial_approvals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[22],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_approve_expense',
                'guard_name'    => 'api',
                'form_id'       => $formIds[22],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_disapprove_expense',
                'guard_name'    => 'api',
                'form_id'       => $formIds[22],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'reset_expense_approvals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[22],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // material approval
            [
                'name'          => 'create_approve_material',
                'guard_name'    => 'api',
                'form_id'       => $formIds[22],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_disapprove_material',
                'guard_name'    => 'api',
                'form_id'       => $formIds[22],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'reset_material_approvals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[22],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_approve_active_inactive',
                'guard_name'    => 'api',
                'form_id'       => $formIds[22],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_disapprove_active_inactive',
                'guard_name'    => 'api',
                'form_id'       => $formIds[22],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'reset_active_inactive_approvals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[22],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // Categories
            [
                'name'          => 'show_all_commercial_categories',
                'guard_name'    => 'api',
                'form_id'       => $formIds[23],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_commercial_categories',
                'guard_name'    => 'api',
                'form_id'       => $formIds[23],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_commercial_categories',
                'guard_name'    => 'api',
                'form_id'       => $formIds[23],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_commercial_categories',
                'guard_name'    => 'api',
                'form_id'       => $formIds[23],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_commercial_categories',
                'guard_name'    => 'api',
                'form_id'       => $formIds[23],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_commercial_categories',
                'guard_name'    => 'api',
                'form_id'       => $formIds[23],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Categories Types
            [
                'name'          => 'show_all_commercial_categories_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[24],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_commercial_categories_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[24],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_commercial_categories_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[24],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_commercial_categories_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[24],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_commercial_categories_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[24],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_commercial_categories_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[24],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Payment methods
            [
                'name'          => 'show_all_commercial_payment_methods',
                'guard_name'    => 'api',
                'form_id'       => $formIds[25],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_commercial_payment_methods',
                'guard_name'    => 'api',
                'form_id'       => $formIds[25],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_commercial_payment_methods',
                'guard_name'    => 'api',
                'form_id'       => $formIds[25],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_commercial_payment_methods',
                'guard_name'    => 'api',
                'form_id'       => $formIds[25],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_commercial_payment_methods',
                'guard_name'    => 'api',
                'form_id'       => $formIds[25],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_commercial_payment_methods',
                'guard_name'    => 'api',
                'form_id'       => $formIds[25],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Commercial Custodies
            [
                'name'          => 'show_all_commercial_custodies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[26],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_commercial_custodies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[26],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_commercial_custodies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[26],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_commercial_custodies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[26],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_commercial_custodies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[26],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_commercial_custodies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[26],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // Service Done
            [
                'name'          => 'service_complete_commercial_request',
                'guard_name'    => 'api',
                'form_id'       => $formIds[27],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_service_completes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[27],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Partial Payments
            [
                'name'          => 'show_all_partial_payments',
                'guard_name'    => 'api',
                'form_id'       => $formIds[28],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_partial_payments',
                'guard_name'    => 'api',
                'form_id'       => $formIds[28],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_partial_payments',
                'guard_name'    => 'api',
                'form_id'       => $formIds[28],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
        ];


        foreach ($permissions as $value) {
            Permission::insert($value);
        }
    }
}
