<?php

namespace Database\Seeders;

use App\Models\PV\EventAction;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class PVActionDrugSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pv_modules =
            [
                [
                    'id' => 1,
                    'name' => 'Stopped',
                    'notes' => 'Stopped',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 2,
                    'name' => 'Dose Increase',
                    'notes' => 'Dose Increase',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 3,
                    'name' => 'Dose Decrease',
                    'notes' => 'Dose Decrease',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 4,
                    'name' => 'No Change',
                    'notes' => 'No Change',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 5,
                    'name' => 'Unknown',
                    'notes' => 'Unknown',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        EventAction::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_pv_modules = array_chunk($pv_modules, 5);


        foreach ($chunked_pv_modules as $value) {
            EventAction::insert($value);
        }
    }
}
