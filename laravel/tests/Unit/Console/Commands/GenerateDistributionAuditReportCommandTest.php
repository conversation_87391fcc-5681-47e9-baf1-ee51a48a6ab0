<?php

namespace Tests\Unit\Console\Commands;

use App\Console\Commands\GenerateDistributionAuditReportCommand;
use App\LineDivParent;
use App\LineDivision;
use App\Sale;
use App\SaleDetail;
use App\Services\Enums\Ceiling;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

/**
 * Test class for GenerateDistributionAuditReportCommand
 * 
 * Tests the comprehensive distribution audit report generation including
 * validation of distribution integrity, hierarchy checks, split analysis,
 * ceiling processing, and referential integrity.
 * 
 * @covers \App\Console\Commands\GenerateDistributionAuditReportCommand
 */
class GenerateDistributionAuditReportCommandTest extends TestCase
{
    use RefreshDatabase;

    private string $fromDate;
    private string $toDate;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->fromDate = Carbon::now()->subDays(30)->format('Y-m-d');
        $this->toDate = Carbon::now()->format('Y-m-d');
        
        // Mock storage for file exports
        Storage::fake('local');
    }

    /**
     * Test command signature and basic structure
     */
    public function test_command_signature(): void
    {
        $command = new GenerateDistributionAuditReportCommand();
        
        $this->assertStringContainsString('distribution:generate-audit-report', $command->getName());
        $this->assertStringContainsString('Generate comprehensive audit report', $command->getDescription());
    }

    /**
     * Test command requires date parameters
     */
    public function test_command_requires_date_parameters(): void
    {
        $exitCode = Artisan::call('distribution:generate-audit-report');
        
        $this->assertEquals(1, $exitCode);
        $this->assertStringContainsString('Both --from-date and --to-date are required', Artisan::output());
    }

    /**
     * Test command validates date format
     */
    public function test_command_validates_date_format(): void
    {
        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => 'invalid-date',
            '--to-date' => '2024-01-31'
        ]);
        
        $this->assertEquals(1, $exitCode);
        $this->assertStringContainsString('Invalid date format', Artisan::output());
    }

    /**
     * Test command validates date range
     */
    public function test_command_validates_date_range(): void
    {
        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => '2024-01-31',
            '--to-date' => '2024-01-01'
        ]);
        
        $this->assertEquals(1, $exitCode);
        $this->assertStringContainsString('from-date cannot be after to-date', Artisan::output());
    }

    /**
     * Test command validates distribution type
     */
    public function test_command_validates_distribution_type(): void
    {
        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--distribution-type' => '5'
        ]);
        
        $this->assertEquals(1, $exitCode);
        $this->assertStringContainsString('distribution-type must be 1, 2, or 3', Artisan::output());
    }

    /**
     * Test command validates output format
     */
    public function test_command_validates_output_format(): void
    {
        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--format' => 'invalid'
        ]);
        
        $this->assertEquals(1, $exitCode);
        $this->assertStringContainsString('format must be table, json, or csv', Artisan::output());
    }

    /**
     * Test basic command execution with minimal data
     */
    public function test_basic_command_execution(): void
    {
        // Create minimal test data
        $this->createTestSalesData();
        
        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--summary-only' => true
        ]);
        
        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('DISTRIBUTION AUDIT SUMMARY', Artisan::output());
    }

    /**
     * Test command with JSON output format
     */
    public function test_json_output_format(): void
    {
        $this->createTestSalesData();
        
        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--format' => 'json'
        ]);
        
        $this->assertEquals(0, $exitCode);
        
        $output = Artisan::output();
        $this->assertJson($output);
        
        $data = json_decode($output, true);
        $this->assertArrayHasKey('metadata', $data);
        $this->assertArrayHasKey('summary', $data);
        $this->assertArrayHasKey('distribution_integrity', $data);
    }

    /**
     * Test command with CSV output format
     */
    public function test_csv_output_format(): void
    {
        $this->createTestSalesData();

        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--format' => 'csv'
        ]);

        $this->assertEquals(0, $exitCode);

        $output = Artisan::output();
        $this->assertStringContainsString('DISTRIBUTION AUDIT REPORT (CSV FORMAT)', $output);
        $this->assertStringContainsString('Category","Type","Sale ID"', $output);
        $this->assertStringContainsString('Summary Statistics:', $output);
    }

    /**
     * Test command with file export
     */
    public function test_file_export(): void
    {
        $this->createTestSalesData();

        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--format' => 'json',
            '--export-file' => 'test_audit'
        ]);

        $this->assertEquals(0, $exitCode);

        // Check if file was created
        $files = Storage::disk('local')->files('distribution_audit_reports');
        $this->assertNotEmpty($files);

        $exportedFile = collect($files)->first(function ($file) {
            return str_contains($file, 'test_audit') && str_ends_with($file, '.json');
        });

        $this->assertNotNull($exportedFile);

        // Verify file content
        $content = Storage::disk('local')->get($exportedFile);
        $data = json_decode($content, true);
        $this->assertArrayHasKey('metadata', $data);
    }

    /**
     * Test CSV export with file
     */
    public function test_csv_file_export(): void
    {
        $this->createTestSalesData();

        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--format' => 'csv',
            '--export-file' => 'test_csv_audit'
        ]);

        $this->assertEquals(0, $exitCode);

        // Check if CSV file was created
        $files = Storage::disk('local')->files('distribution_audit_reports');
        $csvFile = collect($files)->first(function ($file) {
            return str_contains($file, 'test_csv_audit') && str_ends_with($file, '.csv');
        });

        $this->assertNotNull($csvFile);

        // Verify CSV file content
        $content = Storage::disk('local')->get($csvFile);
        $this->assertStringContainsString('Category","Type","Sale ID"', $content);

        // Verify terminal output also shows CSV data
        $output = Artisan::output();
        $this->assertStringContainsString('CSV data also displayed below:', $output);
        $this->assertStringContainsString('DISTRIBUTION AUDIT REPORT (CSV FORMAT)', $output);
    }

    /**
     * Test command with all analysis options enabled
     */
    public function test_comprehensive_analysis(): void
    {
        $this->createTestSalesData();
        $this->createTestHierarchyData();
        
        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--include-hierarchy' => true,
            '--include-split-analysis' => true,
            '--include-ceiling-analysis' => true,
            '--include-referential' => true,
            '--format' => 'json'
        ]);
        
        $this->assertEquals(0, $exitCode);
        
        $output = Artisan::output();
        $data = json_decode($output, true);
        
        $this->assertArrayHasKey('hierarchy_validation', $data);
        $this->assertArrayHasKey('split_analysis', $data);
        $this->assertArrayHasKey('ceiling_analysis', $data);
        $this->assertArrayHasKey('referential_integrity', $data);
        $this->assertArrayHasKey('recommendations', $data);
    }

    /**
     * Test Store strategy specific analysis
     */
    public function test_store_strategy_analysis(): void
    {
        $this->createTestSalesData();
        
        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--distribution-type' => '2',
            '--include-split-analysis' => true,
            '--format' => 'json'
        ]);
        
        $this->assertEquals(0, $exitCode);
        
        $output = Artisan::output();
        $data = json_decode($output, true);
        
        $this->assertArrayHasKey('split_analysis', $data);
        $this->assertEquals('2', $data['metadata']['distribution_type']);
    }

    /**
     * Create test sales data for testing
     */
    private function createTestSalesData(): void
    {
        // Create ABOVE sales (original sales - should have null sale_ids)
        $aboveSale = Sale::factory()->create([
            'ceiling' => Ceiling::ABOVE->value,
            'quantity' => 300,
            'value' => 3000,
            'bonus' => 30,
            'date' => Carbon::now()->subDays(15)->format('Y-m-d'),
            'sale_ids' => null
        ]);

        // Create BELOW sales (limited sales - should reference ABOVE sale)
        $belowSale = Sale::factory()->create([
            'ceiling' => Ceiling::BELOW->value,
            'quantity' => 100,
            'value' => 1000,
            'bonus' => 10,
            'date' => Carbon::now()->subDays(15)->format('Y-m-d'),
            'sale_ids' => (string)$aboveSale->id
        ]);

        // Create sale details for below sale
        SaleDetail::factory()->create([
            'sale_id' => $belowSale->id,
            'quantity' => 90,
            'value' => 900,
            'bonus' => 9,
            'ratio' => 0.9
        ]);

        SaleDetail::factory()->create([
            'sale_id' => $belowSale->id,
            'quantity' => 10,
            'value' => 100,
            'bonus' => 1,
            'ratio' => 0.1
        ]);

        // Create DISTRIBUTED sales (excess sales - should reference ABOVE sale)
        $distributedSale = Sale::factory()->create([
            'ceiling' => Ceiling::DISTRIBUTED->value,
            'quantity' => 200,
            'value' => 2000,
            'bonus' => 20,
            'date' => Carbon::now()->subDays(15)->format('Y-m-d'),
            'sale_ids' => (string)$aboveSale->id
        ]);

        // Create sale details for distributed sale
        SaleDetail::factory()->create([
            'sale_id' => $distributedSale->id,
            'quantity' => 180,
            'value' => 1800,
            'bonus' => 18,
            'ratio' => 0.9
        ]);

        SaleDetail::factory()->create([
            'sale_id' => $distributedSale->id,
            'quantity' => 20,
            'value' => 200,
            'bonus' => 2,
            'ratio' => 0.1
        ]);
    }

    /**
     * Create test hierarchy data for testing
     */
    private function createTestHierarchyData(): void
    {
        // Create line divisions
        $parentDivision = LineDivision::factory()->create([
            'name' => 'Parent Division',
            'from_date' => Carbon::now()->subYear(),
            'to_date' => null
        ]);

        $childDivision = LineDivision::factory()->create([
            'name' => 'Child Division',
            'from_date' => Carbon::now()->subYear(),
            'to_date' => null
        ]);

        // Create parent-child relationship
        LineDivParent::factory()->create([
            'line_div_id' => $childDivision->id,
            'parent_id' => $parentDivision->id,
            'from_date' => Carbon::now()->subYear(),
            'to_date' => null
        ]);
    }
}
