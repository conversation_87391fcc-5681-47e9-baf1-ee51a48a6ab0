<?php

namespace Tests\Unit\Console\Commands;

use App\Console\Commands\ScanDistributionErrorsCommand;
use App\Sale;
use App\SaleDetail;
use App\Services\Enums\Ceiling;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;
use Mockery;

class ScanDistributionErrorsCommandTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('local');
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test command requires date parameters
     */
    public function test_command_requires_date_parameters(): void
    {
        // Act
        $exitCode = Artisan::call('distribution:scan-errors');

        // Assert
        $this->assertEquals(1, $exitCode);
        $this->assertStringContainsString('Both --from-date and --to-date are required', Artisan::output());
    }

    /**
     * Test command validates date format
     */
    public function test_command_validates_date_format(): void
    {
        // Act
        $exitCode = Artisan::call('distribution:scan-errors', [
            '--from-date' => 'invalid-date',
            '--to-date' => '2024-01-31'
        ]);

        // Assert
        $this->assertEquals(1, $exitCode);
        $this->assertStringContainsString('Invalid date format', Artisan::output());
    }

    /**
     * Test command validates date range
     */
    public function test_command_validates_date_range(): void
    {
        // Act
        $exitCode = Artisan::call('distribution:scan-errors', [
            '--from-date' => '2024-01-31',
            '--to-date' => '2024-01-01'
        ]);

        // Assert
        $this->assertEquals(1, $exitCode);
        $this->assertStringContainsString('from-date cannot be after to-date', Artisan::output());
    }

    /**
     * Test command runs successfully with no errors
     */
    public function test_command_runs_successfully_with_no_errors(): void
    {
        // Arrange - Create valid sales data
        Sale::factory()->create([
            'date' => '2024-01-15',
            'ceiling' => Ceiling::BELOW->value,
            'quantity' => 100,
            'value' => 1000,
            'bonus' => 10,
            'sale_ids' => null
        ]);

        // Act
        $exitCode = Artisan::call('distribution:scan-errors', [
            '--from-date' => '2024-01-01',
            '--to-date' => '2024-01-31'
        ]);

        // Assert
        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('No distribution errors found!', Artisan::output());
    }

    /**
     * Test detection of distributed sales without records
     */
    public function test_detects_distributed_sales_without_records(): void
    {
        // Arrange - Create distributed sale without details
        $sale = Sale::factory()->create([
            'date' => '2024-01-15',
            'ceiling' => Ceiling::DISTRIBUTED->value,
            'sale_ids' => '123,456',
            'quantity' => 100,
            'value' => 1000,
            'bonus' => 10
        ]);

        // Act
        $exitCode = Artisan::call('distribution:scan-errors', [
            '--from-date' => '2024-01-01',
            '--to-date' => '2024-01-31',
            '--detailed'
        ]);

        // Assert
        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('distributed sales without records', Artisan::output());
        $this->assertStringContainsString("Sale ID: {$sale->id}", Artisan::output());
    }

    /**
     * Test detection of mismatched totals
     */
    public function test_detects_mismatched_totals(): void
    {
        // Arrange - Create distributed sale with mismatched details
        $sale = Sale::factory()->create([
            'date' => '2024-01-15',
            'ceiling' => Ceiling::DISTRIBUTED->value,
            'sale_ids' => '123',
            'quantity' => 100,
            'value' => 1000,
            'bonus' => 10
        ]);

        SaleDetail::factory()->create([
            'sale_id' => $sale->id,
            'quantity' => 50, // Mismatched quantity
            'value' => 500,
            'bonus' => 5,
            'ratio' => 0.5
        ]);

        // Act
        $exitCode = Artisan::call('distribution:scan-errors', [
            '--from-date' => '2024-01-01',
            '--to-date' => '2024-01-31',
            '--detailed'
        ]);

        // Assert
        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('sales with mismatched totals', Artisan::output());
        $this->assertStringContainsString("Sale ID: {$sale->id}", Artisan::output());
    }

    /**
     * Test detection of incorrect ceiling transitions
     */
    public function test_detects_incorrect_ceiling_transitions(): void
    {
        // Arrange - Create sale with DISTRIBUTED status but no sale_ids
        $sale = Sale::factory()->create([
            'date' => '2024-01-15',
            'ceiling' => Ceiling::DISTRIBUTED->value,
            'sale_ids' => null, // Should have sale_ids if DISTRIBUTED
            'quantity' => 100,
            'value' => 1000,
            'bonus' => 10
        ]);

        // Act
        $exitCode = Artisan::call('distribution:scan-errors', [
            '--from-date' => '2024-01-01',
            '--to-date' => '2024-01-31',
            '--detailed'
        ]);

        // Assert
        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('incorrect ceiling transitions', Artisan::output());
        $this->assertStringContainsString("Sale ID: {$sale->id}", Artisan::output());
    }

    /**
     * Test detection of orphaned distributions
     */
    public function test_detects_orphaned_distributions(): void
    {
        // Arrange - Create distributed sale referencing non-existent original sale
        $sale = Sale::factory()->create([
            'date' => '2024-01-15',
            'ceiling' => Ceiling::DISTRIBUTED->value,
            'sale_ids' => '999999', // Non-existent sale ID
            'quantity' => 100,
            'value' => 1000,
            'bonus' => 10
        ]);

        // Act
        $exitCode = Artisan::call('distribution:scan-errors', [
            '--from-date' => '2024-01-01',
            '--to-date' => '2024-01-31',
            '--detailed'
        ]);

        // Assert
        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('orphaned distribution records', Artisan::output());
        $this->assertStringContainsString("Sale ID: {$sale->id}", Artisan::output());
    }

    /**
     * Test detection of sales details misalignment
     */
    public function test_detects_sales_details_misalignment(): void
    {
        // Arrange - Create BELOW sale with details (should not have details)
        $sale = Sale::factory()->create([
            'date' => '2024-01-15',
            'ceiling' => Ceiling::BELOW->value,
            'quantity' => 100,
            'value' => 1000,
            'bonus' => 10
        ]);

        SaleDetail::factory()->create([
            'sale_id' => $sale->id,
            'quantity' => 100,
            'value' => 1000,
            'bonus' => 10,
            'ratio' => 1.0
        ]);

        // Act
        $exitCode = Artisan::call('distribution:scan-errors', [
            '--from-date' => '2024-01-01',
            '--to-date' => '2024-01-31',
            '--detailed'
        ]);

        // Assert
        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('sales details misalignments', Artisan::output());
        $this->assertStringContainsString("Sale ID: {$sale->id}", Artisan::output());
    }

    /**
     * Test 90/10 split validation for Store strategy
     */
    public function test_validates_split_distribution_for_store_strategy(): void
    {
        // Arrange - Create distributed sale with invalid ratio total
        $sale = Sale::factory()->create([
            'date' => '2024-01-15',
            'ceiling' => Ceiling::DISTRIBUTED->value,
            'sale_ids' => '123',
            'quantity' => 100,
            'value' => 1000,
            'bonus' => 10
        ]);

        SaleDetail::factory()->create([
            'sale_id' => $sale->id,
            'quantity' => 100,
            'value' => 1000,
            'bonus' => 10,
            'ratio' => 0.8 // Invalid ratio (should total to 1.0)
        ]);

        // Act
        $exitCode = Artisan::call('distribution:scan-errors', [
            '--from-date' => '2024-01-01',
            '--to-date' => '2024-01-31',
            '--distribution-type' => '2', // Store strategy
            '--detailed'
        ]);

        // Assert
        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('90/10 split validation errors', Artisan::output());
        $this->assertStringContainsString("Sale ID: {$sale->id}", Artisan::output());
    }

    /**
     * Test CSV export functionality
     */
    public function test_csv_export_functionality(): void
    {
        // Arrange - Create sale with error
        $sale = Sale::factory()->create([
            'date' => '2024-01-15',
            'ceiling' => Ceiling::DISTRIBUTED->value,
            'sale_ids' => null,
            'quantity' => 100,
            'value' => 1000,
            'bonus' => 10
        ]);

        // Act
        $exitCode = Artisan::call('distribution:scan-errors', [
            '--from-date' => '2024-01-01',
            '--to-date' => '2024-01-31',
            '--export-csv' => 'test_export'
        ]);

        // Assert
        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('Results exported to:', Artisan::output());
        
        // Check that CSV file was created
        $files = Storage::disk('local')->files('distribution_errors');
        $this->assertNotEmpty($files);
        $this->assertStringContainsString('test_export', $files[0]);
    }

    /**
     * Test command with product and distributor filters
     */
    public function test_command_with_filters(): void
    {
        // Arrange
        $sale1 = Sale::factory()->create([
            'date' => '2024-01-15',
            'product_id' => 1,
            'distributor_id' => 1,
            'ceiling' => Ceiling::BELOW->value
        ]);

        $sale2 = Sale::factory()->create([
            'date' => '2024-01-15',
            'product_id' => 2,
            'distributor_id' => 2,
            'ceiling' => Ceiling::BELOW->value
        ]);

        // Act
        $exitCode = Artisan::call('distribution:scan-errors', [
            '--from-date' => '2024-01-01',
            '--to-date' => '2024-01-31',
            '--product-ids' => '1',
            '--distributor-ids' => '1'
        ]);

        // Assert
        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('No distribution errors found!', Artisan::output());
    }

    /**
     * Test tolerance parameter
     */
    public function test_tolerance_parameter(): void
    {
        // Arrange - Create sale with small difference
        $sale = Sale::factory()->create([
            'date' => '2024-01-15',
            'ceiling' => Ceiling::DISTRIBUTED->value,
            'sale_ids' => '123',
            'quantity' => 100,
            'value' => 1000,
            'bonus' => 10
        ]);

        SaleDetail::factory()->create([
            'sale_id' => $sale->id,
            'quantity' => 100.0005, // Small difference within tolerance
            'value' => 1000,
            'bonus' => 10,
            'ratio' => 1.0
        ]);

        // Act
        $exitCode = Artisan::call('distribution:scan-errors', [
            '--from-date' => '2024-01-01',
            '--to-date' => '2024-01-31',
            '--tolerance' => '0.001'
        ]);

        // Assert
        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('No distribution errors found!', Artisan::output());
    }
}
