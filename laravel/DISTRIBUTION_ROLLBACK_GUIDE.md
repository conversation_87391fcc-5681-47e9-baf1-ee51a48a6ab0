# Distribution Rollback Guide

This guide explains how to use the distribution rollback functionality to reverse distribution operations.

## Overview

The distribution rollback system provides two ways to rollback distribution operations:

1. **Artisan Command** (Recommended): `php artisan distribution:rollback`
2. **Standalone Script**: `php scripts/rollback_distribution.php`

## What Gets Rolled Back

The rollback process:

1. **Removes distributed sales** - Sales with `ceiling = 'ABOVE'` created during distribution
2. **Removes sales details** - All sales_details records linked to distributed sales  
3. **Resets original sales** - Updates ceiling status back to `'BELOW'` for original sales
4. **Creates backups** - Optional backup tables before making changes

## Usage Examples

### Basic Rollback (Artisan Command)

```bash
# Dry run to preview changes
php artisan distribution:rollback --from-date=2024-01-01 --to-date=2024-01-31 --dry-run

# Execute rollback with backup
php artisan distribution:rollback --from-date=2024-01-01 --to-date=2024-01-31 --backup

# Rollback specific distribution type
php artisan distribution:rollback --from-date=2024-01-01 --to-date=2024-01-31 --distribution-type=2 --backup

# Rollback specific products
php artisan distribution:rollback --from-date=2024-01-01 --to-date=2024-01-31 --product-ids=1,2,3 --backup

# Force rollback without confirmation
php artisan distribution:rollback --from-date=2024-01-01 --to-date=2024-01-31 --force --backup
```

### Using Standalone Script

```bash
# Dry run
php scripts/rollback_distribution.php --from-date=2024-01-01 --to-date=2024-01-31 --dry-run

# Execute with backup
php scripts/rollback_distribution.php --from-date=2024-01-01 --to-date=2024-01-31 --backup
```

## Command Options

| Option | Description | Required | Example |
|--------|-------------|----------|---------|
| `--from-date` | Start date for rollback | Yes | `--from-date=2024-01-01` |
| `--to-date` | End date for rollback | Yes | `--to-date=2024-01-31` |
| `--distribution-type` | Filter by distribution type | No | `--distribution-type=2` |
| `--product-ids` | Filter by specific products | No | `--product-ids=1,2,3` |
| `--distributor-ids` | Filter by specific distributors | No | `--distributor-ids=1,2,3` |
| `--dry-run` | Preview changes without executing | No | `--dry-run` |
| `--backup` | Create backup tables | No | `--backup` |
| `--force` | Skip confirmation prompts | No | `--force` |

## Distribution Types

- `1` = Private Pharmacy Strategy
- `2` = Store Strategy  
- `3` = Local Chain Strategy

## Safety Features

### 1. Dry Run Mode
Always test with `--dry-run` first to see what will be affected:

```bash
php artisan distribution:rollback --from-date=2024-01-01 --to-date=2024-01-31 --dry-run
```

### 2. Backup Creation
Use `--backup` to create backup tables before rollback:

```bash
php artisan distribution:rollback --from-date=2024-01-01 --to-date=2024-01-31 --backup
```

Backup tables are named with timestamp:
- `sales_backup_YYYY_MM_DD_HH_MM_SS`
- `sales_details_backup_YYYY_MM_DD_HH_MM_SS`

### 3. Transaction Safety
All operations are wrapped in database transactions for atomicity.

### 4. Confirmation Prompts
Interactive confirmation unless `--force` is used.

## Workflow Recommendations

### 1. Analysis Phase
```bash
# First, analyze what will be affected
php artisan distribution:rollback --from-date=2024-01-01 --to-date=2024-01-31 --dry-run
```

### 2. Backup Phase
```bash
# Create backup before proceeding
php artisan distribution:rollback --from-date=2024-01-01 --to-date=2024-01-31 --backup
```

### 3. Verification Phase
```bash
# Verify backup tables exist
SHOW TABLES LIKE '%backup%';

# Check backup data
SELECT COUNT(*) FROM sales_backup_2024_01_15_14_30_00;
```

## Troubleshooting

### No Sales Found
If no distributed sales are found:
- Check date range is correct
- Verify distribution was actually performed in that period
- Check if filters (product-ids, distributor-ids) are too restrictive

### Permission Errors
Ensure the script has:
- Database write permissions
- Permission to create tables (for backup)
- Laravel environment access

### Large Dataset Handling
For large datasets:
- Use smaller date ranges
- Consider running during off-peak hours
- Monitor database performance

## Recovery from Backup

If you need to restore from backup:

```sql
-- Restore sales
INSERT INTO sales SELECT * FROM sales_backup_YYYY_MM_DD_HH_MM_SS;

-- Restore sales_details  
INSERT INTO sales_details SELECT * FROM sales_details_backup_YYYY_MM_DD_HH_MM_SS;

-- Clean up backup tables when no longer needed
DROP TABLE sales_backup_YYYY_MM_DD_HH_MM_SS;
DROP TABLE sales_details_backup_YYYY_MM_DD_HH_MM_SS;
```

## Logging

All rollback operations are logged to:
- Laravel application logs
- Console output with timestamps

Check logs for detailed operation history:
```bash
tail -f storage/logs/laravel.log | grep "Distribution rollback"
```

## Best Practices

1. **Always use dry-run first** to understand impact
2. **Create backups** for important data
3. **Use specific filters** when possible to limit scope
4. **Run during maintenance windows** for large operations
5. **Verify results** after rollback completion
6. **Document rollback reasons** for audit trail

## Support

For issues or questions:
1. Check Laravel logs for error details
2. Verify database connectivity and permissions
3. Ensure all required Laravel dependencies are available
4. Contact system administrator if problems persist
